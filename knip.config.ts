import type { KnipConfig } from 'knip';

const config: KnipConfig = {
  entry: [
    'src/index.tsx'
  ],
  project: ['src/**/*.{ts,tsx,js,jsx}'],
  ignore: [
    // 忽略构建相关文件
    'build/**',
    'dist/**',
    'public/**',
    // 忽略配置文件
    '*.config.*',
    // 忽略测试文件
    '**/*.test.*',
    '**/*.spec.*',
    // 忽略 story 文件 
    '**/*.stories.*',
    // 忽略类型定义
    '**/*.d.ts',
    // 忽略开发时依赖
    'src/wdyr.*'
  ]
};

export default config;