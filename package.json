{"name": "pdf-flow-space", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ant-design/icons": "^5.5.2", "@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@atlaskit/pragmatic-drag-and-drop-auto-scroll": "^2.1.1", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.3", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@dagrejs/dagre": "^1.1.4", "@excalidraw/excalidraw": "^0.18.0", "@hookform/resolvers": "^5.2.1", "@lexical/code": "^0.33.1", "@lexical/file": "^0.33.1", "@lexical/hashtag": "^0.33.1", "@lexical/link": "^0.33.1", "@lexical/list": "^0.33.1", "@lexical/mark": "^0.33.1", "@lexical/markdown": "^0.33.1", "@lexical/overflow": "^0.33.1", "@lexical/react": "^0.33.1", "@lexical/rich-text": "^0.33.1", "@lexical/selection": "^0.33.1", "@lexical/table": "^0.33.1", "@lexical/utils": "^0.33.1", "@lexical/yjs": "^0.33.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/line-clamp": "^0.4.4", "@types/react-syntax-highlighter": "^15.5.13", "@xyflow/react": "^12.4.2", "ai": "^4.3.16", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "antd": "^5.22.7", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "fuse.js": "^7.1.0", "gh-pages": "^6.3.0", "html-to-image": "^1.11.13", "immer": "^10.1.1", "katex": "^0.16.21", "less": "^4.4.0", "lexical": "^0.33.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.538.0", "mark.js": "^8.11.1", "motion": "^12.23.11", "nanoid": "^5.0.9", "pdfjs-dist": "4.4.168", "prettier": "^3.6.2", "prismjs": "^1.30.0", "react": "^18.3.1", "react-arborist": "^3.4.3", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.62.0", "react-markdown": "^9.0.3", "react-pdf-highlighter": "8.0.0-rc.0", "react-remark": "^2.1.0", "react-resizable": "^3.0.5", "react-rnd": "^10.4.14", "react-router-dom": "6", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "rxdb": "^16.8.0", "rxjs": "^7.8.2", "sonner": "^2.0.6", "styled-components": "^6.1.13", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "vditor": "^3.11.0", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zod": "^4.0.15", "zustand": "^5.0.2"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "deploy": "gh-pages -d build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.17.15", "@types/lodash-es": "^4.17.12", "@types/mark.js": "^8.11.12", "@types/node": "^20.0.0", "@types/react": "^19.0.6", "@types/react-color": "^3.0.13", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^19.0.3", "@types/react-resizable": "^3.0.8", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "knip": "^5.62.0", "postcss": "^8.4.35", "sass": "^1.85.1", "sass-embedded": "^1.85.1", "typescript": "^5.7.2", "vite": "^5.1.0", "vite-plugin-svgr": "^4.2.0"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}