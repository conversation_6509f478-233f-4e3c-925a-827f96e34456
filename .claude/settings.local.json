{"permissions": {"allow": ["Bash(rm:*)", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__ide__getDiagnostics", "Bash(pnpm run:*)", "Bash(grep:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "WebFetch(domain:lexical.dev)", "mcp__figma-dev-mode-mcp-server__get_variable_defs", "mcp__figma-dev-mode-mcp-server__get_code", "mcp__figma-dev-mode-mcp-server__get_image", "Bash(pnpm add:*)", "Bash(pnpm build:*)", "Bash(find:*)", "Bash(npm ls:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "WebFetch(domain:github.com)", "Bash(pnpm tsc:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run:*)", "Bash(node:*)", "Bash(timeout 10 npm run build:*)", "WebSearch", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(claude doctor)", "<PERSON><PERSON>(python3:*)", "mcp__console-ninja__runtime-logs-and-errors", "mcp__console-ninja__runtime-errors", "mcp__console-ninja__runtime-logs", "mcp__console-ninja__runtime-logs-by-location", "WebFetch(domain:www.figma.com)", "Bash(npx:*)", "<PERSON><PERSON>(env)", "mcp__<PERSON><PERSON><PERSON>_Figma_MCP__get_figma_data"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/Users/<USER>/.cursor", "/Users/<USER>/.console-ninja", "/Users/<USER>"]}, "outputStyle": "Explanatory"}