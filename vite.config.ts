import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import path from 'path';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

export default defineConfig({
    // 为GitHub Pages设置固定的base路径
    // base: '/pdf-build/',
    plugins: [
        react(),
        svgr({
            svgrOptions: {
                icon: true,
            },
        }),
        {
            name: 'block-dot-git',
            configureServer(server) {
                server.middlewares.use((req, res, next) => {
                    if (req.url && req.url.startsWith('/.git')) {
                        res.statusCode = 403;
                        res.end('Forbidden');
                        return;
                    }
                    if (req.url && req.url.startsWith('/ai-chat')) {
                        // 设置 SSE 响应头
                        res.writeHead(200, {
                            'Content-Type': 'text/event-stream',
                            'Cache-Control': 'no-cache',
                            'Connection': 'keep-alive',
                            'Access-Control-Allow-Origin': '*',
                            'Access-Control-Allow-Headers': 'Cache-Control'
                        });

                        // 发送连接建立消息
                        res.write('data: 开始回到问题中...\n\n');

                        // 模拟流式数据发送
                        const messages = [
                            '你好！我是AI助手，',
                            '很高兴为您服务。',
                            '我可以帮助您解答各种问题，',
                            '包括编程、写作、分析等。',
                            '请告诉我您需要什么帮助？'
                        ];

                        let index = 0;
                        const interval = setInterval(() => {
                            if (index < messages.length) {
                                // const data = {
                                //     type: 'message',
                                //     content: messages[index],
                                //     index: index,
                                //     total: messages.length
                                // };
                                res.write(`data: ${JSON.stringify(messages[index])}\n\n`);
                                index++;
                            } else {
                                // 发送完成消息
                                res.write('data:\n\n');
                                clearInterval(interval);
                                res.end();
                            }
                        }, 500); // 每500ms发送一条消息

                        // 处理客户端断开连接
                        req.on('close', () => {
                            clearInterval(interval);
                            console.log('Client disconnected from SSE');
                        });
                        
                        // 重要：处理完 SSE 请求后直接返回，不调用 next()
                        return;
                    }
                    next();
                });
            },
        },
    ],
    css: {
        postcss: {
            plugins: [tailwindcss, autoprefixer],
        },
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
    server: {
        // host: process.env.HOST || '0.0.0.0', // 注释掉host配置，默认只监听localhost
        port: process.env.PORT ? parseInt(process.env.PORT) : undefined, // 通过环境变量动态设置端口
        open: true,
        proxy: {
            // 代理API请求到外部服务，解决CORS问题
            '/api/chat': {
                target: 'https://api.chatanywhere.tech',
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api\/chat/, '/v1/chat/completions'),
                configure: (proxy, options) => {
                    proxy.on('proxyReq', (proxyReq, req, res) => {
                        console.log('🔀 代理请求:', req.method, req.url, '-> target:', options.target)
                        // 设置流式请求头
                        proxyReq.setHeader('Accept', 'text/event-stream')
                        proxyReq.setHeader('Cache-Control', 'no-cache')
                    })
                    proxy.on('proxyRes', (proxyRes, req, res) => {
                        console.log('✅ 代理响应:', proxyRes.statusCode, req.url)
                        // 设置流式响应头
                        res.setHeader('Content-Type', 'text/event-stream')
                        res.setHeader('Cache-Control', 'no-cache')
                        res.setHeader('Connection', 'keep-alive')
                    })
                }
            },
        },
    },
    build: {
        outDir: 'dist',
        sourcemap: true,
    },
    define: {
        APP_VERSION: JSON.stringify(process.env.npm_package_version),
    },
}); 