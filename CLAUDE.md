# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 通用开发规则

- 相关的库或工具，始终查找 Context7 的文档寻找最佳实践的方案，以最小成本实现基础功能
- 优先使用现有技术栈：React 18.3、TypeScript 5.7、Tailwind CSS 3.4、Shadcn UI、Lucide React
- 遵循项目现有的架构模式和代码组织方式

## 项目技术栈概览

### 核心技术

- **框架**: React 18.3 + TypeScript 5.7 + Vite 5.1
- **样式**: Tailwind CSS 3.4 + CSS Variables + Less
- **UI库**: Shadcn UI (对话框、下拉菜单等)
- **图标**: Lucide React (主要) + Ant Design Icons (辅助)
- **状态管理**: Zustand
- **拖拽**: Atlaskit Pragmatic Drag and Drop + React DnD
- **文档编辑**: Lexical
- **PDF处理**: pdfjs-dist + react-pdf-highlighter
- **数据存储**: RxDB + Dexie (IndexedDB)

## 数据存储机制

### 1. 存储架构

#### 数据库层次 (src/local/db/)

```
本地数据存储:
├── RxDB (反应式数据库)          # 主数据库框架
├── Dexie Storage               # IndexedDB 存储引擎  
├── AJV 验证器                  # 数据验证
└── 自动时间戳                  # create_at/update_at

数据集合:
├── workspaces                  # 工作区
├── categories                  # 分类
├── notes                      # 笔记
├── nodes                      # 节点
├── edges                      # 连线
├── attachments                # 附件
├── marks                      # 标记
├── sessions                   # 会话
├── rounds                     # 轮次
├── tagGroups/tags/nodeTags    # 标签系统
├── canvases                   # 画布
├── notebooks                  # 笔记本
└── folders                    # 文件夹
```

#### 状态管理层 (src/store/)

```
Zustand Store:
├── pdf-store.ts              # PDF 相关状态 (高亮、标签页、窗口)
├── workflow-store.ts         # 工作流状态
├── flow-store.ts            # 流程图状态
├── note-store.ts            # 笔记状态
├── resource-store.tsx       # 资源状态
├── home-store.ts           # 首页状态
└── workerspace-store/      # 工作区状态集合
    ├── store.ts            # 工作区主状态
    ├── flow-store.ts       # 工作区流程状态
    ├── chat-store.ts       # 对话状态
    ├── edit-node-store.ts  # 节点编辑状态
    └── highlighter-store.ts # 高亮器状态
```

### 2. 存储策略

#### 数据持久化
- **本地优先**: 所有数据首先存储在本地 IndexedDB
- **自动同步**: 使用 RxDB 的反应式特性实现状态同步
- **离线可用**: 支持完全离线操作

#### 状态管理
- **内存状态**: Zustand 管理运行时状态
- **持久化状态**: RxDB 管理持久化数据
- **混合架构**: 临时状态(内存) + 业务数据(数据库)

### 3. 使用规范

#### 数据操作
```tsx
// ✅ 使用 RxDB 操作持久化数据
import { getDatabase } from '@/local/db'
const db = await getDatabase()
await db.notes.insert({...})

// ✅ 使用 Zustand 管理 UI 状态
import { usePdfStore } from '@/store/pdf-store'
const { setMode, mode } = usePdfStore()
```

#### 数据验证
- 所有数据模式定义在 `src/local/db/schemas/`
- 使用 AJV 自动验证数据完整性
- 预插入/预保存钩子确保数据一致性

#### 性能优化
- RxDB 查询使用索引优化
- Zustand 使用 shallow 比较避免不必要重渲染
- 大数据集使用分页和虚拟化

## 前端样式统一规范

### 1. 颜色体系 (CSS Variables)

#### 统一颜色系统 (已完成迁移)

```css
/* Tailwind 设计令牌 - 已映射业务颜色 */
--background: #f1f2ff; /* 主背景 (原 --bg-primary) */
--foreground: #3d56ba; /* 主文字 (原 --text-primary) */
--primary: #3d56ba; /* 主色调 (原 --text-primary) */
--secondary: #f5f5f5; /* 次要背景 (原 --bg-secondary) */
--secondary-foreground: #5c5c5c; /* 表单标签 (原 --text-form-label) */
--muted: #e0e2f4; /* 静音背景 (原 --bg-category-tabs) */
--muted-foreground: #999999; /* 禁用文字 (原 --text-disabled) */
--accent: #cdd2e9; /* 强调色 (原 --bg-category-tabs-hover) */
--border: #a5b0dd; /* 边框色 (原 --border-color) */
--input: #a5b0dd; /* 输入框边框 */
```

#### 业务颜色变量迁移映射表

| 原业务变量                      | 迁移到 Tailwind 类          | 使用示例                                        |
| ------------------------------- | --------------------------- | ----------------------------------------------- |
| `var(--text-primary)`           | `text-foreground`           | `<div className="text-foreground">`             |
| `var(--text-disabled)`          | `text-muted-foreground`     | `<div className="text-muted-foreground">`       |
| `var(--text-form-label)`        | `text-secondary-foreground` | `<label className="text-secondary-foreground">` |
| `var(--bg-primary)`             | `bg-background`             | `<div className="bg-background">`               |
| `var(--bg-secondary)`           | `bg-secondary`              | `<div className="bg-secondary">`                |
| `var(--bg-category-tabs)`       | `bg-muted`                  | `<div className="bg-muted">`                    |
| `var(--bg-category-tabs-hover)` | `bg-accent`                 | `<div className="hover:bg-accent">`             |
| `var(--border-color)`           | `border-border`             | `<div className="border border-border">`        |

#### 颜色使用优先级 (已更新)

1. **唯一标准**: Tailwind 设计令牌 (`bg-background`, `text-foreground`, `border-border`)
2. **避免**: 原业务 CSS Variables (已移除)
3. **避免**: 硬编码颜色值

### 2. 图标使用规范

#### 图标库优先级

1. **主要**: Lucide React - 现代、一致的线性图标
2. **辅助**: Ant Design Icons - 特定业务场景
3. **自定义**: assets/icons/ 目录的SVG图标

#### 图标使用示例

```tsx
// ✅ 推荐：Lucide React
import { Search, Plus, Settings } from "lucide-react";

// ✅ 可用：Ant Design (特定场景)
import { FileTextOutlined } from "@ant-design/icons";

// ❌ 避免：直接引入大量不同图标库
```

### 3. 基础 UI 组件体系

#### 现有组件库 (src/components/ui/)

```
基础交互组件:
├── button.tsx          # 按钮组件
├── input.tsx           # 输入框
├── textarea.tsx        # 文本域
├── select.tsx          # 选择器
├── dialog.tsx          # 对话框
├── tooltip.tsx         # 工具提示
├── popover.tsx         # 弹出层
├── dropdown-menu.tsx   # 下拉菜单
├── context-menu.tsx    # 右键菜单

布局组件:
├── card.tsx            # 卡片
├── separator.tsx       # 分隔符
├── accordion.tsx       # 折叠面板
├── collapsible.tsx     # 折叠容器

表单组件:
├── form.tsx            # 表单
├── label.tsx           # 标签
├── toggle.tsx          # 开关
├── toggle-group.tsx    # 开关组

其他:
├── badge.tsx           # 徽章
├── command.tsx         # 命令面板
├── model-selector.tsx  # 模型选择器
```

#### 组件使用规范

1. **优先使用**: src/components/ui/ 中的基础组件
2. **组件不足时**:
   - 先检查 shadcn UI 是否有对应组件
   - 使用 Context7 查找最佳实践
   - 基于现有组件扩展
3. **一致性原则**: 新组件样式应与现有设计规范一直，包括颜色、间距、字体等仅保留原组件功能逻辑

### 4. 样式编写规范

#### 样式优先级

1. **首选**: Tailwind CSS 类
2. **次选**: CSS Variables + Tailwind
3. **避免**: 内联样式 (style 属性)
4. **避免**: styled-components (除非必要)

#### 样式迁移策略

```tsx
// ❌ 内联样式 (需要替换)
<div style={{backgroundColor: '#f1f2ff', padding: '16px'}}>

// ❌ styled-components (逐步替换)
const StyledDiv = styled.div`
  background-color: #f1f2ff;
  padding: 16px;
`;

// ✅ Tailwind CSS (推荐)
<div className="bg-primary p-4">

// ✅ CSS Variables + Tailwind (可接受)
<div className="p-4" style={{backgroundColor: 'var(--bg-primary)'}}>
```

### 5. 动画与交互

#### 动画配置 (tailwind.config.js 已配置)

```css
/* 可用的动画 */
animate-accordion-down  /* 折叠面板下拉 */
animate-accordion-up    /* 折叠面板收起 */
animate-shine          /* 光泽效果 */
```

#### 自定义滚动条

```css
/* 已配置的滚动条样式 */
.scrollbar-custom
```

# MCP Servers
## Figma Dev Mode MCP Rules
  - The Figma Dev Mode MCP Server provides an assets endpoint which can serve image and SVG assets
  - IMPORTANT: If the Figma Dev Mode MCP Server returns a localhost source for an image or an SVG, use that image or SVG source directly
  - IMPORTANT: DO NOT import/add new icon packages, all the assets should be in the Figma payload
  - IMPORTANT: do NOT use or create placeholders if a localhost source is provided

## 语言输出
使用中文输出