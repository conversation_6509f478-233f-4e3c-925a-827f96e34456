import React, {Dispatch, SetStateAction, useMemo, useState} from "react";
import {PanelPosition} from "../workspace/Panel";

interface PanelPositionContextValue {
    flowPanelPosition: PanelPosition;
    setFlowPanelPosition: Dispatch<SetStateAction<PanelPosition>>;
}

const PanelPositionContext = React.createContext<PanelPositionContextValue | null>(null);

export const PanelPositionProvider = ({children}: { children: React.ReactNode }) => {
    const [flowPanelPosition, setFlowPanelPosition] = useState<PanelPosition>({
       x: 0,
        y: 0,
        width: window.innerWidth - 64,
        height: 0,
        isSnapped: true
    })
    const values = useMemo(() => ({flowPanelPosition, setFlowPanelPosition}), [flowPanelPosition])

    return (
        <PanelPositionContext.Provider value={values}>
            {children}
        </PanelPositionContext.Provider>
    )
}

export const usePanelPosition = () => {
    const context = React.useContext(PanelPositionContext);
    if (!context) {
        throw new Error('usePanelPosition must be used within a PanelPositionProvider');
    }
    return context;
};