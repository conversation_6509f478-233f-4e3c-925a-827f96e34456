import {getOssSignatureForImage, uploadPreview} from "@/api/image";
import {ChatPanel} from "@/components/chat-panel";
import {Sidebar, SidebarEventType} from "@/components/flow/Sidebar";
import {NotePanelMain} from "@/components/note/NotePanel";
import {PdfPanelMain} from "@/components/pdf/PdfPanel.tsx";
import {TabPanelMain} from "@/components/pdf/TabPanel.tsx";
import {usePanelOpenStore} from "@/store/panel-open-store";
import {useTabPanelStore} from "@/store/tab-panel-store";
import {useFlowStore} from "@/store/flow-store";
import {useHighlighterStore} from "@/store/workerspace-store/highlighter-store";
import {useWorkerSpaceStore} from "@/store/workerspace-store/store";
import {message} from "antd";
import * as htmlToImage from 'html-to-image';
import {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {usePdfStore} from "@/store/pdf-store";
import 'react-resizable/css/styles.css';
import {useLocation} from "react-router-dom";
import styled from "styled-components";
import {PanelPosition, SnapPreview} from "./Panel";
import {ResizeDivider} from "@/components/ui/ResizeDivider.tsx";
import {TagPanel} from "@/components/tag-management";
import CardList from "@/components/card-list";
import CanvasWrapper from "@/components/canvas/canvas-wrapper";
import {LLMPanel} from "@/components/llm-panel";
import {PanelPositionProvider, usePanelPosition,} from "../provider/canvas-provider";
import {GlobalWindowManager} from "@/components/pdf/components/draggable-tabs/GlobalWindowManager.tsx";
import {ReactFlowProvider} from "@xyflow/react";
import {HTML5Backend} from "react-dnd-html5-backend";
import {DndProvider} from "react-dnd";
import {TabDragMonitor} from "@/components/pdf/components/draggable-tabs/TabDragMonitor";
import {TabPanelVisibilityProvider} from "@/components/flow/TabPanelVisibilityProvider";
import {useTabPanelVisibility} from "@/components/flow/TabPanelControls";

const OuterContainer = styled.div.withConfig({
    shouldForwardProp: (prop) => !['active'].includes(prop),
})`
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: row;
`;

const PanelContainer = styled.div.withConfig({
    shouldForwardProp: (prop) => !['active', 'onSelect', 'noteOpen', 'chatOpen', 'pdfOpen', 'tagOpen', 'cardListOpen'].includes(prop),
})`
    flex: 1;
    min-width: 800px;
    height: 100vh;
    position: relative;
    overflow: hidden;
`;


const SnapPreviewOverlay = styled.div<{
    position: { x: number; y: number; width: number; height: number };
}>`
    position: absolute;
    left: ${(props) => props.position.x}px;
    top: ${(props) => props.position.y}px;
    width: ${(props) => props.position.width}px;
    height: ${(props) => props.position.height}px;
    background-color: rgba(0, 120, 215, 0.3);
    border: 2px solid rgba(0, 120, 215, 0.8);
    border-radius: 8px;
    z-index: 999;
    pointer-events: none; // 确保蒙版不会影响鼠标事件
    transition: all 0.1s ease-out;
`;

// 1. 将截图逻辑独立出来
const captureAndUploadSnapshot = async (
    workerspaceRef: HTMLDivElement | null,
    wid: string | null,
) => {
    if (!workerspaceRef || !wid) return;

    try {
        const blob = await htmlToImage.toBlob(workerspaceRef, {
            onImageErrorHandler: (error) => {
                console.log('Error converting image:', error);
            },
        });

        if (!blob) {
            throw new Error("Failed to create image blob");
        }

        // 上传图片
        const res = await uploadPreview({
            wid,
            file: blob,
        });

        if ((res as any).code === 0) {
        }


    } catch (error) {
        console.error("截图失败:", error);
    }
};

/**
 * 创建增强版截图功能:
 * 1. 用户活动检测 - 只在用户停止操作后才执行截图，避免影响用户体验
 * 2. 浏览器空闲调度 - 使用requestIdleCallback在浏览器空闲时执行截图，降低对性能的影响
 * 3. 节流控制 - 设置最小截图时间间隔，避免过于频繁的截图
 * 4. 自动清理 - 提供完整的资源清理机制
 */
const createEnhancedCaptureAndUpload = (workerspaceRefGetter: () => HTMLDivElement | null, widGetter: () => string | null) => {
    // 用户活动状态
    let isUserActive = false;
    let userActivityTimeout: ReturnType<typeof setTimeout> | null = null;
    let lastCaptureTime = 0;
    const inactivityThreshold = 10000; // 10秒无活动视为不活跃
    const captureInterval = 20000; // 两次截图之间的最小间隔
    let idleCallbackId: number | ReturnType<typeof setTimeout> | null = null;
    let pendingCapture = false; // 同时表示是否有待处理的截图请求和是否正在进行截图
    let isIdleCallbackSupported = 'requestIdleCallback' in window;

    // 用户活动监测
    const setupUserActivityDetection = () => {
        const userActivityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];

        // 用户活动处理函数
        const handleUserActivity = () => {
            isUserActive = true;

            // 清除之前的超时
            if (userActivityTimeout) {
                clearTimeout(userActivityTimeout);
            }

            // 设置新的超时，用户停止活动后将状态设为不活跃
            userActivityTimeout = setTimeout(() => {
                isUserActive = false;
                // 如果之前有请求未执行，并且当前没有正在进行的截图，尝试执行
                if (pendingCapture) {
                    const now = Date.now();
                    const intervalElapsed = now - lastCaptureTime > captureInterval;
                    if (intervalElapsed) {
                        scheduleCaptureWhenIdle();
                    }
                }
            }, inactivityThreshold);
        };

        // 添加所有活动事件监听
        userActivityEvents.forEach(eventType => {
            window.addEventListener(eventType, handleUserActivity, {passive: true});
        });

        // 初始化时设为活跃状态
        handleUserActivity();

        // 返回移除事件监听的函数
        return () => {
            userActivityEvents.forEach(eventType => {
                window.removeEventListener(eventType, handleUserActivity);
            });
            cleanupCallbacks();
        };
    };

    // 在浏览器空闲时执行截图
    const scheduleCaptureWhenIdle = () => {
        // 如果已经有排队的请求，不再添加新的
        if (idleCallbackId !== null || pendingCapture) return;

        // 根据浏览器支持选择合适的API
        if (isIdleCallbackSupported) {
            idleCallbackId = (window as any).requestIdleCallback(
                () => {
                    idleCallbackId = null;
                    executeCapture();
                },
                {timeout: 2000} // 最多等待2秒
            );
        } else {
            // 降级方案：使用setTimeout
            idleCallbackId = setTimeout(() => {
                idleCallbackId = null;
                executeCapture();
            }, 100);
        }
    };

    // 执行截图
    const executeCapture = async () => {
        // 如果已经在执行截图，直接返回
        if (pendingCapture) {
            return;
        }

        pendingCapture = true; // 标记为正在执行截图
        const currentRef = workerspaceRefGetter();
        const currentWid = widGetter();

        if (currentRef && currentWid) {
            // 执行截图
            await captureAndUploadSnapshot(currentRef, currentWid);

            lastCaptureTime = Date.now();
        }
        pendingCapture = false;
    };

    // 清理定时器和回调
    const cleanupCallbacks = () => {
        if (idleCallbackId !== null) {
            if (isIdleCallbackSupported) {
                (window as any).cancelIdleCallback(idleCallbackId);
            } else {
                clearTimeout(idleCallbackId);
            }
            idleCallbackId = null;
        }

        if (userActivityTimeout !== null) {
            clearTimeout(userActivityTimeout);
            userActivityTimeout = null;
        }
    };

    // 初始化用户活动检测，返回清理函数
    const cleanup = setupUserActivityDetection();

    // 暴露给外部的截图函数
    return {
        captureAndUpload: (force = false) => {
            // 如果强制执行，忽略所有条件
            if (force) {
                const currentRef = workerspaceRefGetter();
                const currentWid = widGetter();
                return captureAndUploadSnapshot(currentRef, currentWid);
            }

            // 如果正在执行中，直接返回
            if (pendingCapture) {
                return;
            }

            // 检查是否达到执行条件
            const now = Date.now();
            const intervalElapsed = now - lastCaptureTime > captureInterval;
            const currentRef = workerspaceRefGetter();
            const currentWid = widGetter();
            if (!currentRef || !currentWid) return;

            // 判断是否可以立即执行
            if (!isUserActive && intervalElapsed) {
                scheduleCaptureWhenIdle();
            }
            // 如果条件不满足，等待用户变为不活跃时自动检查条件
        },
        cleanup: () => {
            cleanup();
            cleanupCallbacks();
        }
    };
};


enum panel {
    note = 'note',
    pdf = "pdf",
    chat = 'chat'
}

type panelTypes = keyof typeof panel
const SIDEBAR_WIDTH = 64
const WorkerSpaceContent = memo(() => {
    const {hiddenPanels} = useTabPanelVisibility();
    const wid = useWorkerSpaceStore(state => state.wid);
    const setWid = useWorkerSpaceStore(state => state.setWid);
    const workerspaceRef = useWorkerSpaceStore(state => state.workerspaceRef);
    const setWorkerspaceRef = useWorkerSpaceStore(state => state.setWorkerspaceRef);
    const wRef = useRef<HTMLDivElement>(null);
    const {
        notePanelOpen,
        pdfPanelOpen,
        chatPanelOpen,
        tagPanelOpen,
        cardListPanelOpen,
        llmPanelOpen,
        togglePdfPanel,
        toggleChatPanel,
        toggleNotePanel,
        toggleTagPanel,
        toggleCardListPanel,
        toggleLLMPanel,
    } = usePanelOpenStore();

    const {tabPanels, updateTabPanelPosition} = useTabPanelStore();
    const location = useLocation();
    // 添加一个 ref 来跟踪当前的 wid
    const currentWid = useRef<string | null>(null);

    // 获取签名
    useEffect(() => {
        // 获取签名并保存到localStorage
        const fetchAndSaveSignatures = async () => {
            try {
                // 获取图片签名
                const imageOssSignature = await getOssSignatureForImage();
                if ((imageOssSignature as any).code === 0) {
                    localStorage.setItem(
                        "image_oss_signature",
                        imageOssSignature.data.policy
                    );
                }

            } catch (error) {
                console.error("获取签名失败:", error);
                message.error("获取上传签名失败");
            }
        };
        fetchAndSaveSignatures();
        // 设置定时器，每小时更新一次签名
        const intervalId = setInterval(fetchAndSaveSignatures, 60 * 50 * 1000);

        return () => clearInterval(intervalId);
    }, []);

    // 启动后做一次去重，避免同一文件在多个容器中重复
    useEffect(() => {
        const s = usePdfStore.getState();
        if (!s.dragTabWindowId && !s.dragHoverWindowId) {
            s.dedupeOpenFiles?.();
        }
    }, []);

    // 获取工作区ID
    useEffect(() => {
        // 使用 URLSearchParams 更安全地获取参数
        const searchParams = new URLSearchParams(location.search);
        const newWid = searchParams.get("wid");

        // 当 wid 发生变化时执行操作（包括从有值变为 null 的情况）
        if (newWid !== currentWid.current) {
            if (newWid) {
                // 有新的 wid，加载新工作区
                // 1. 先清除旧数据
                useFlowStore.getState().clearNodes();
                useHighlighterStore.getState().clearHighlights();

                // 2. 更新 ref
                currentWid.current = newWid;

                // 3. 设置新的 wid
                setWid(newWid);

                // 将当前 workspace id 暴露到全局，供新建 TabPanel 绑定项目
                (window as any).__CURRENT_WORKSPACE_ID__ = newWid;
            } else {
                // wid 变为 null（例如用户回退到项目列表页）
                // 清理状态
                useFlowStore.getState().clearNodes();
                useHighlighterStore.getState().clearHighlights();
                setWid(null);
                currentWid.current = null;
                (window as any).__CURRENT_WORKSPACE_ID__ = null;
            }
        }

        if (wRef.current) {
            setWorkerspaceRef(wRef.current);
        }

        // 组件卸载时的清理函数
        return () => {
            // 清除所有状态
            useFlowStore.getState().clearNodes();
            useHighlighterStore.getState().clearHighlights();
            setWid(null);
            currentWid.current = null;
            if (wRef.current) {
                setWorkerspaceRef(null);
            }
            // 清理全局 wid
            (window as any).__CURRENT_WORKSPACE_ID__ = null;
        };
    }, [location.search, setWid, setWorkerspaceRef]);

    // 创建增强版截图功能
    const screenshotManager = useRef<ReturnType<typeof createEnhancedCaptureAndUpload> | null>(null);

    // 每30s截图一次，但只在用户不活跃时
    useEffect(() => {
        // 初始化截图管理器
        if (!screenshotManager.current) {
            screenshotManager.current = createEnhancedCaptureAndUpload(
                () => workerspaceRef, // 获取当前的workerspaceRef
                () => wid // 获取当前的wid
            );
        }

        // 设置定时器
        const intervalId = setInterval(() => {
            if (wid && workerspaceRef && screenshotManager.current) {
                screenshotManager.current.captureAndUpload();
            }
        }, 30000);

        return () => {
            if (screenshotManager.current) {
                screenshotManager.current.cleanup();
                screenshotManager.current = null;
            }
            // 组件卸载时的清理函数
            clearInterval(intervalId);
        };
    }, [wid, workerspaceRef]);


    // 使用 Zustand persist store 管理面板位置
    const {
        notePanelPosition,
        pdfPanelPosition,
        chatPanelPosition,
        setNotePanelPosition,
        setPdfPanelPosition,
        setChatPanelPosition,
    } = usePanelOpenStore();

    // 为每个 TabPanel 创建独立的位置状态
    const [tabPanelPositions, setTabPanelPositions] = useState<Map<string, PanelPosition>>(() => {
        // 初始化时从 tabPanels 获取位置
        const initialPositions = new Map<string, PanelPosition>();
        if (tabPanels instanceof Map) {
            tabPanels.forEach((panel, id) => {
                if (!panel.projectId || panel.projectId === wid) {
                    initialPositions.set(id, panel.position);
                }
            });
        }
        return initialPositions;
    });

    // 只在 TabPanel 添加或删除时同步
    useEffect(() => {
        if (tabPanels instanceof Map) {
            setTabPanelPositions(prev => {
                const newMap = new Map(prev);
                // 添加新的 panels
                tabPanels.forEach((panel, id) => {
                    if ((!panel.projectId || panel.projectId === wid) && !newMap.has(id)) {
                        newMap.set(id, panel.position);
                    }
                });
                // 删除不存在的 panels
                Array.from(newMap.keys()).forEach(id => {
                    if (!tabPanels.has(id)) {
                        newMap.delete(id);
                    }
                });
                return newMap;
            });
        }
    }, [tabPanels, wid]);

    const {setFlowPanelPosition} = usePanelPosition()
    const [dragPanel, setDragPanel] = useState("")
    // TabPanel 层级管理：使用受控计数器避免时间戳导致的超大 zIndex
    const TAB_Z_BASE = 1000;
    const TAB_Z_MAX = 1900;
    const [tabZTop, setTabZTop] = useState(TAB_Z_BASE);
    // 添加贴边预览状态
    const [snapPreview] = useState<SnapPreview>({
        visible: false,
        edge: null,
        previewPosition: {x: 0, y: 0, width: 0, height: 0},
        snapPosition: {x: 0, y: 0, width: 0, height: 0},
        opacity: 1,
    });
    // 窗口大小变化时更新位置
    useEffect(() => {
        const handleResize = () => {
            // 更新右侧面板的位置
            if (!pdfPanelPosition.isSnapped) {
                setPdfPanelPosition((prev) => ({
                    ...prev,
                    x: window.innerWidth - 420,
                }));

            }
            if (!chatPanelPosition.isSnapped) {
                setChatPanelPosition((prev) => ({
                    ...prev,
                    x: window.innerWidth - 420,
                    y: window.innerHeight - 520,
                }));
            }
        };

        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [pdfPanelPosition.isSnapped, chatPanelPosition.isSnapped]);

    // 添加z-index状态管理
    const [zIndexes, setZIndexes] = useState({
        note: 10,
        pdf: 10,
        chat: 10
    });

    // 提升面板层级（与 TabPanel 共享同一受控区间，确保跨类型点击可置顶）
    const bringToFront = (panelType: panelTypes) => {
        setTabZTop((currentTop) => {
            const nextZ = currentTop >= TAB_Z_MAX ? TAB_Z_BASE : currentTop + 1;
            setZIndexes(prev => ({
                ...prev,
                [panelType]: nextZ,
            }));
            return nextZ;
        });
    };

    // 修改点击处理函数，使面板在点击时提升到最上层
    const handlePanelClick = (type: panelTypes) => {
        bringToFront(type)
    }

    // 添加监听面板打开状态的useEffect
    useEffect(() => {
        // 当笔记面板打开时，提高其z-index
        if (notePanelOpen) {
            bringToFront(panel.note);
        }
    }, [notePanelOpen]);

    useEffect(() => {
        // 当PDF面板打开时，提高其z-index
        if (pdfPanelOpen) {
            bringToFront(panel.pdf);
        }
    }, [pdfPanelOpen]);

    useEffect(() => {
        // 当聊天面板打开时，提高其z-index
        if (chatPanelOpen) {
            bringToFront(panel.chat);
        }
    }, [chatPanelOpen]);

    // 添加导航到home的函数
    const navigateToHome = useCallback(() => {
        // 如果记事本面板打开，先处理可能的未保存内容
        if (notePanelOpen) {
            // 触发记事本关闭事件，让记事本组件处理保存逻辑
            // 标记这是home导航

            // 添加一次性事件监听器，在记事本关闭后再导航到home
            const handleNotepadClose = () => {
                window.removeEventListener('notepad-close', handleNotepadClose);
                window.location.href = "/"; // 使用直接的URL导航确保在处理完后跳转
            };

            window.addEventListener('notepad-close', handleNotepadClose);

            // 触发记事本的关闭事件，这将启动保存检查流程
            const closeEvent = new CustomEvent('notepad-close');
            window.dispatchEvent(closeEvent);
        } else {
            // 如果记事本没有打开，直接导航
            window.location.href = "/";
        }
    }, [notePanelOpen]);

    // 添加事件监听（恢复原始的事件监听）
    useEffect(() => {
        const handleNotepadClose = () => {
            toggleNotePanel(false);
        };

        window.addEventListener('notepad-close', handleNotepadClose);
        return () => {
            window.removeEventListener('notepad-close', handleNotepadClose);
        };
    }, [toggleNotePanel]);

    // 新增：当前激活的面板
    const [activePanel, setActivePanel] = useState<SidebarEventType>('home');

    // 侧边栏切换逻辑
    const handleSidebarSelect = useCallback((type: SidebarEventType) => {
            setActivePanel(type);
            if (type === 'home') {
                navigateToHome();
                return;
            }
            if (type === "note") toggleNotePanel(!notePanelOpen);
            if (type === "chat") toggleChatPanel(!chatPanelOpen);
            if (type === "pdf") togglePdfPanel(!pdfPanelOpen);
            if (type === "tag") toggleTagPanel(!tagPanelOpen);
            if (type === "card-list") toggleCardListPanel(!cardListPanelOpen);
        },
        [
            navigateToHome,
            toggleNotePanel,
            toggleChatPanel,
            togglePdfPanel,
            toggleTagPanel,
            toggleCardListPanel,
            notePanelOpen,
            chatPanelOpen,
            pdfPanelOpen,
            tagPanelOpen,
            cardListPanelOpen,
        ]
    );

    const panels = useMemo(() => {
        const basePanels = [
            {
                x: pdfPanelPosition.x,
                y: pdfPanelPosition.y,
                width: pdfPanelPosition.width,
                height: pdfPanelPosition.height,
                isOpen: pdfPanelOpen,
                type: 'pdf'
            },
            {
                x: chatPanelPosition.x,
                y: chatPanelPosition.y,
                width: chatPanelPosition.width,
                height: chatPanelPosition.height,
                isOpen: chatPanelOpen,
                type: 'chat'
            },
            {
                x: notePanelPosition.x,
                y: notePanelPosition.y,
                width: notePanelPosition.width,
                height: notePanelPosition.height,
                isOpen: notePanelOpen,
                type: 'note'
            },
        ];

        // 添加 TabPanel 位置（排除已隐藏的 TabPanel）
        tabPanelPositions.forEach((position, id) => {
            if (!hiddenPanels.has(id)) {
                basePanels.push({
                    x: position.x,
                    y: position.y,
                    width: position.width,
                    height: position.height,
                    isOpen: true,
                    type: 'tab',
                    id
                } as any);
            }
        });

        return basePanels;
    }, [
        pdfPanelPosition.x,
        pdfPanelPosition.y,
        pdfPanelPosition.width,
        pdfPanelPosition.height,
        pdfPanelOpen,
        chatPanelPosition.x,
        chatPanelPosition.y,
        chatPanelPosition.width,
        chatPanelPosition.height,
        chatPanelOpen,
        notePanelPosition.x,
        notePanelPosition.y,
        notePanelPosition.width,
        notePanelPosition.height,
        notePanelOpen,
        tabPanelPositions,
        hiddenPanels
    ])

    // 为每个 TabPanel 创建 setter
    const tabPanelSetters = useMemo(() => {
        const setters: Record<string, (position: any) => void> = {};
        tabPanelPositions.forEach((_, id) => {
            setters[`tab-${id}`] = (positionOrUpdater) => {
                setTabPanelPositions(prev => {
                    const newMap = new Map(prev);
                    const current = newMap.get(id);
                    if (current) {
                        const next = typeof positionOrUpdater === 'function'
                            ? positionOrUpdater(current)
                            : positionOrUpdater;
                        newMap.set(id, next);
                    }
                    return newMap;
                });
            };
        });
        return setters;
    }, [tabPanelPositions]);

    const setPanelsPosition = useMemo(() => ({
        [panel.pdf]: setPdfPanelPosition,
        [panel.note]: setNotePanelPosition,
        [panel.chat]: setChatPanelPosition,
        "flow": setFlowPanelPosition,
        ...tabPanelSetters
    }), [setPdfPanelPosition, setNotePanelPosition, setChatPanelPosition, setFlowPanelPosition, tabPanelSetters])

    // 推动动态 TabPanel：根据 panelId 平移 dx
    const bumpTabPanel = useCallback((panelId: string, dx: number) => {
        const position = tabPanelPositions.get(panelId);
        if (!position) return;

        const setter = tabPanelSetters[`tab-${panelId}`];
        if (setter) {
            setter({
                ...position,
                x: position.x + dx,
                // 保持"连接"语义：被推进的 TabPanel 仍视为贴边态
                isSnapped: true,
            });
        }
    }, [tabPanelPositions, tabPanelSetters]);
    // 获取所有贴边面板
    const getAdjacentPanels = (otherPanels: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>) => {
        // 过滤出打开的面板并按x坐标排序（不修改原数组）
        const openPanels = otherPanels
            .filter(panel => panel.isOpen)
            .sort((a, b) => a.x - b.x);


        // 为 tab 生成稳定唯一键，其它面板使用 type
        const getPanelKey = (p: any) => (p.type === 'tab' && p.id) ? `tab:${p.id}` : p.type;

        // 获取左侧连续紧贴的面板
        const getLeftAdjacentPanels = () => {
            const leftPanels = [];
            let currentX = 0; // 从左边界开始

            // 添加虚拟的左边界面板
            leftPanels.push({
                x: 0,
                y: 0,
                width: 0,
                height: window.innerHeight,
                isOpen: true,
                type: 'left'
            });

            // 检查每个面板是否与当前位置紧贴
            for (const panel of openPanels) {
                if (panel.x === currentX) {
                    leftPanels.push(panel);
                    currentX = panel.x + panel.width;
                } else {
                    break; // 一旦发现不连续的面板，就停止
                }
            }

            return leftPanels;
        };

        // 获取右侧连续紧贴的面板
        const getRightAdjacentPanels = (usedPanelKeys: Set<string>) => {
            const rightPanels = [];
            let currentX = window.innerWidth - SIDEBAR_WIDTH; // 从右边界开始

            // 添加虚拟的右边界面板
            rightPanels.push({
                x: window.innerWidth - SIDEBAR_WIDTH,
                y: 0,
                width: 0,
                height: window.innerHeight,
                isOpen: true,
                type: 'right'
            });

            // 从右往左检查每个面板是否紧贴（排除已被左侧使用的面板）
            for (let i = openPanels.length - 1; i >= 0; i--) {
                const panel = openPanels[i];

                // 跳过已被左侧使用的面板（按唯一键判断）
                if (usedPanelKeys.has(getPanelKey(panel))) {
                    continue;
                }

                if (panel.x + panel.width === currentX) {
                    rightPanels.unshift(panel);
                    currentX = panel.x;
                } else if (panel.x + panel.width < currentX) {
                    break;
                }
            }

            return rightPanels;
        };

        const leftAdjacentPanels = getLeftAdjacentPanels();

        // 创建已使用面板的集合（排除虚拟边界面板）
        const usedPanelKeys = new Set(
            leftAdjacentPanels
                .filter(panel => panel.type !== 'left')
                .map(panel => getPanelKey(panel))
        );

        const rightAdjacentPanels = getRightAdjacentPanels(usedPanelKeys);

        // console.log('leftAdjacentPanels', leftAdjacentPanels);
        // console.log('rightAdjacentPanels', rightAdjacentPanels);
        // console.log('usedPanelTypes', usedPanelTypes);

        return {leftAdjacentPanels, rightAdjacentPanels}
    }

    // 生成分割线数据
    const generateDividers = () => {
        const dividers: Array<{
            id: string;
            x: number;
            y: number;
            height: number;
            leftPanel: any;
            rightPanel: any;
            setLeftPosition: any;
            setRightPosition: any;
        }> = [];

        // 映射面板到位置状态的公共函数
        const mapPanelToPosition = (p: any) => {
            if (p.type === 'note') return notePanelPosition;
            if (p.type === 'pdf') return pdfPanelPosition;
            if (p.type === 'chat') return chatPanelPosition;
            if (p.type === 'tab' && p.id) {
                return (
                    tabPanelPositions.get(p.id) ||
                    {x: p.x, y: p.y, width: p.width, height: p.height, isSnapped: true}
                ) as any;
            }
            return undefined;
        };

        // 映射面板到setter函数的公共函数
        const mapPanelToSetter = (p: any) => {
            if (p.type === 'note') return setNotePanelPosition;
            if (p.type === 'pdf') return setPdfPanelPosition;
            if (p.type === 'chat') return setChatPanelPosition;
            if (p.type === 'tab' && p.id) {
                return tabPanelSetters[`tab-${p.id}`];
            }
            return undefined;
        };

        // panels 数组已经包含了 TabPanel，不需要再添加
        const allPanels = panels;

        // 拖拽中时，排除正在拖拽的那个面板（支持 tab-<id>）
        const filteredPanels = allPanels.filter(panel => {
            if (!dragPanel) return true;
            if (dragPanel.startsWith('tab-')) {
                const targetId = dragPanel.slice(4);
                return !(panel.type === 'tab' && (panel as any).id === targetId);
            }
            return panel.type !== dragPanel;
        });

        const {
            leftAdjacentPanels,
            rightAdjacentPanels
        } = getAdjacentPanels(filteredPanels);
        // 为左侧连续面板之间添加分割线（跳过虚拟左边界面板）
        for (let i = 1; i < leftAdjacentPanels.length - 1; i++) {
            const leftPanel = leftAdjacentPanels[i];
            const rightPanel = leftAdjacentPanels[i + 1];

            // 跳过虚拟边界面板
            if (leftPanel.type === 'left' || rightPanel.type === 'left') continue;

            const dividerX = leftPanel.x + leftPanel.width;

            const lp = mapPanelToPosition(leftPanel);
            const rp = mapPanelToPosition(rightPanel);
            const lps = mapPanelToSetter(leftPanel);
            const rps = mapPanelToSetter(rightPanel);
            if (!lp || !rp || !lps || !rps) continue;

            const keyOf = (p: any) => `${p.type}${(p as any).id ? `-${(p as any).id}` : ''}`;
            dividers.push({
                id: `${keyOf(leftPanel)}|${keyOf(rightPanel)}@${dividerX}`,
                x: dividerX,
                y: 0,
                height: window.innerHeight,
                leftPanel: lp,
                rightPanel: rp,
                setLeftPosition: lps,
                setRightPosition: rps,
            });
        }

        // 为右侧连续面板之间添加分割线（跳过虚拟右边界面板）
        for (let i = 0; i < rightAdjacentPanels.length - 1; i++) {
            const leftPanel = rightAdjacentPanels[i];
            const rightPanel = rightAdjacentPanels[i + 1];

            // 跳过虚拟边界面板
            if (leftPanel.type === 'right' || rightPanel.type === 'right') continue;

            const dividerX = leftPanel.x + leftPanel.width;

            const lp = mapPanelToPosition(leftPanel);
            const rp = mapPanelToPosition(rightPanel);
            const lps = mapPanelToSetter(leftPanel);
            const rps = mapPanelToSetter(rightPanel);
            if (!lp || !rp || !lps || !rps) continue;

            const keyOf = (p: any) => `${p.type}${(p as any).id ? `-${(p as any).id}` : ''}`;
            dividers.push({
                id: `${keyOf(leftPanel)}|${keyOf(rightPanel)}@${dividerX}`,
                x: dividerX,
                y: 0,
                height: window.innerHeight,
                leftPanel: lp,
                rightPanel: rp,
                setLeftPosition: lps,
                setRightPosition: rps,
            });
        }

        return dividers;
    };


    useEffect(() => {
        // 过滤掉正在拖拽的面板 - 需要正确处理 TabPanel
        const allPanelsForFlow = panels.filter(panel => {
            if (dragPanel.startsWith('tab-')) {
                // 如果拖拽的是 TabPanel，检查 id
                const dragId = dragPanel.slice(4);
                return !(panel.type === 'tab' && (panel as any).id === dragId);
            }
            return panel.type !== dragPanel;
        });

        const {
            leftAdjacentPanels,
            rightAdjacentPanels
        } = getAdjacentPanels(allPanelsForFlow)
        const leftX = leftAdjacentPanels.length > 0 ? leftAdjacentPanels[leftAdjacentPanels.length - 1].x + leftAdjacentPanels[leftAdjacentPanels.length - 1].width : 0
        const rightX = rightAdjacentPanels.length > 0 ? rightAdjacentPanels[0].x : window.innerWidth - SIDEBAR_WIDTH

        // flow位置
        setFlowPanelPosition(prev => {
            // 只在真正变化时更新
            if (prev.x !== leftX || prev.width !== rightX - leftX) {
                return {
                    ...prev,
                    x: leftX,
                    width: rightX - leftX
                }
            }
            return prev;
        });
        // 边 - 只在需要时更新
        if (leftAdjacentPanels.length > 1) {
            for (let i = 1; i <= leftAdjacentPanels.length - 1; i++) {
                const panel = leftAdjacentPanels[i];
                const panelKey = panel.type === 'tab' && (panel as any).id
                    ? `tab-${(panel as any).id}`
                    : panel.type as keyof typeof setPanelsPosition;
                const fn = (setPanelsPosition as any)[panelKey];
                if (typeof fn !== 'function') continue;

                const targetHandles = i === leftAdjacentPanels.length - 1
                    ? ['n', 's', 'e', 'ne', 'se']
                    : ['n', 's'];

                fn((prev: PanelPosition) => {
                    // 只在边缘处理器真正改变时更新
                    const currentHandles = prev.resizeHandles || [];
                    const needsUpdate = targetHandles.length !== currentHandles.length ||
                        !targetHandles.every(h => currentHandles.includes(h as any));

                    if (needsUpdate) {
                        return {
                            ...prev,
                            resizeHandles: targetHandles,
                        };
                    }
                    return prev;
                });
            }
        }
        if (rightAdjacentPanels.length > 1) {
            for (let i = 0; i <= rightAdjacentPanels.length - 2; i++) {
                const panel = rightAdjacentPanels[i];
                const panelKey = panel.type === 'tab' && (panel as any).id
                    ? `tab-${(panel as any).id}`
                    : panel.type as keyof typeof setPanelsPosition;
                const fn = (setPanelsPosition as any)[panelKey];
                if (typeof fn !== 'function') continue;

                const targetHandles = i === 0
                    ? ['n', 's', 'w', 'nw', 'sw']
                    : ['n', 's'];

                fn((prev: PanelPosition) => {
                    // 只在边缘处理器真正改变时更新
                    const currentHandles = prev.resizeHandles || [];
                    const needsUpdate = targetHandles.length !== currentHandles.length ||
                        !targetHandles.every(h => currentHandles.includes(h as any));

                    if (needsUpdate) {
                        return {
                            ...prev,
                            resizeHandles: targetHandles,
                        };
                    }
                    return prev;
                });
            }
        }

        const leftPanelKeys = new Set(leftAdjacentPanels.map(panel =>
            panel.type === 'tab' && (panel as any).id
                ? `tab-${(panel as any).id}`
                : panel.type
        ));
        const rightPanelKeys = new Set(rightAdjacentPanels.map(panel =>
            panel.type === 'tab' && (panel as any).id
                ? `tab-${(panel as any).id}`
                : panel.type
        ));

        // 获取所有独立的面板 - panels 已经包含了 TabPanel
        const otherPanels = panels.filter(panel => {
            if (!panel.isOpen) return false;

            const panelKey = panel.type === 'tab' && (panel as any).id
                ? `tab-${(panel as any).id}`
                : panel.type;

            // 排除正在拖拽的面板
            if (dragPanel === panelKey) return false;

            // 排除已经在左右两侧的面板
            return !leftPanelKeys.has(panelKey) && !rightPanelKeys.has(panelKey);
        });
        // 恢复边
        for (const panel of otherPanels) {
            const panelKey = panel.type === 'tab' && (panel as any).id
                ? `tab-${(panel as any).id}`
                : panel.type as keyof typeof setPanelsPosition;
            const fn = (setPanelsPosition as any)[panelKey];
            if (typeof fn !== 'function') continue;

            const targetHandles = ['n', 's', 'w', 'e', 'nw', 'ne', 'sw', 'se'];
            fn((prev: PanelPosition) => {
                // 只在边缘处理器真正改变时更新
                const currentHandles = prev.resizeHandles || [];
                const needsUpdate = targetHandles.length !== currentHandles.length ||
                    !targetHandles.every(h => currentHandles.includes(h as any));

                if (needsUpdate) {
                    return {
                        ...prev,
                        resizeHandles: targetHandles,
                    };
                }
                return prev;
            });
        }
    }, [panels, dragPanel, tabPanelPositions, hiddenPanels]);
    return (
        <DndProvider backend={HTML5Backend}>
            <OuterContainer>
                <Sidebar active={activePanel}
                         onSelect={handleSidebarSelect}
                         noteOpen={notePanelOpen}
                         chatOpen={chatPanelOpen}
                         pdfOpen={pdfPanelOpen}
                         tagOpen={tagPanelOpen}
                         cardListOpen={cardListPanelOpen}
                />
                <PanelContainer ref={wRef}>
                    <CanvasWrapper/>
                    {/* 贴边预览蒙版 */}
                    {snapPreview.visible && (
                        <SnapPreviewOverlay position={snapPreview.previewPosition}/>
                    )}

                    {/* 笔记面板 */}
                    {notePanelOpen && (
                        <NotePanelMain
                            handlePanelClick={() => handlePanelClick(panel.note)}
                            zIndex={zIndexes.note}
                            panelPosition={{
                                ...notePanelPosition
                            }}
                            setPanelPosition={setNotePanelPosition}
                            setPanelsPosition={setPanelsPosition}
                            setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? 'note' : "")}
                            getAdjacentPanels={getAdjacentPanels}
                            onBumpTabPanel={bumpTabPanel}
                            otherPanels={[
                                ...panels.filter((p: any) => p.type !== 'note' && p.isOpen)
                            ]}/>
                    )}

                    {/* PDF面板 */}
                    {(pdfPanelOpen && !tagPanelOpen) && (
                        <PdfPanelMain
                            handlePanelClick={() => handlePanelClick(panel.pdf)}
                            zIndex={zIndexes.pdf}
                            panelPosition={{
                                ...pdfPanelPosition
                            }}
                            setPanelPosition={setPdfPanelPosition}
                            setPanelsPosition={setPanelsPosition}
                            setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? 'pdf' : "")}
                            getAdjacentPanels={getAdjacentPanels}
                            onBumpTabPanel={bumpTabPanel}
                            otherPanels={[
                                ...panels.filter((p: any) => p.type !== 'pdf' && p.isOpen)
                            ]}/>
                    )}

                    {/* 动态Tab面板（按项目过滤） */}
                    {tabPanels && tabPanels instanceof Map && Array.from(tabPanels.values())
                        .filter(p => !p.projectId || p.projectId === wid)
                        .map(panel => {
                            // 检查是否隐藏
                            const isHidden = hiddenPanels.has(panel.id);
                            if (isHidden) return null; // 隐藏的 Panel 不渲染

                            return (
                                <TabPanelMain
                                    key={panel.id}
                                    tabId={panel.id}
                                    tabLabel={panel.label}
                                    aid={panel.aid}
                                    // 点击置顶当前 TabPanel（受控且有上限，避免超过分割线层级）
                                    handlePanelClick={() => {
                                        const nextZ = tabZTop >= TAB_Z_MAX ? TAB_Z_BASE : tabZTop + 1;
                                        setTabZTop(nextZ);
                                        const position = tabPanelPositions.get(panel.id) || panel.position;
                                        const setter = tabPanelSetters[`tab-${panel.id}`];
                                        if (setter) {
                                            setter({...position, zIndex: nextZ});
                                        }
                                        // 同步到 store
                                        updateTabPanelPosition(panel.id, {...position, zIndex: nextZ});
                                    }}
                                    zIndex={tabPanelPositions.get(panel.id)?.zIndex ?? panel.position?.zIndex ?? 2000}
                                    panelPosition={tabPanelPositions.get(panel.id) || {
                                        ...panel.position,
                                        isSnapped: false
                                    }}
                                    setPanelPosition={(value) => {
                                        const setter = tabPanelSetters[`tab-${panel.id}`];
                                        if (setter) {
                                            setter(value);
                                            // 不在这里立即更新 store，等拖拽结束后统一更新
                                        }
                                    }}
                                    setPanelsPosition={setPanelsPosition}
                                    setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? `tab-${panel.id}` : "")}
                                    getAdjacentPanels={getAdjacentPanels}
                                    onBumpTabPanel={bumpTabPanel}
                                    otherPanels={
                                        // 仅使用去重后的 panels，排除当前 TabPanel，并过滤掉被隐藏的 TabPanel
                                        panels
                                            .filter((p: any) => p.isOpen)
                                            .filter((p: any) => !(p.type === 'tab' && (p as any).id === panel.id))
                                            .filter((p: any) => !(p.type === 'tab' && (p as any).id && hiddenPanels.has((p as any).id)))
                                    }
                                />
                            );
                        })}

                    {/* 聊天面板 */}
                    {chatPanelOpen && (
                        <ChatPanel
                            handlePanelClick={() => handlePanelClick(panel.chat)}
                            zIndex={zIndexes.chat}
                            panelPosition={{
                                ...chatPanelPosition
                            }}
                            setPanelPosition={setChatPanelPosition}
                            setPanelsPosition={setPanelsPosition}
                            setDragPanel={(isDragging: boolean) => setDragPanel(isDragging ? 'chat' : "")}
                            getAdjacentPanels={getAdjacentPanels}
                            onBumpTabPanel={bumpTabPanel}
                            otherPanels={[
                                ...panels.filter((p: any) => p.type !== 'chat' && p.isOpen)
                            ]}/>
                    )}

                    {/* 面板分割线 */}
                    {generateDividers().map((divider) => (
                        <ResizeDivider
                            key={divider.id}
                            x={divider.x}
                            y={divider.y}
                            height={divider.height}
                            leftPanel={divider.leftPanel}
                            rightPanel={divider.rightPanel}
                            setLeftPanelPosition={divider.setLeftPosition}
                            setRightPanelPosition={divider.setRightPosition}
                            minLeftWidth={200}
                            minRightWidth={200}
                        />
                    ))}
                    {tagPanelOpen && (
                        <TagPanel onClose={() => toggleTagPanel(false)} zIndex={3000}/>
                    )}
                    {/* card list */}
                    {cardListPanelOpen && (
                        <CardList
                            onClose={() => toggleCardListPanel(false)}
                            zIndex={3000}
                        />
                    )}
                    {/* 这里显示一个 LLM Panel */}
                    {llmPanelOpen && (
                        <LLMPanel onClose={() => toggleLLMPanel(false)} zIndex={3000}/>
                    )}

                    {/* 全局窗口管理器 - 在最外层渲染独立窗口 */}
                    <GlobalWindowManager/>
                </PanelContainer>
            </OuterContainer>
            <TabDragMonitor/>
        </DndProvider>
    );
});

export const WorkerSpace = memo(() => {
    return (
        <TabPanelVisibilityProvider>
            <WorkerSpaceContent/>
        </TabPanelVisibilityProvider>
    );
});

export const WorkerSpaceWrapper = () => {
    return (
        <PanelPositionProvider>
            <ReactFlowProvider>
                <WorkerSpace/>
            </ReactFlowProvider>
        </PanelPositionProvider>
    );
};
