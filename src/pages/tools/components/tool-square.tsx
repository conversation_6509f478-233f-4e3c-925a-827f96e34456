import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  BrowserAutomationIcon,
  CustomMCPIcon,
  GoogleSearchIcon,
  SubtitleIcon,
  UploadToFeishuIcon,
  VideoIcon,
  ZhiWangPaperIcon,
} from "./icons";
import { Button } from "@/components/ui/button";

interface Tool {
  id?: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color?: string;
  disabled?: boolean;
}

interface ToolCardProps extends Tool {
  onClick?: () => void;
}

const ToolCard = ({
  name,
  description,
  icon,
  disabled,
  onClick,
}: ToolCardProps) => {
  return (
    <Card
      className={cn(
        "group relative overflow-hidden shadow-none rounded-[16px] border-none p-3 h-[226px] transition-colors hover:border-primary/50"
      )}
      onClick={!disabled ? onClick : undefined}
    >
      <div className="space-y-3 h-full flex flex-col justify-center">
        <div className="flex flex-col justify-center items-center text-center gap-5">
          <div className="rounded-full p-2">{icon}</div>
          <div>
            <h3 className="font-semibold text-[#13123C] mb-2">{name}</h3>
            <p className="text-sm text-[#979797]">{description}</p>
            {disabled && (
              <>
                <Button color="primary" className="m-5 px-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="13"
                    height="13"
                    viewBox="0 0 13 13"
                    fill="none"
                  >
                    <path
                      d="M10.8504 4.81111C11.0125 4.81111 11.1681 4.8431 11.3174 4.90706C11.4666 4.97103 11.5946 5.05632 11.7012 5.16293C11.8078 5.26954 11.8931 5.39747 11.957 5.54672C12.021 5.69598 12.053 5.85162 12.053 6.01367C12.053 6.18425 12.021 6.34203 11.957 6.48702C11.8931 6.63201 11.8078 6.75781 11.7012 6.86442C11.5946 6.97103 11.4666 7.05632 11.3174 7.12028C11.1681 7.18425 11.0125 7.21623 10.8504 7.21623H7.25554V10.8111C7.25554 10.9817 7.22355 11.1395 7.15959 11.2845C7.09562 11.4295 7.01033 11.5553 6.90373 11.6619C6.79712 11.7685 6.66918 11.8538 6.51993 11.9177C6.37068 11.9817 6.21503 12.0137 6.05298 12.0137C5.8824 12.0137 5.72462 11.9817 5.57963 11.9177C5.43464 11.8538 5.30884 11.7685 5.20223 11.6619C5.09562 11.5553 5.01033 11.4295 4.94637 11.2845C4.8824 11.1395 4.85042 10.9817 4.85042 10.8111V7.21623H1.25554C1.08496 7.21623 0.927179 7.18425 0.78219 7.12028C0.637201 7.05632 0.5114 6.97103 0.404791 6.86442C0.298181 6.75781 0.212893 6.63201 0.148927 6.48702C0.0849615 6.34203 0.0529785 6.18425 0.0529785 6.01367C0.0529785 5.85162 0.0849615 5.69598 0.148927 5.54672C0.212893 5.39747 0.298182 5.26954 0.404791 5.16293C0.511401 5.05632 0.637201 4.97103 0.78219 4.90706C0.927179 4.8431 1.08496 4.81111 1.25554 4.81111H4.85042V1.21623C4.85042 1.05418 4.8824 0.898534 4.94637 0.74928C5.01033 0.600025 5.09562 0.472094 5.20223 0.365484C5.30884 0.258874 5.43464 0.173587 5.57963 0.109621C5.72462 0.0456548 5.8824 0.0136719 6.05298 0.0136719C6.3856 0.0136719 6.66918 0.130943 6.90373 0.365484C7.13827 0.600025 7.25554 0.883608 7.25554 1.21623V4.81111H10.8504Z"
                      fill="white"
                    />
                  </svg>{" "}
                  自定义MCP
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

const tools: Tool[] = [
  {
    name: "提取视频字幕",
    description: "提取视频字幕提取视频字幕提取视频字幕",
    icon: <SubtitleIcon />,
  },
  {
    name: "提取视频重点",
    description: "提取视频重点提取视频重点提取视频重点",
    icon: <VideoIcon />,
  },
  {
    name: "playwright操作浏览器",
    description: "playwright操作浏览器playwright操作浏览器",
    icon: <BrowserAutomationIcon />,
  },
  {
    name: "知网下载论文",
    description: "知网下载论文知网下载论文知网下载论文",
    icon: <ZhiWangPaperIcon />,
  },
  {
    name: "上传到飞书文档",
    description: "上传到飞书文档上传到飞书文档上传到飞书文档",
    icon: <UploadToFeishuIcon />,
  },
  {
    name: "Google search",
    description: "Google searchGoogle searchGoogle",
    icon: <GoogleSearchIcon />,
  },
  {
    name: "Google图片搜索",
    description: "Google图片搜索Google图片搜索",
    icon: <GoogleSearchIcon />,
  },
  {
    name: "暂无自定义MCP",
    description: "开始配置专属于个人的MCP",
    icon: <CustomMCPIcon />,
    disabled: true,
  },
];

export const ToolSquare = () => {
  const handleToolClick = (tool: Tool) => {
    // TODO: 处理工具点击事件
    console.log("Tool clicked:", tool.id);
  };

  return (
    <div className="px-[45px] py-[36px]">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2 mb-14">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="47"
              height="47"
              viewBox="0 0 47 47"
              fill="none"
            >
              <circle
                cx="23.0132"
                cy="24.6699"
                r="12"
                stroke="#13123C"
                strokeWidth="2"
              />
              <circle
                cx="13.0132"
                cy="30.6699"
                r="3.5"
                fill="#13123C"
                stroke="#13123C"
              />
              <circle
                cx="34.0132"
                cy="30.6699"
                r="3.5"
                fill="#13123C"
                stroke="#13123C"
              />
              <circle
                cx="23.0132"
                cy="12.6699"
                r="3.5"
                fill="#13123C"
                stroke="#13123C"
              />
            </svg> &nbsp;
            Tools 广场
          </h2>
        </div>
        <div className="grid gap-x-11 gap-y-9 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {tools.map((tool) => (
            <ToolCard
              key={tool.id}
              {...tool}
              onClick={() => handleToolClick(tool)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
