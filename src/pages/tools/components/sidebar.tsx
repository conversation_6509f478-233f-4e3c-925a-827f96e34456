import { Undo2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

export const ToolsSidebar = () => {
  const navigate = useNavigate();
  const handleBack = () => {
    navigate(-1);
    // setTimeout(() => {
    //   window.location.reload();
    // }, 100);
  };
  return (
    <div className="w-[100px] flex-grow-0 flex-shrink-0 h-full bg-background flex p-4 flex-col">
      <div
        className="flex w-12 h-12 flex-col items-center justify-center gap-8 hover:bg-muted rounded-sm p-2 cursor-pointer"
        onClick={handleBack}
      >
        <Undo2 className="text-[#999999] text-2xl" />
      </div>
    </div>
  );
};
