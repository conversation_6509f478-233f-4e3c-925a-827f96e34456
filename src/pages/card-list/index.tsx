import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Undo2 } from "lucide-react";
import { CardItem } from "@/components/card-list/card-item";
import { CardListFilter } from "@/components/card-list/card-list-filter";
import { useCardNodes, usePdfData, useCardFilters } from "@/components/card-list/hooks";
import { CardFilters } from "@/components/card-list/types";

/**
 * 卡片列表路由页面
 * 使用与标签管理相同的侧边栏样式
 */
export const CardListPage: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<CardFilters>({
    cardType: [],
    tag: [],
    search: "",
  });

  // 使用自定义钩子
  const { nodes, loading } = useCardNodes();
  const { pdfs } = usePdfData();
  const data_list = useCardFilters(nodes, pdfs, filters);

  // 处理返回导航
  const handleBack = () => {
    navigate(-1); // 返回上一页
  };

  const handleCardClick = (id: string) => {
    console.log("Card clicked:", id);
    // TODO: 处理卡片点击事件
  };

  return (
    <div className="flex h-screen">
      {/* 左侧边栏 - 使用与标签管理相同的样式 */}
      <div className="relative h-full w-16 flex-shrink-0">
        <div className="flex justify-center pt-6 w-[61px]">
          <button
            className="w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted text-[#666]"
            onClick={handleBack}
            title="返回"
          >
            <Undo2 />
          </button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 bg-white p-6 overflow-hidden">
        <div className="flex flex-col gap-6 h-full">
          {/* 筛选器顶部区域 */}
          <div className="flex-shrink-0">
            <CardListFilter onChange={setFilters} />
          </div>
          
          {/* 分割线 */}
          <div className="h-px bg-border" />
          
          {/* 卡片网格区域 */}
          <div className="flex-1 overflow-auto py-2">
            {loading ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">加载中...</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                  {data_list.map((item) => (
                    <CardItem
                      key={item.id}
                      data={item}
                      onClick={handleCardClick}
                    />
                  ))}
                </div>

                {data_list.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">暂无内容</p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardListPage;