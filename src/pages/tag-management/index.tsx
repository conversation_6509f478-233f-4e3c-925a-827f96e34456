import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Undo2 } from "lucide-react";
import { Board } from "@/components/tag-management/TagManagement";
import { useTagGroups, useTagStore } from "@/components/tag-management/stores/useTagStore";
import { Button } from "@/components/ui/button";

/**
 * 标签管理路由页面
 * 将原有的全屏弹窗模式改为独立路由页面
 */
export const TagManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { loadTagGroups, isLoading, error } = useTagStore();
  const tagGroups = useTagGroups();

  // 加载标签组数据
  useEffect(() => {
    loadTagGroups();
  }, [loadTagGroups]);

  // 处理返回导航
  const handleBack = () => {
    navigate(-1); // 返回上一页
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-muted-foreground">加载中...</div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-background">
        <div className="text-destructive mb-4">错误: {error}</div>
        <Button onClick={() => loadTagGroups()}>重试</Button>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      {/* 左侧边栏 - 使用原始 Sidebar 样式 */}
      <div className="relative h-full w-16 flex-shrink-0">
        <div className="flex justify-center pt-6 w-[61px]">
          <button
            className="w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted text-[#666]"
            onClick={handleBack}
            title="返回"
          >
            <Undo2 />
          </button>
        </div>
      </div>

      {/* 主内容区域 - Board 组件自带 header */}
      <div className="flex-1 overflow-hidden">
        <Board initial={{ columns: tagGroups }} />
      </div>
    </div>
  );
};

export default TagManagementPage;