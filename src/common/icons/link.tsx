export const LinkIcon = () => <span className="chat-link-icon">
    <svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.119 11.0949C11.4257 10.7737 11.4243 10.2628 11.119 9.94765C10.8142 9.63302 10.3202 9.63131 10.0076 9.94765L7.50649 12.5283C6.73992 13.3265 6.74188 14.6071 7.50649 15.3964C8.2714 16.186 9.51195 16.188 10.2849 15.3964L15.0082 10.5207C15.7751 9.7291 15.7751 8.4442 15.0082 7.65261C14.2416 6.86132 12.9966 6.86103 12.2298 7.65261L12.0914 7.7966C11.8614 8.03407 11.488 8.03407 11.2579 7.7966C11.0281 7.5594 11.0279 7.17423 11.2579 6.93618L11.3963 6.79219C12.6288 5.52623 14.6167 5.52768 15.8418 6.79219C17.0667 8.05672 17.0687 10.1083 15.8418 11.3811L11.1182 16.2565C9.88981 17.5147 7.90492 17.5119 6.68102 16.2485C5.45742 14.9854 5.45435 12.9362 6.67353 11.6685L9.1741 9.08721C9.94539 8.29564 11.1873 8.29736 11.9525 9.08721C12.7177 9.87708 12.7194 11.1591 11.9525 11.9553L9.59087 14.3931C9.36082 14.6306 8.98741 14.6306 8.75735 14.3931C8.5273 14.1557 8.5273 13.7702 8.75735 13.5327L11.119 11.0949V11.0949Z" fill="currentColor" />
        <circle cx="11.1382" cy="11.1982" r="10.6" stroke="currentColor" strokeWidth="0.8" />
    </svg>
</span>

export const ToNoteBookIcon = () => <span className="chat-book-icon">
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" rx="6" fill="#F0F0FA" />
        <path d="M10.5003 7.16667H8.83366C8.39163 7.16667 7.96771 7.34226 7.65515 7.65482C7.34259 7.96738 7.16699 8.39131 7.16699 8.83333V18.8333C7.16699 19.2754 7.34259 19.6993 7.65515 20.0118C7.96771 20.3244 8.39163 20.5 8.83366 20.5H17.167C17.609 20.5 18.0329 20.3244 18.3455 20.0118C18.6581 19.6993 18.8337 19.2754 18.8337 18.8333V8.83333C18.8337 8.39131 18.6581 7.96738 18.3455 7.65482C18.0329 7.34226 17.609 7.16667 17.167 7.16667H15.5003M10.5003 7.16667C10.5003 6.72464 10.6759 6.30072 10.9885 5.98816C11.301 5.67559 11.725 5.5 12.167 5.5H13.8337C14.2757 5.5 14.6996 5.67559 15.0122 5.98816C15.3247 6.30072 15.5003 6.72464 15.5003 7.16667M10.5003 7.16667C10.5003 7.60869 10.6759 8.03262 10.9885 8.34518C11.301 8.65774 11.725 8.83333 12.167 8.83333H13.8337C14.2757 8.83333 14.6996 8.65774 15.0122 8.34518C15.3247 8.03262 15.5003 7.60869 15.5003 7.16667M10.5003 13H15.5003M10.5003 16.3333H15.5003" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

</span>


export const ToNodeIcon = () => <span className="chat-node-icon">
    <svg width="26" height="27" viewBox="0 0 26 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.883545" width="26" height="26" rx="6" fill="#F0F0FA" />
        <path fillRule="evenodd" clipRule="evenodd" d="M9.47114 15.6573C9.72939 15.6573 9.93875 15.4049 9.93875 15.0936V14.5426H14.0098V15.3388C14.0098 16.2178 14.5387 16.9312 15.1907 16.9312H19.3252C19.9777 16.9312 20.5061 16.2178 20.5061 15.3388V12.1539C20.5061 11.2749 19.9772 10.5615 19.3252 10.5615H15.1912C14.5387 10.5615 14.0103 11.2749 14.0103 12.1539V12.9502H9.93875V12.3992C9.93875 12.0877 9.72951 11.8355 9.47114 11.8355H7.23616C6.97779 11.8355 6.76855 12.0877 6.76855 12.3992V15.0936C6.76855 15.4049 6.97791 15.6573 7.23616 15.6573H9.47114ZM15.191 11.5805H19.3249L19.3656 11.5856C19.5151 11.62 19.6604 11.8417 19.6604 12.1538V15.3387L19.6567 15.4215C19.6314 15.7164 19.4739 15.9119 19.3249 15.9119H15.191L15.1503 15.9068C15.0002 15.8725 14.8555 15.6508 14.8555 15.3387V12.1538L14.8586 12.071C14.8845 11.7761 15.0415 11.5805 15.191 11.5805Z" fill="currentColor" />
    </svg>
</span>

export const ReferenceIcon = () => <span>
    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.5075 5.03248C7.22572 3.65816 9.69944 4.39063 9.99164 6.65135C10.3016 9.04965 9.6829 10.9511 7.28163 10.9511C5.05186 10.9511 5.12047 9.0401 5.12047 9.0401C5.12047 6.9926 8.65379 6.7196 10.712 7.74335C14.4854 10.1321 11.7411 14.5001 7.62467 14.5001C4.21446 14.5001 1.44995 12.7938 1.44995 7.6751C1.44995 2.55635 4.21446 0.850098 7.62467 0.850098C10.0314 0.850098 12.2018 2.08334 13 4.54983" stroke="currentColor" strokeWidth="1.05" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
</span>