@bg-color: #F0F0FA;
@bg-item: #F9FAFC;
@bg-btn: #6E6BEE;
@font-color: #13123C;
@font-color80: #13123CCC;
@font-color-w: #fff;
@font-color-cancel: rgba(19, 18, 60, 0.50);

.base-icon {
    width: 24px;
    height: 24px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.base-icon-pos {
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.text-center {
    display: flex;
    align-items: center;
}

.size-12 {
    width: 12px;
    height: 12px;
}

.size-18 {
    width: 16px;
    height: 16px;
}

.chat-history__toolbar {
    .open {
        background-image: url("@/assets/icons/open.svg");

        &:extend(.base-icon);

        &:hover {
            background-image: url("@/assets/icons/open-hover.svg");
        }

        &:active {
            background-image: url("@/assets/icons/open-click.svg");
        }
    }

    .chat {
        background-image: url("@/assets/icons/chat.svg");

        &:extend(.base-icon);

        &:hover {
            background-image: url("@/assets/icons/chat-hover.svg");
        }

        &:active {
            background-image: url("@/assets/icons/chat-click.svg");
        }
    }

    .chat-close {

        background-image: url("@/assets/icons/chat-close.svg");

        &:extend(.base-icon);

        &:hover {
            background-image: url("@/assets/icons/chat-close-hover.svg");
        }
    }


}

.chat-toolbar {
    background-color: @bg-color;

    .chat-title {
        height: 46px;
        padding-left: 20px;
        width: 160px;
        border-radius: 15px 15px 0 0;
        background-color: @bg-item;
        position: relative;

        &:extend(.text-center);

        .chat-title__icon {
            width: 20px;
            height: 20px;
            margin: 4px;

            background-image: url("@/assets/icons/chat-icon.svg");

            &:extend(.base-icon);
        }
    }
}

.pdf-icon {
    width: 47px;
    height: 47px;
    background-image: url("@/assets/icons/pdf.svg");

    &:extend(.base-icon-pos);
}

.node-icon {
    width: 47px;
    height: 47px;
    background-image: url("@/assets/icons/node.svg");

    &:extend(.base-icon-pos);
}

.submit-icon {
    width: 70px;
    height: 32px;
    background-image: url("@/assets/icons/submit.png");

    &:extend(.base-icon-pos);
}

.link-icon {
    width: 20px;
    height: 20px;
    background-image: url("@/assets/icons/link-p.png");
    // background-image: url("@/assets/icons/link-o.png");

    &:extend(.base-icon-pos);
}

.chat-bg {
    background-color: @bg-item;
}

.tag-close-icon {
    background-image: url("@/assets/icons/tag-close.svg");
    &:extend(.base-icon);
}

.tag-pdf-icon {
    background-image: url("@/assets/icons/pdf-tag.svg");
    &:extend(.base-icon-pos,  .size-12);
}

.tag-node-icon {
    background-image: url("@/assets/icons/tag-node.svg");

    &:extend(.base-icon-pos, .size-12);
}

.edit-icon {
    background-image: url("@/assets/icons/edit.svg");
    
    &:extend(.base-icon-pos, .size-18);
}

.reload-icon {
    background-image: url("@/assets/icons/reload.svg");
    &:extend(.base-icon-pos, .size-18);
}