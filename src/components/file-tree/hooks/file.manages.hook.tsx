/**
 *  
 *  文件夹的表是 folders src/local/db/schemas/folder
 *  请在 folder.service.ts 文件帮我增加，对folder 的增删改查，根节点的pid 为root
 *  
 *  笔记本的表是 src/local/db/schemas/notebook.ts
 *  请在 notebook.service.ts文件中,帮我增加对 记事本的增删改查，
 * 
 * 
 */

import { useState, useCallback, useEffect, createContext, useContext } from 'react';
import { folderService, CreateFolderReq, UpdateFolderReq } from '@/local/services/folder.service';
import { FolderDocType } from '@/local/db/schemas/folder';
import { useWorkerSpaceStore } from '@/store/workerspace-store/store';
import { FileNode } from '../files/arbtree';
import { buildFileTree, TreeBuilder } from '../utils/tree-builder';
import { useFileSelectionStore } from '@/store/file-selection-store';

export const useGetAllFiles = () => {
    const [files, setFiles] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const wid = useWorkerSpaceStore(state => state.wid) as string;
    const { refreshTrigger } = useFileSelectionStore();
    const getAllFiles = useCallback(async () => {

        if (!wid) return
        setLoading(true);
        setError(null);
        try {
            const folders = await folderService.getFolderList({ wid });
            const files = await folderService.getAllFiles({ wid })

            const root:any = folders?.filter((folder) => folder?.id === wid + '_root')?.[0]
            
            console.log('root', root)
            const folderNoRoot = folders?.filter((folder) => folder?.id !== wid + '_root')
            // 包含 root 节点到 buildFileTree 的输入中，以便正确处理父子关系
            const allResult = [...files, ...folderNoRoot, ...(root ? [root] : [])]
            // 使用正确的 rootParentId 参数，让 tree-builder 知道谁是根节点
            const result = TreeBuilder.buildTree(allResult, wid + '_root');

            if (root?.children?.length) {
                // 使用 root.children 的顺序对 result 进行排序
                const childrenOrder = root.children;
                console.log('🔄 使用root.children顺序排序:', childrenOrder);
                
                result.sort((a, b) => {
                    const indexA = childrenOrder.indexOf(a.id);
                    const indexB = childrenOrder.indexOf(b.id);
                    
                    // 如果都在children数组中，按数组顺序排序
                    if (indexA !== -1 && indexB !== -1) {
                        return indexA - indexB;
                    }
                    // 如果只有一个在children数组中，优先显示在数组中的
                    if (indexA !== -1) return -1;
                    if (indexB !== -1) return 1;
                    
                    // 都不在children数组中，保持原有顺序
                    return 0;
                });
                
                console.log('✅ 排序后的result:', result.map(item => ({ id: item.id, name: item.name })));
            }

            setFiles(result)
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '获取文件列表失败';
            setError(errorMessage);
            console.error('获取文件列表失败:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [wid]);

    const updateFolder = useCallback(async (req: UpdateFolderReq) => {
        setLoading(true);
        setError(null);
        try {
            await folderService.updateFolder(req);
            // 更新成功后更新本地文件列表
            setFiles(prev => prev.map(file =>
                file.id === req.id
                    ? { ...file, ...req, update_at: Math.floor(Date.now() / 1000) }
                    : file
            ));
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '更新文件夹失败';
            setError(errorMessage);
            console.error('更新文件夹失败:', err);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [wid]);

    useEffect(() => {
        getAllFiles()
    }, [wid])

    // 监听刷新触发器，从外部触发文件列表刷新
    useEffect(() => {
        if (refreshTrigger > 0) {
            getAllFiles();
        }
    }, [refreshTrigger]); // 移除getAllFiles依赖，避免循环

    const updateOrAdd = async (newName: string, originData: any) => {
        try {
            if (originData?.isNew) {
                // 新增操作
                if (originData.type === 'folder') {
                    // 新增文件夹
                    
                    await folderService.createFolder({
                        wid: wid,
                        file_name: newName,
                        parent_id: originData?.parent_id,
                        type: originData?.type || ''
                        // 可以根据需要调整父级ID
                    });

                } else if (originData.type === 'file') {
                    
                    await folderService.createFile({
                        wid: wid,
                        file_name: newName,
                        parent_id: originData?.parent_id, // 可以根据需要调整父级ID
                        type: 'file',
                        mime_type: 'notebook'
                    });
                    console.log('✅ 新增文件成功:', newName);
                }

                getAllFiles()
            } else {
                // 更新操作
                if (originData.type === 'folder') {
                    // 更新文件夹
                    await folderService.updateFolder({
                        id: originData.id,
                        file_name: newName,
                    });
                    getAllFiles()
                    console.log('✅ 更新文件夹成功:', newName);
                } else if (originData.type === 'file') {
                    // 更新文件
                    await folderService.updateFile({
                        id: originData.id,
                        file_name: newName
                    });
                    console.log('✅ 更新文件成功:', newName);
                }
            }
        } catch (error) {
            console.error('❌ 操作失败:', error);
            throw error;
        }
    }

    const updateOnMove = async (dragChangeResult: any) => {
        try {
            setLoading(true);
            setError(null);
            
            const { moveType, draggedNodes, crossLevelDetails, sameLevelDetails } = dragChangeResult;
            
            console.log(draggedNodes)
            if (moveType.isCrossLevel && crossLevelDetails) {
                // 跨级拖动：需要更新新旧节点的children ID和文件的parent_id
                console.log('🔀 处理跨级拖动更新');
                
                // 1. 更新拖拽的文件/文件夹的parent_id
                for (const draggedNode of draggedNodes) {
                    if (draggedNode.type === 'file') {
                        // 更新文件的parent_id
                        await folderService.updateFile({
                            id: draggedNode.id,
                            parent_id: draggedNode.newParentId || wid + '_root'
                        });
                        console.log(`✅ 更新文件${draggedNode.name}的parent_id: ${draggedNode.oldParentId} -> ${draggedNode.newParentId}`);
                    } else if (draggedNode.type === 'folder') {
                        // 更新文件夹的parent_id
                        await folderService.updateFolder({
                            id: draggedNode.id,
                            parent_id: draggedNode.newParentId || wid + '_root'
                        });
                        console.log(`✅ 更新文件夹${draggedNode.name}的parent_id: ${draggedNode.oldParentId} -> ${draggedNode.newParentId}`);
                    }
                }
                
                // 2. 更新旧父节点的children数组
                for (const oldParent of crossLevelDetails.oldParents) {
                    if (oldParent.parentId) {
                        await folderService.updateFolderChildren({
                            id: oldParent.parentId,
                            children: oldParent.afterChildren
                        });
                        console.log(`✅ 更新旧父节点${oldParent.parentName}的children:`, oldParent.afterChildren);
                    } else {
                        // 更新root文件夹的children
                        await folderService.updateFolderChildren({
                            id: wid + '_root',
                            children: oldParent.afterChildren
                        });
                        console.log(`✅ 更新root的children:`, oldParent.afterChildren);
                    }
                }
                
                // 3. 更新新父节点的children数组
                const newParent = crossLevelDetails.newParent;
                if (newParent.parentId) {
                    await folderService.updateFolderChildren({
                        id: newParent.parentId,
                        children: newParent.afterChildren
                    });
                    console.log(`✅ 更新新父节点${newParent.parentName}的children:`, newParent.afterChildren);
                } else {
                    // 更新root文件夹的children
                    await folderService.updateFolderChildren({
                        id: wid + '_root',
                        children: newParent.afterChildren
                    });
                    console.log(`✅ 更新root的children:`, newParent.afterChildren);
                }
                
            } else if (moveType.isSameLevel && sameLevelDetails) {
                // 同级拖动：只需要更新父文件夹的children顺序
                console.log('↕️ 处理同级拖动更新');
                
                if (sameLevelDetails.parentId) {
                    await folderService.updateFolderChildren({
                        id: sameLevelDetails.parentId,
                        children: sameLevelDetails.afterOrder
                    });
                    console.log(`✅ 更新父节点${sameLevelDetails.parentName}的children顺序:`, sameLevelDetails.afterOrder);
                } else {
                    // 更新root文件夹的children
                    await folderService.updateFolderChildren({
                        id: wid + '_root',
                        children: sameLevelDetails.afterOrder
                    });
                    console.log(`✅ 更新root的children顺序:`, sameLevelDetails.afterOrder);
                }
            }
            
            // 更新完成后重新获取文件列表
            await getAllFiles();
            console.log('✅ 拖拽数据库更新完成');
            
        } catch (error) {
            console.error('❌ 拖拽数据库更新失败:', error);
            setError(error instanceof Error ? error.message : '拖拽更新失败');
            throw error;
        } finally {
            setLoading(false);
        }
    }

    // 删除节点（文件或文件夹）
    const deleteNode = useCallback(async (nodeId: string) => {
        try {
            setLoading(true);
            setError(null);

            // 找到要删除的节点信息
            const flattenData = (nodes: any[]): any[] => {
                return nodes.reduce((acc, node) => {
                    acc.push(node);
                    if (node.children) {
                        acc.push(...flattenData(node.children));
                    }
                    return acc;
                }, []);
            };

            const nodeToDelete = flattenData(files).find((node: any) => node.id === nodeId);
            
            if (!nodeToDelete) {
                throw new Error('找不到要删除的节点');
            }

            console.log('🗑️ 删除节点:', { id: nodeId, name: nodeToDelete.name, type: nodeToDelete.type });

            // 调用相应的删除API
            if (nodeToDelete.type === 'file') {
                await folderService.deleteFile(nodeId);
            } else if (nodeToDelete.type === 'folder') {
                await folderService.deleteFolder(nodeId);
            } else {
                throw new Error('未知的节点类型');
            }

            // API调用成功后，刷新文件列表
            await getAllFiles();
            
            console.log('✅ 节点删除成功:', nodeId);
            return true;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '删除失败: 未知错误';
            setError(errorMessage);
            console.error('❌ 删除节点失败:', error);
            throw error;
        } finally {
            setLoading(false);
        }
    }, [files, getAllFiles]);

    return {
        allFiles: files,
        loading,
        error,
        // createFolder,
        updateFolder,
        updateOrAdd,
        updateOnMove,
        deleteNode,
        setFiles,
        getAllFiles,
    };
};

// 初始数据
const _initialData: FileNode[] = [
    { id: "1", name: "未读文件", type: 'file' },
    { id: "2", name: "线程", type: 'file' },
    {
        id: "3",
        name: "聊天室",
        type: 'folder',
        children: [
            { id: "c1", name: "常规", type: 'file' },
            { id: "c2", name: "随机", type: 'file' },
            { id: "c3", name: "开源项目", type: 'file' },
        ],
    },
    {
        id: "4",
        name: "私信",
        type: 'folder',
        children: [
            { id: "d1", name: "Alice", type: 'file' },
            { id: "d2", name: "Bob", type: 'file' },
            { id: "d3", name: "Charlie", type: 'file' },
        ],
    },
];


export const FilesContext = createContext<any>({})
export const useFilesContext = () => {
    const context = useContext(FilesContext)
    if (!context) {
        throw new Error('useFilesContext must be used within a FilesContext')
    }
    return context
}