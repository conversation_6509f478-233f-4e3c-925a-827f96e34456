import { FileNode } from '../files/arbtree';

/**
 * 扁平化数据项接口
 */
export interface FlatDataItem {
    id: string;
    wid: string;
    parent_id: string;
    file_name: string;
    type: 'file' | 'folder';
    mime_type?: string;
    order: number;
    create_at: number;
    update_at: number;
}

/**
 * 树形结构构建工具类
 */
export class TreeBuilder {
    /**
     * 将扁平化数据转换为树形结构
     * @param flatData 扁平化的数据数组
     * @param rootParentId 根节点的parent_id，默认为'root'
     * @returns 树形结构数组
     */
    static buildTree(flatData: FlatDataItem[], rootParentId: string = 'root'): FileNode[] {
        const originData = [...flatData]
        console.log('元数据: ', originData)
        console.log('🌳 开始构建树结构:', {
            totalItems: flatData.length,
            rootParentId,
            items: flatData.map(item => ({
                ...item,
                id: item.id,
                name: item.file_name,
                type: item.type,
                parent_id: item.parent_id,
            } as any))
        });

        if (!flatData || flatData.length === 0) {
            console.log('⚠️ 没有数据，返回空数组');
            return [];
        }

        // 创建映射表用于快速查找
        const itemMap = new Map<string, FileNode>();
        const rootItems: FileNode[] = [];

        // 第一遍：将扁平数据转换为FileNode格式并建立映射
        flatData.forEach(item => {
            const fileNode: FileNode = {
                ...item,
                id: item.id,
                name: item.file_name,
                type: item.type,
                mime_type: item.mime_type,
                children: item.type === 'folder' ? [] : undefined,
                isEditing: false,
                isNew: false
            };
            itemMap.set(item.id, fileNode);
        });

        console.log('📋 创建了节点映射:', itemMap.size, '个节点');

        // 第二遍：构建父子关系
        flatData.forEach(item => {
            const currentNode = itemMap.get(item.id);
            if (!currentNode) {
                console.warn('⚠️ 找不到节点:', item.id);
                return;
            }

            // 如果当前项就是 root 节点本身，跳过不显示
            if (item.id === rootParentId) {
                console.log('🚫 跳过 root 节点本身:', item.file_name);
                return;
            }

            if (item.parent_id === rootParentId) {
                // 根节点的直接子节点，显示为根级项目
                rootItems.push(currentNode);
                console.log('🔝 添加根级项目:', item.file_name, `(${item.type})`);
            } else {
                // 子节点
                const parentNode = itemMap.get(item.parent_id);
                if (parentNode) {
                    if (parentNode.children) {
                        parentNode.children.push(currentNode);
                        console.log('📂 添加子节点:', item.file_name, '到父节点:', parentNode.name);
                    } else {
                        console.warn('⚠️ 父节点不是文件夹类型:', parentNode.name, parentNode.type);
                    }
                } else {
                    console.warn('⚠️ 找不到父节点:', item.parent_id, '对于节点:', item.file_name);
                    console.log('📋 可用的父节点IDs:', [...itemMap.keys()]);
                    // 如果找不到父节点，将其作为根节点处理
                    rootItems.push(currentNode);
                }
            }
        });

        // 按order排序，并根据元数据中的children数组进行排序
        const sortByOrder = (nodes: FileNode[], parentId?: string): void => {
            // 找到当前父节点的元数据
            const parentItem = parentId ? originData.find(item => item.id === parentId) : null;
            
            if (parentItem && (parentItem as any).children && Array.isArray((parentItem as any).children)) {
                // 如果父节点有children数组，按照children数组的顺序排序
                const childrenOrder = (parentItem as any).children;
                console.log('🔄 使用父节点children数组排序:', parentId, childrenOrder);
                
                nodes.sort((a, b) => {
                    const indexA = childrenOrder.indexOf(a.id);
                    const indexB = childrenOrder.indexOf(b.id);
                    
                    // 如果都在children数组中，按数组顺序排序
                    if (indexA !== -1 && indexB !== -1) {
                        return indexA - indexB;
                    }
                    // 如果只有一个在children数组中，优先显示在数组中的
                    if (indexA !== -1) return -1;
                    if (indexB !== -1) return 1;
                    
                    // 都不在children数组中，按order排序
                    const itemA = originData.find(item => item.id === a.id);
                    const itemB = originData.find(item => item.id === b.id);
                    return (itemA?.order || 0) - (itemB?.order || 0);
                });
            } else {
                // 没有children数组，按传统order排序
                nodes.sort((a, b) => {
                    const itemA = originData.find(item => item.id === a.id);
                    const itemB = originData.find(item => item.id === b.id);
                    return (itemA?.order || 0) - (itemB?.order || 0);
                });
            }

            // 递归排序子节点
            nodes.forEach(node => {
                if (node.children && node.children.length > 0) {
                    sortByOrder(node.children, node.id);
                }
            });
        };

        sortByOrder(rootItems, rootParentId);

        console.log('✅ 树结构构建完成:', {
            rootCount: rootItems.length,
            structure: this.logTreeStructure(rootItems)
        });

        return rootItems;
    }

    /**
     * 打印树结构用于调试
     */
    private static logTreeStructure(nodes: FileNode[], depth: number = 0): any {
        return nodes.map(node => ({
            name: node.name,
            type: node.type,
            hasChildren: node.children ? node.children.length > 0 : false,
            childrenCount: node.children?.length || 0,
            children: node.children && node.children.length > 0 
                ? this.logTreeStructure(node.children, depth + 1) 
                : []
        }));
    }

    /**
     * 合并文件夹和文件数据并构建树形结构
     * @param folders 文件夹数据数组
     * @param files 文件数据数组
     * @param rootParentId 根节点的parent_id，默认为'root'
     * @returns 树形结构数组
     */
    static buildMixedTree(
        folders: FlatDataItem[], 
        files: FlatDataItem[], 
        rootParentId: string = 'root'
    ): FileNode[] {
        const combinedData = [...folders, ...files];
        return this.buildTree(combinedData, rootParentId);
    }

    /**
     * 树形结构扁平化
     * @param treeData 树形数据
     * @returns 扁平化的节点数组
     */
    static flattenTree(treeData: FileNode[]): FileNode[] {
        const result: FileNode[] = [];
        
        const flatten = (nodes: FileNode[]) => {
            nodes.forEach(node => {
                result.push(node);
                if (node.children && node.children.length > 0) {
                    flatten(node.children);
                }
            });
        };

        flatten(treeData);
        return result;
    }

    /**
     * 根据ID查找树中的节点
     * @param treeData 树形数据
     * @param targetId 目标节点ID
     * @returns 找到的节点或null
     */
    static findNodeById(treeData: FileNode[], targetId: string): FileNode | null {
        for (const node of treeData) {
            if (node.id === targetId) {
                return node;
            }
            if (node.children && node.children.length > 0) {
                const found = this.findNodeById(node.children, targetId);
                if (found) return found;
            }
        }
        return null;
    }

    /**
     * 获取节点的完整路径
     * @param treeData 树形数据
     * @param targetId 目标节点ID
     * @returns 路径数组，包含从根到目标节点的所有节点名称
     */
    static getNodePath(treeData: FileNode[], targetId: string): string[] {
        const path: string[] = [];

        const findPath = (nodes: FileNode[], target: string, currentPath: string[]): boolean => {
            for (const node of nodes) {
                const newPath = [...currentPath, node.name];
                
                if (node.id === target) {
                    path.push(...newPath);
                    return true;
                }
                
                if (node.children && node.children.length > 0) {
                    if (findPath(node.children, target, newPath)) {
                        return true;
                    }
                }
            }
            return false;
        };

        findPath(treeData, targetId, []);
        return path;
    }
}

/**
 * 便捷函数：将API返回的数据转换为树形结构
 */
export const buildFileTree = (apiData: FlatDataItem[]): FileNode[] => {
    return TreeBuilder.buildTree(apiData);
};

/**
 * 便捷函数：合并文件夹和文件数据
 */
export const buildMixedFileTree = (folders: FlatDataItem[], files: FlatDataItem[]): FileNode[] => {
    return TreeBuilder.buildMixedTree(folders, files);
};