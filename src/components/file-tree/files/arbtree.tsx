import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Tree} from 'react-arborist';
import {
    Archive,
    ChevronDown,
    ChevronRight,
    Edit,
    File,
    FileText,
    Folder,
    FolderPlus,
    Image,
    Music,
    Trash2,
    Video
} from 'lucide-react';
import {
    ContextMenu,
    ContextMenuContent,
    ContextMenuItem,
    ContextMenuPortal,
    ContextMenuSeparator,
    ContextMenuTrigger,
} from '@radix-ui/react-context-menu';
import {nanoid} from 'nanoid';
import _ from 'lodash';
import {useFilesContext} from '../hooks/file.manages.hook';
import {useWorkerSpaceStore} from '@/store/workerspace-store/store';
import {useMenu} from '@/components/pdf/hooks/menu';
import {useFileSelectionStore} from '@/store/file-selection-store';
import FolderOpenOutlined from '@ant-design/icons/lib/icons/FolderOpenOutlined';
import FolderOutlined from '@ant-design/icons/lib/icons/FolderOutlined';
import FilePdfOutlined from '@ant-design/icons/lib/icons/FilePdfOutlined';
import BookOutlined from '@ant-design/icons/lib/icons/BookOutlined';
import {workspaceService} from '@/local/services/workspace-service';
import {folderService} from '@/local/services/folder.service';
import {DeletePdf} from '@/components/pdf/components/popup/DeletePdf';
import {usePdfStore} from '@/store/pdf-store';
import {useTabPanelStore} from '@/store/tab-panel-store';

type fileType = 'file' | 'folder'

// 根据文件类型和mime_type获取对应的图标
const getFileIcon = (mimeType?: string) => {
    const iconProps = {size: 16, className: "text-gray-500"};

    if (!mimeType) return <File {...iconProps} />;

    const iconMap: Record<string, React.ReactElement> = {
        'notebook': <BookOutlined/>,
        'application/pdf': <FilePdfOutlined style={{color: 'red'}}/>,
        'pdf': <FilePdfOutlined style={{color: 'red'}}/>,
        'image': <Image {...iconProps} className="text-green-500"/>,
        'video': <Video {...iconProps} className="text-purple-500"/>,
        'audio': <Music {...iconProps} className="text-yellow-500"/>,
        'archive': <Archive {...iconProps} className="text-orange-500"/>,
    };

    // 检查完整匹配或前缀匹配
    for (const [key, icon] of Object.entries(iconMap)) {
        if (mimeType === key || mimeType.startsWith(key + '/')) {
            return icon;
        }
    }

    return <File {...iconProps} />;
};

// 文件节点类型定义
export interface FileNode {
    id: string;
    name: string;
    type: fileType;
    mime_type?: string;
    children?: FileNode[];
    isEditing?: boolean;
    isNew?: boolean;
}

// 拖拽变动结果类型
interface DragChangeResult {
    draggedNodes: Array<{
        id: string;
        name: string;
        type: fileType;
        oldParentId: string | null;
        newParentId: string | null;
        oldIndex: number;
        newIndex: number;
    }>;
    affectedParents: Array<{
        id: string | null;
        name: string;
        type: string
        changeType: 'lost_children' | 'gained_children';
        changedChildren: string[];
    }>;
    moveType: {
        isCrossLevel: boolean;
        isSameLevel: boolean;
        description: string;
    };
    crossLevelDetails?: {
        oldParents: Array<{
            parentId: string | null;
            parentName: string;
            beforeChildren: string[];
            afterChildren: string[];
            removedNodes: string[];
        }>;
        newParent: {
            parentId: string | null;
            parentName: string;
            beforeChildren: string[];
            afterChildren: string[];
            addedNodes: string[];
        };
    };
    sameLevelDetails?: {
        parentId: string | null;
        parentName: string;
        beforeOrder: string[];
        afterOrder: string[];
        movedNodes: Array<{
            nodeId: string;
            nodeName: string;
            oldIndex: number;
            newIndex: number;
        }>;
    };
    childrenOrderChanges: {
        oldOrder: Array<{
            parentId: string | null;
            parentName: string;
            childrenIds: string[];
        }>;
        newOrder: Array<{
            parentId: string | null;
            parentName: string;
            childrenIds: string[];
        }>;
    };
    summary: {
        totalMovedNodes: number;
        affectedFolders: number;
        moveDetails: string;
    };
}

interface ArbTreeProps {
    searchString?: string;
    filteredData?: any[];
    expandedItems?: string[];
    highlightMap?: Record<string, string>;
}

export const ArbTree = forwardRef<any, ArbTreeProps>((props, ref) => {
    const {searchString, filteredData, expandedItems, highlightMap} = props;
    const {data, setData, updateOrAdd, updateOnMove, deleteNodeFromHook, getAllFiles} = useFilesContext()
    const treeRef = useRef<any>(null);
    const wid = useWorkerSpaceStore(state => state.wid) as string;
    const {
        selectedFileId,
        clearSelection
    } = useFileSelectionStore();
    const {
        tabPanels,
        removePanelTabItem,
        setPanelActiveAid,
        getPanelContent
    } = useTabPanelStore();

    // 公共函数：获取当前选中的文件夹ID
    const getCurrentFolderId = useCallback(() => {
        const selectId = [...treeRef?.current?.selectedIds || []]?.[0]
        if (selectId) {
            const flattenData = (nodes: FileNode[]): FileNode[] => {
                return _.flatMapDeep(nodes, node => [node, ...(node.children ? flattenData(node.children) : [])]);
            };
            const currentNode: any = _.find(flattenData(data), {id: selectId});

            if (currentNode?.type === 'folder') {
                return currentNode?.id
            } else {
                // 选中文件时，返回其父文件夹ID，如果没有父文件夹则返回根目录
                return currentNode?.parent_id || (wid + '_root');
            }
        }
        return wid + '_root';
    }, [data, wid]);
    // 辅助函数：根据 ID 查找节点及其父节点信息
    const findNodeWithParent = useCallback((nodeId: string, nodes: FileNode[], parentId: string | null = null): {
        node: FileNode;
        parentId: string | null;
        index: number
    } | null => {
        for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i];
            if (node.id === nodeId) {
                return {node, parentId, index: i};
            }
            if (node.children) {
                const found = findNodeWithParent(nodeId, node.children, node.id);
                if (found) return found;
            }
        }
        return null;
    }, []);

    // 辅助函数：获取节点的所有子节点 ID
    const getAllChildrenIds = useCallback((node: FileNode): string[] => {
        const childIds: string[] = [];
        if (node.children) {
            node.children.forEach(child => {
                childIds.push(child.id);
                childIds.push(...getAllChildrenIds(child));
            });
        }
        return childIds;
    }, []);

    // 辅助函数：根据 ID 查找节点名称
    const getNodeName = useCallback((nodeId: string | null, nodes: FileNode[]): string => {
        if (!nodeId) return '根目录';

        const findNode = (nodes: FileNode[]): string | null => {
            for (const node of nodes) {
                if (node.id === nodeId) return node.name;
                if (node.children) {
                    const found = findNode(node.children);
                    if (found) return found;
                }
            }
            return null;
        };

        return findNode(nodes) || '未知节点';
    }, []);

    // 公共函数：创建新节点（文件或文件夹）
    const createNode = useCallback((type: 'folder' | 'file') => {
        const newNode: FileNode = {
            id: nanoid(),
            name: type === 'folder' ? '新建文件夹' : '新建文件.txt',
            type: type,
            ...(type === 'folder' ? {children: []} : {mime_type: 'notebook'}),
            isEditing: true,
            isNew: true
        };

        const parentId = getCurrentFolderId();

        setData((prevData: FileNode[]) => {
            if (!parentId || parentId?.endsWith('_root')) {
                return [...prevData, {
                    ...newNode,
                    parent_id: parentId || (wid + '_root')
                }];
            }

            const updateChildren = (nodes: FileNode[]): FileNode[] => {
                return nodes.map(node => {
                    if (node.id === parentId) {
                        return {
                            ...node,
                            children: [...(node.children || []), {
                                ...newNode,
                                parent_id: node.id
                            }]
                        };
                    }
                    if (node.children) {
                        return {
                            ...node,
                            children: updateChildren(node.children)
                        };
                    }
                    return node;
                });
            };
            return updateChildren(prevData);
        });

        // 如果是文件，同步到 pdf-store
        if (type === 'file') {
            try {
                usePdfStore.getState().addPdfs(new Map<string, any>().set(newNode.id, {
                    aid: newNode.id,
                    filename: newNode.name,
                    url: '',
                }));
            } catch (e) {
                // 忽略同步失败
            }
        }
    }, [getCurrentFolderId, setData]);

    // 创建新文件夹
    const createFolder = useCallback(() => createNode('folder'), [createNode]);

    // 创建新文件
    const createFile = useCallback(() => createNode('file'), [createNode]);

    // 删除确认弹窗相关状态
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [deleteWithNodes, setDeleteWithNodes] = useState(false);
    const [deleteLoading, setDeleteLoading] = useState(false);
    const [pendingDeleteIds, setPendingDeleteIds] = useState<string[]>([]);

    // 触发删除（打开确认弹窗）
    const deleteNode = useCallback(async (nodeId: string) => {
        if (confirmOpen) return;
        const selectedNodes = treeRef.current?.selectedNodes || [];
        if (selectedNodes.length > 1) {
            const ids = selectedNodes.map((n: any) => n.data?.id || n.id);
            setPendingDeleteIds(ids);
        } else {
            setPendingDeleteIds([nodeId]);
        }
        setConfirmOpen(true);
    }, [confirmOpen]);

    // 确认删除处理
    const handleConfirmDelete = useCallback(async () => {
        setDeleteLoading(true);
        try {
            for (const id of pendingDeleteIds) {
                // 查找节点类型
                const found = findNodeWithParent(id, data);
                const isFile = found?.node?.type === 'file';

                if (deleteWithNodes && isFile) {
                    // 删除附件并级联删除相关节点与高亮
                    await workspaceService.deleteAttach({aid: id, type: 2});
                    // 同步更新父文件夹 children 列表
                    if (found?.parentId) {
                        const parentNode = findNodeWithParent(found.parentId, data)?.node;
                        const currentChildren = (parentNode?.children || []).map((c: any) => c.id);
                        const afterChildren = currentChildren.filter((cid) => cid !== id);
                        await folderService.updateFolderChildren({id: found.parentId, children: afterChildren});
                    } else {
                        // root
                        const rootChildren = data.map((n: any) => n.id).filter((cid: string) => cid !== id);
                        await folderService.updateFolderChildren({id: wid + '_root', children: rootChildren});
                    }
                } else {
                    // 按原逻辑删除（文件或文件夹）
                    await deleteNodeFromHook(id);
                }
                // 避免本地db并发写入冲突
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            // 刷新文件列表
            await getAllFiles();

            // 删除后同步关闭对应 Tab，并清理前端 pdf/highlight 状态
            const pdfState = usePdfStore.getState();
            pendingDeleteIds.forEach((aid) => {
                // 清除Tip
                pdfState.pdfs.get(aid)?.pdfHighlighterUtils?.setTip(null);

                // 主面板 Tab
                const tabs = pdfState.tabItems || [];
                if (tabs.some((t: any) => t.key === aid)) {
                    const newTabs = tabs.filter((t: any) => t.key !== aid);
                    pdfState.setTabItems(newTabs);
                    if (pdfState.activeAid === aid) {
                        pdfState.setActiveAid(newTabs[0]?.key || '');
                    }
                }

                // 各 Panel Tab
                if (tabPanels && tabPanels.size > 0) {
                    tabPanels.forEach((_panelInfo: any, panelId: string) => {
                        const panelContent = getPanelContent(panelId);
                        if (panelContent?.tabItems?.some((ti: any) => ti.key === aid)) {
                            removePanelTabItem(panelId, aid);
                            // 重新获取最新的panel内容
                            const refreshedContent = getPanelContent(panelId);
                            if (refreshedContent && refreshedContent.activeAid === aid) {
                                const next = refreshedContent.tabItems?.[0]?.key || '';
                                setPanelActiveAid(panelId, next);
                            }
                        }
                    });
                }

                // 各 Window Tab
                const windows = pdfState.windows;
                if (windows && windows.size > 0) {
                    windows.forEach((w: any, windowId: string) => {
                        if (w?.tabs?.some((ti: any) => ti.key === aid)) {
                            pdfState.removeTabFromWindow(windowId, aid);
                        }
                    });
                }

                // 移除 pdf 实体（包含 highlights）
                if (pdfState.pdfs.has(aid)) {
                    pdfState.removePdfs(aid);
                }
            });
        } catch (error) {
            if (error instanceof Error) {
                alert(`删除失败: ${error.message}`);
            } else {
                alert('删除失败: 未知错误');
            }
        } finally {
            setDeleteLoading(false);
            setConfirmOpen(false);
            setDeleteWithNodes(false);
            setPendingDeleteIds([]);
        }
    }, [pendingDeleteIds, deleteWithNodes, data, deleteNodeFromHook, getAllFiles, wid, findNodeWithParent]);

    useImperativeHandle(ref, () => {
        return {
            createFolder() {
                createFolder()
            },
            createFile() {
                createFile()
            },
            getCurrentSelectId() {
                return getCurrentFolderId()
            }
        }
    })

    // 重命名节点
    const startRename = useCallback((nodeId: string) => {
        setData((prevData: FileNode[]) => {
            // 找到要重命名的节点信息
            const findNode = (nodes: FileNode[]): FileNode | null => {
                for (const node of nodes) {
                    if (node.id === nodeId) {
                        return node;
                    }
                    if (node.children) {
                        const found = findNode(node.children);
                        if (found) return found;
                    }
                }
                return null;
            };

            findNode(prevData);

            const updateNode = (nodes: FileNode[]): FileNode[] => {
                return nodes.map(node => {
                    if (node.id === nodeId) {
                        return {...node, isEditing: true};
                    }
                    if (node.children) {
                        return {
                            ...node,
                            children: updateNode(node.children)
                        };
                    }
                    return node;
                });
            };

            return updateNode(prevData);
        });
    }, []);

    // 完成重命名
    const finishRename = useCallback(async (nodeId: string, newName: string) => {
        if (!newName.trim()) {
            return false;
        }

        // 使用lodash根据ID找到对应的数据
        const flattenData = (nodes: FileNode[]): FileNode[] => {
            return _.flatMapDeep(nodes, node => [node, ...(node.children ? flattenData(node.children) : [])]);
        };
        const currentNode = _.find(flattenData(data), {id: nodeId});

        updateOrAdd(newName, currentNode)

        setData((prevData: any) => {
            const updateNode = (nodes: FileNode[]): FileNode[] => {
                return nodes.map(node => {
                    if (node.id === nodeId) {
                        return {
                            ...node,
                            name: newName.trim(),
                            isEditing: false,
                            isNew: false
                        };
                    }
                    if (node.children) {
                        return {
                            ...node,
                            children: updateNode(node.children)
                        };
                    }
                    return node;
                });
            };

            return updateNode(prevData);
        });

        // 同步更新 pdf-store 中的名称，便于 BigSearch 立即反映重命名
        try {
            usePdfStore.getState().updatePdfs({aid: nodeId, filename: newName.trim()});
        } catch (e) {
            // 忽略同步失败
        }
    }, [data]);

    // 处理拖拽
    const onMove = useCallback((args: { dragIds: string[], parentId: string | null, index: number }) => {
        const {dragIds, parentId, index} = args;


        setData((prevData: FileNode[]) => {
            // 收集拖拽前的节点信息
            const draggedNodesInfo = dragIds.map(dragId => {
                const nodeInfo = findNodeWithParent(dragId, prevData);
                if (!nodeInfo) return null;
                return {
                    ...nodeInfo,
                    id: dragId,
                    name: nodeInfo.node.name,
                    type: nodeInfo.node.type,
                    // 如果 parentId 为 null，说明是根级别节点，实际的 parent_id 应该是 wid + '_root'
                    oldParentId: nodeInfo.parentId || (wid + '_root'),
                    oldIndex: nodeInfo.index,
                    node: nodeInfo.node
                };
            }).filter(Boolean) as Array<{
                id: string;
                name: string;
                type: 'file' | 'folder';
                oldParentId: string | null;
                oldIndex: number;
                node: FileNode;
            }>;

            // 创建数据副本
            const newData = JSON.parse(JSON.stringify(prevData));

            // 找到被拖拽的节点
            const draggedNodes: FileNode[] = [];
            const removeNodes = (nodes: FileNode[]): FileNode[] => {
                const result: FileNode[] = [];

                for (const node of nodes) {
                    if (dragIds.includes(node.id)) {
                        // 保存被拖拽的节点（深拷贝避免引用问题）
                        draggedNodes.push(JSON.parse(JSON.stringify(node)));
                    } else {
                        // 保留非拖拽节点
                        const newNode = {...node};
                        if (node.children) {
                            // 递归处理子节点
                            newNode.children = removeNodes(node.children);
                        }
                        result.push(newNode);
                    }
                }

                return result;
            };

            // 从原位置移除节点，创建完全干净的副本
            const cleanedData = removeNodes(newData);

            // 如果没有父节点，插入到根级别
            if (!parentId) {
                cleanedData.splice(index, 0, ...draggedNodes);
            } else {
                // 找到目标父节点并插入
                const insertInto = (nodes: FileNode[]): FileNode[] => {
                    return nodes.map(node => {
                        if (node.id === parentId) {
                            if (!node.children) node.children = [];
                            node.children.splice(index, 0, ...draggedNodes);
                        } else if (node.children) {
                            node.children = insertInto(node.children);
                        }
                        return node;
                    });
                };
                insertInto(cleanedData);
            }

            // 统计拖拽后新旧节点的children顺序
            const getChildrenOrder = (nodes: FileNode[], parentId: string | null): string[] => {
                if (!parentId) {
                    // 根级别节点
                    return nodes.map(node => node.id);
                } else {
                    // 查找指定父节点的children顺序
                    const findParent = (nodeList: FileNode[]): string[] => {
                        for (const node of nodeList) {
                            if (node.id === parentId) {
                                return node.children ? node.children.map(child => child.id) : [];
                            }
                            if (node.children) {
                                const found = findParent(node.children);
                                if (found.length > 0) return found;
                            }
                        }
                        return [];
                    };
                    return findParent(nodes);
                }
            };

            // 获取拖拽前后的children顺序
            const oldParentChildrenOrder = new Map<string | null, string[]>();
            const newParentChildrenOrder = new Map<string | null, string[]>();

            // 收集旧的children顺序
            draggedNodesInfo.forEach(nodeInfo => {
                const oldParentId = nodeInfo.oldParentId;
                if (!oldParentChildrenOrder.has(oldParentId)) {
                    oldParentChildrenOrder.set(oldParentId, getChildrenOrder(prevData, oldParentId));
                }
            });

            // 收集新的children顺序
            const affectedParentIds = new Set([
                ...draggedNodesInfo.map(info => info.oldParentId),
                parentId
            ]);

            affectedParentIds.forEach(pid => {
                newParentChildrenOrder.set(pid, getChildrenOrder(cleanedData, pid));
            });

            // 判断是跨级拖动还是同级拖动
            const isCrossLevelMove = draggedNodesInfo.some(info => info.oldParentId !== parentId);
            const isSameLevelMove = draggedNodesInfo.every(info => info.oldParentId === parentId);

            // 构建 dragChangeResult 对象
            const dragChangeResult: DragChangeResult = {
                moveType: {
                    isCrossLevel: isCrossLevelMove,
                    isSameLevel: isSameLevelMove,
                    description: isCrossLevelMove ? '跨级拖动' : '同级拖动'
                },
                draggedNodes: draggedNodesInfo.map(info => ({
                    id: info.id,
                    name: info.name,
                    type: info.type,
                    oldParentId: info.oldParentId,
                    // 如果 parentId 为 null，说明是拖拽到根级别，实际的 parent_id 应该是 wid + '_root'
                    newParentId: parentId || (wid + '_root'),
                    oldIndex: info.oldIndex,
                    newIndex: index
                })),
                affectedParents: [
                    // 失去子节点的父节点
                    ...Array.from(new Set(draggedNodesInfo.map(info => info.oldParentId)))
                        .filter(pid => pid !== (parentId || (wid + '_root')))
                        .map(pid => ({
                            id: pid,
                            name: getNodeName(pid?.endsWith('_root') ? null : pid, prevData) || 'root',
                            type: pid?.endsWith('_root') ? 'root' : 'folder',
                            changeType: 'lost_children' as const,
                            changedChildren: draggedNodesInfo
                                .filter(info => info.oldParentId === pid)
                                .map(info => info.id)
                        })),
                    // 获得子节点的父节点
                    ...(draggedNodesInfo.some(info => info.oldParentId !== (parentId || (wid + '_root'))) ? [{
                        id: parentId || (wid + '_root'),
                        name: getNodeName(parentId, cleanedData) || 'root',
                        type: parentId ? 'folder' : 'root',
                        changeType: 'gained_children' as const,
                        changedChildren: draggedNodesInfo.map(info => info.id)
                    }] : [])
                ],
                crossLevelDetails: isCrossLevelMove ? {
                    oldParents: Array.from(oldParentChildrenOrder.entries())
                        .filter(([pid]) => pid !== parentId)
                        .map(([pid, children]) => {
                            const afterChildren = newParentChildrenOrder.get(pid) || [];
                            const removedNodes = children.filter(childId => !afterChildren.includes(childId));
                            return {
                                // 如果是根级别，转换为实际的数据库 ID
                                parentId: pid === null ? (wid + '_root') : pid,
                                parentName: getNodeName(pid, prevData) || 'root',
                                beforeChildren: children,
                                afterChildren: afterChildren,
                                removedNodes: removedNodes
                            };
                        }),
                    newParent: {
                        // 如果是根级别，转换为实际的数据库 ID
                        parentId: parentId || (wid + '_root'),
                        parentName: getNodeName(parentId, cleanedData) || 'root',
                        beforeChildren: oldParentChildrenOrder.get(parentId) || [],
                        afterChildren: newParentChildrenOrder.get(parentId) || [],
                        addedNodes: draggedNodesInfo.map(info => info.id)
                    }
                } : undefined,
                sameLevelDetails: isSameLevelMove ? {
                    // 如果是根级别，转换为实际的数据库 ID
                    parentId: parentId || (wid + '_root'),
                    parentName: getNodeName(parentId, cleanedData) || 'root',
                    beforeOrder: oldParentChildrenOrder.get(parentId) || [],
                    afterOrder: newParentChildrenOrder.get(parentId) || [],
                    movedNodes: draggedNodesInfo.map(info => ({
                        nodeId: info.id,
                        nodeName: info.name,
                        oldIndex: info.oldIndex,
                        newIndex: index
                    }))
                } : undefined,
                childrenOrderChanges: {
                    oldOrder: [],
                    newOrder: []
                },
                summary: {
                    totalMovedNodes: draggedNodesInfo.length,
                    affectedFolders: affectedParentIds.size,
                    moveDetails: isCrossLevelMove ? '跨级拖动' : '同级拖动'
                }
            };


            // 调用 updateOnMove 更新数据库
            updateOnMove(dragChangeResult);

            return cleanedData;
        });

    }, [findNodeWithParent, getNodeName, data, setData]);

    // 使用过滤后的数据或原始数据
    const displayData = searchString && filteredData ? filteredData : data;

    // 在搜索时展开所需的节点
    useEffect(() => {
        if (searchString && expandedItems && expandedItems.length > 0 && treeRef.current) {
            // 延迟执行以确保树已渲染
            setTimeout(() => {
                expandedItems.forEach(nodeId => {
                    const node = treeRef.current?.get(nodeId);
                    if (node && !node.isOpen) {
                        node.open();
                    }
                });
            }, 100);
        }
    }, [searchString, expandedItems, displayData]);

    // 监听文件选择变化，自动选中对应的文件
    useEffect(() => {
        if (selectedFileId && treeRef.current) {
            setTimeout(() => {
                const node = treeRef.current?.get(selectedFileId);
                if (node) {
                    node.select();
                    // 展开父级节点确保文件可见
                    let parent = node.parent;
                    while (parent) {
                        if (!parent.isOpen) {
                            parent.open();
                        }
                        parent = parent.parent;
                    }
                    // 清除选择状态，避免重复选择
                    clearSelection();
                }
            }, 200);
        }
    }, [selectedFileId, clearSelection]);

    // 使用状态来跟踪选中项目的数量
    const [selectedCount, setSelectedCount] = useState(0);

    // 监听树的选择变化
    useEffect(() => {
        if (treeRef.current) {
            const updateSelectedCount = () => {
                const count = treeRef.current?.selectedIds?.size || 0;
                setSelectedCount(count);
            };

            // 初始化计数
            updateSelectedCount();

            // 这里可能需要监听选择变化事件
            // react-arborist 可能有相关的事件监听器
        }
    }, [treeRef.current]);

    return (
        <>
            {/* 多选状态指示器 */}
            {selectedCount > 1 && (
                <div
                    className="mb-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-between text-sm text-blue-800">
                    <span>已选择 {selectedCount} 个项目</span>
                    <button
                        onClick={() => {
                            if (treeRef.current) {
                                treeRef.current.deselectAll();
                                setSelectedCount(0);
                            }
                        }}
                        className="text-blue-600 hover:text-blue-800 underline"
                    >
                        取消选择
                    </button>
                </div>
            )}

            <div
                className="w-full h-full border border-gray-200 rounded-lg bg-white relative"
                style={{
                    position: 'relative',
                    zIndex: 1,
                    // 确保不会裁剪拖拽预览
                    overflow: 'visible'
                }}
                onKeyDown={(e) => {
                    // 全选快捷键 Ctrl/Cmd + A
                    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                        e.preventDefault();
                        e.stopPropagation();

                        if (treeRef.current) {
                            treeRef.current.selectAll();
                        }
                        return;
                    }

                    // ESC 键取消选择
                    if (e.key === 'Escape' && treeRef.current) {
                        treeRef.current.deselectAll();
                        setSelectedCount(0);
                    }
                }}
                tabIndex={0} // 使 div 可以接收键盘焦点
            >
                <Tree
                    ref={treeRef}
                    data={displayData}
                    onMove={searchString ? undefined : onMove}
                    width="100%"
                    indent={12}
                    rowHeight={32}
                    className="drag-tree"
                    disableDrag={!!searchString}
                    selection="multi" // 明确启用多选
                    onSelect={(selectedNodes) => {
                        setSelectedCount(selectedNodes.length);
                    }}
                    renderDragPreview={(props) => {
                        const {mouse, isDragging, dragIds} = props;

                        if (!isDragging || !mouse || !dragIds || dragIds.length === 0) {
                            return null;
                        }

                        // 获取所有被拖拽的节点信息
                        const flattenData = (nodes: FileNode[]): FileNode[] => {
                            return _.flatMapDeep(nodes, node => [node, ...(node.children ? flattenData(node.children) : [])]);
                        };

                        const allNodes = flattenData(data);
                        const draggedNodes = dragIds.map(dragId => allNodes.find(node => node.id === dragId)).filter(Boolean);

                        // 如果没有找到任何节点，或者找到的节点数量与拖拽ID数量不匹配
                        // 说明不是文件树的拖拽（可能是Tab拖拽），返回 null
                        if (draggedNodes.length === 0 || draggedNodes.length !== dragIds.length) {
                            return null;
                        }

                        const primaryNode = draggedNodes[0];


                        // 计算相对于父容器的偏移量
                        let relativeLeft = mouse.x;
                        let relativeTop = mouse.y;

                        // 获取 Tree 容器的边界信息
                        const getContainerRect = () => {
                            // 方法1: 通过 treeRef.current.element
                            if (treeRef.current?.element) {
                                return treeRef.current.element.getBoundingClientRect();
                            }

                            // 方法2: 通过查找 drag-tree 类名的元素
                            const treeElement = document.querySelector('.drag-tree');
                            if (treeElement) {
                                return treeElement.getBoundingClientRect();
                            }

                            return null;
                        };

                        const containerRect = getContainerRect();
                        if (containerRect) {
                            relativeLeft = mouse.x - containerRect.left;
                            relativeTop = mouse.y - containerRect.top;
                        }

                        // 拖拽预览的尺寸（估算）
                        const previewWidth = 200;  // maxWidth
                        const previewHeight = 60;  // 估算高度
                        const offset = 15;         // 偏移量

                        // 边界检测和位置调整
                        let finalLeft = relativeLeft + offset;
                        let finalTop = relativeTop + 10;

                        if (containerRect) {
                            // 右边界检测
                            if (finalLeft + previewWidth > containerRect.width) {
                                finalLeft = relativeLeft - previewWidth - 5; // 显示在鼠标左侧
                            }

                            // 左边界检测
                            if (finalLeft < 0) {
                                finalLeft = 5; // 距离左边界5px
                            }

                            // 下边界检测
                            if (finalTop + previewHeight > containerRect.height) {
                                finalTop = relativeTop - previewHeight - 5; // 显示在鼠标上方
                            }

                            // 上边界检测
                            if (finalTop < 0) {
                                finalTop = 5; // 距离上边界5px
                            }

                        } else {
                            // 如果无法获取容器边界，使用基本的偏移
                            finalLeft = relativeLeft + offset;
                            finalTop = relativeTop + 10;
                        }

                        return (
                            <div
                                style={{
                                    position: 'absolute',  // 恢复相对定位
                                    left: finalLeft,  // 使用边界调整后的位置
                                    top: finalTop,
                                    pointerEvents: 'none',
                                    backgroundColor: 'white',
                                    border: '1px solid #e5e7eb',
                                    borderRadius: '8px',
                                    padding: '8px 12px',
                                    fontSize: '12px',
                                    zIndex: 99999,  // 保持高 zIndex 确保可见性
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                                    minWidth: '120px',
                                    maxWidth: '200px',
                                    // 确保元素不会被隐藏
                                    visibility: 'visible',
                                    display: 'block',
                                    opacity: 1,
                                }}
                            >
                                <div className="flex items-center gap-2 font-medium text-gray-700 mb-1">
                                    {primaryNode?.type === 'folder' ? (
                                        <Folder size={12} className="text-blue-500"/>
                                    ) : (
                                        <File size={12} className="text-gray-500"/>
                                    )}
                                    <span className="truncate">
                                        {primaryNode?.name || '未知文件'}
                                    </span>
                                </div>
                                {dragIds.length > 1 && (
                                    <div className="text-gray-500 text-xs">
                                        +{dragIds.length - 1} 个其他项目
                                    </div>
                                )}
                            </div>
                        );
                    }}
                    renderCursor={({top, left, indent}) => {
                        return (
                            <div
                                style={{
                                    position: 'absolute',
                                    top: top,
                                    left: left + indent,
                                    right: 8,
                                    height: '2px',
                                    backgroundColor: '#3b82f6',
                                    borderRadius: '1px',
                                    zIndex: 1000,
                                    pointerEvents: 'none'
                                }}
                            />
                        );
                    }}
                >
                    {(props) => (
                        <FileNodeRenderer
                            {...props}
                            onCreateFolder={createFolder}
                            onCreateFile={createFile}
                            onDelete={deleteNode}
                            onStartRename={startRename}
                            onFinishRename={finishRename}
                            searchString={searchString}
                            highlightMap={highlightMap}
                            selectedCount={selectedCount}
                            treeRef={treeRef}
                            setPendingDeleteIds={setPendingDeleteIds}
                            setConfirmOpen={setConfirmOpen}
                        />
                    )}
                </Tree>
            </div>
            <DeletePdf
                isModalOpen={confirmOpen}
                checked={deleteWithNodes}
                onChange={(checked) => setDeleteWithNodes(checked)}
                handleOk={handleConfirmDelete}
                handleCancel={() => {
                    if (!deleteLoading) {
                        setConfirmOpen(false);
                        setDeleteWithNodes(false);
                        setPendingDeleteIds([]);
                    }
                }}
                loading={deleteLoading}
            />
        </>
    );
})

// 节点渲染器组件
const FileNodeRenderer: React.FC<any> = ({
                                             node,
                                             style,
                                             dragHandle,
                                             onCreateFolder,
                                             onCreateFile,
                                             onDelete,
                                             onStartRename,
                                             onFinishRename,
                                             searchString,
                                             highlightMap,
                                             selectedCount,
                                             treeRef,
                                             setPendingDeleteIds,
                                             setConfirmOpen
                                         }) => {
    const [editingName, setEditingName] = useState(node.data.name);
    const inputRef = useRef<HTMLInputElement>(null);
    const [isFocusing, setIsFocusing] = useState(false); // 跟踪是否正在聚焦过程中
    const focusTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const {onResourceClick} = useMenu()
    const isFolder = node.data.type === 'folder';
    const isEditing = node.data.isEditing;

    // 高亮文本渲染函数
    const renderHighlightedText = (text: string) => {
        if (!searchString || !highlightMap?.[node.data.id]) {
            return text;
        }

        const searchTerm = searchString.toLowerCase();
        const textLower = text.toLowerCase();
        const index = textLower.indexOf(searchTerm);

        if (index === -1) {
            return text;
        }

        return (
            <>
                {text.substring(0, index)}
                <span className="bg-yellow-300 font-medium">
                    {text.substring(index, index + searchTerm.length)}
                </span>
                {text.substring(index + searchTerm.length)}
            </>
        );
    };


    React.useEffect(() => {
        if (isEditing && inputRef.current) {
            setIsFocusing(true);
            // 清理之前的定时器
            if (focusTimeoutRef.current) {
                clearTimeout(focusTimeoutRef.current);
            }
            // 延迟聚焦，确保右键菜单完全关闭后再设置焦点
            focusTimeoutRef.current = setTimeout(() => {
                if (inputRef.current) {
                    inputRef.current.focus();
                    inputRef.current.select();
                    setIsFocusing(false);
                }
            }, 100); // 100ms延迟足够让菜单关闭
        }

        // 清理函数
        return () => {
            if (focusTimeoutRef.current) {
                clearTimeout(focusTimeoutRef.current);
            }
        };
    }, [isEditing]);

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            onFinishRename(node.data.id, editingName);
        } else if (e.key === 'Escape') {
            setEditingName(node.data.name);
            onFinishRename(node.data.id, node.data.name);
        }
        e?.stopPropagation()
    };

    const handleBlur = (_e: any) => {
        // 如果正在聚焦过程中，不处理blur事件
        if (isFocusing) {
            return;
        }
        onFinishRename(node.data.id, editingName);
    };


    // 处理双击事件
    const handleDoubleClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();

        if (!isEditing) {
            if (isFolder) {
                // 文件夹双击：展开/收起
                node.toggle();
            } else {
                // 文件双击：进入重命名模式
                onStartRename(node.data.id);
            }
        }
    };


    const renderIcon = () => {
        if (isFolder) {
            return node.isOpen ?
                <FolderOpenOutlined/>
                :
                <FolderOutlined/>;
        }
        // 使用新的文件图标函数，传入文件名和mime_type
        return getFileIcon(node.data.mime_type);
    };

    const renderExpandIcon = () => {
        if (!isFolder) return <div className="w-4"/>;
        return (
            <button
                onClick={(e) => {
                    e.stopPropagation(); // 阻止事件冒泡，避免与整体点击冲突
                    node.toggle();
                }}
                className="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
            >
                {node.isOpen ? <ChevronDown size={12}/> : <ChevronRight size={12}/>}
            </button>
        );
    };

    // 添加树容器的键盘事件处理，拦截字符输入防止触发搜索
    const handleTreeKeyDown = useCallback((e: React.KeyboardEvent) => {
        // 拦截字符键入事件，防止默认的键盘搜索行为
        if (e.key.length === 1 && /[a-zA-Z0-9\u4e00-\u9fa5]/.test(e.key)) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, []);

    return (
        <ContextMenu onOpenChange={(open) => {
            // 当右键菜单即将打开时，确保当前节点被选中
            if (open && !node.isSelected) {
                node.select();
            }
        }}>
            <ContextMenuTrigger asChild>
                <div
                    ref={searchString ? undefined : dragHandle}
                    tabIndex={0}  // 确保可以接收键盘焦点
                    style={{
                        ...style,
                        zIndex: node.isDragging ? 1000 : 'auto',
                        cursor: searchString ? 'default' : 'pointer'
                    }}
                    className={`flex items-center px-2 py-1 hover:bg-gray-100 select-none ${
                        node.isSelected ?
                            'bg-blue-100 border-l-4 border-l-blue-500 border-y border-r border-y-blue-200 border-r-blue-200' :
                            ''
                    } ${node.isDragging && !searchString ? 'opacity-50 z-50' : ''
                    } ${node.willReceiveDrop && !searchString ? 'bg-green-100 border-2 border-green-300' : ''
                    } ${searchString ? 'cursor-default' : 'cursor-pointer'
                    }`}
                    onClick={(e) => {
                        console.log('click', isFolder)
                        // 只处理文件夹的展开/收起，让 react-arborist 处理选择
                        if (isFolder && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
                            node.toggle();
                        }
                        onResourceClick(node);
                    }}
                    onDoubleClick={handleDoubleClick}
                    onKeyDown={handleTreeKeyDown}
                >
                    {renderExpandIcon()}
                    {renderIcon()}
                    &nbsp;

                    {isEditing ? (
                        <input
                            ref={inputRef}
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            onKeyDown={handleKeyPress}
                            onBlur={handleBlur}
                            onClick={(e) => e.stopPropagation()}
                            onDoubleClick={(e) => e.stopPropagation()}
                            className="flex-1 px-1 py-0 text-sm border border-blue-300 rounded outline-none"
                        />
                    ) : (
                        <div className="flex-1 flex items-center min-w-0">
                            <span className="text-sm truncate flex-1 min-w-0">
                                {renderHighlightedText(node.data.name)}
                            </span>
                            {node.isSelected && selectedCount > 1 && (
                                <div
                                    className="ml-2 w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs flex-shrink-0">
                                    ✓
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </ContextMenuTrigger>

            <ContextMenuPortal>
                <ContextMenuContent
                    className="w-48 bg-white border border-gray-200 rounded-md shadow-lg p-1"
                    style={{zIndex: 9999}}
                    avoidCollisions={true}
                    collisionPadding={10}
                >
                    {isFolder && (
                        <>
                            <ContextMenuItem
                                onClick={() => !searchString && onCreateFolder(node.data.id)}
                                className={`flex items-center gap-2 px-2 py-1 text-sm rounded ${searchString
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'hover:bg-gray-100 cursor-pointer'
                                }`}
                                disabled={!!searchString}
                            >
                                <FolderPlus size={14}/>
                                新建文件夹
                            </ContextMenuItem>
                            <ContextMenuItem
                                onClick={() => !searchString && onCreateFile(node.data.id)}
                                className={`flex items-center gap-2 px-2 py-1 text-sm rounded ${searchString
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'hover:bg-gray-100 cursor-pointer'
                                }`}
                                disabled={!!searchString}
                            >
                                <FileText size={14}/>
                                新建文件
                            </ContextMenuItem>
                            <ContextMenuSeparator className="h-px bg-gray-200 my-1"/>
                        </>
                    )}

                    <ContextMenuItem
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (!isEditing && selectedCount <= 1) {
                                onStartRename(node.data.id);
                            }
                        }}
                        className={`flex items-center gap-2 px-2 py-1 text-sm rounded ${selectedCount > 1
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'hover:bg-gray-100 cursor-pointer'
                        }`}
                        disabled={selectedCount > 1}
                    >
                        <Edit size={14}/>
                        {selectedCount > 1 ? '重命名 (仅支持单选)' : '重命名'}
                    </ContextMenuItem>

                    <ContextMenuItem
                        onClick={() => {
                            // 获取当前选中的所有节点
                            const selectedNodes = treeRef.current?.selectedNodes || [];

                            if (selectedNodes.length > 1) {
                                // 批量删除：收集所有选中节点的ID，一次性打开确认对话框
                                const selectedIds = selectedNodes.map((n: any) => n.data?.id || n.id);
                                setPendingDeleteIds(selectedIds);
                                setConfirmOpen(true);
                            } else {
                                // 单个删除：删除右键点击的节点
                                onDelete(node.data.id);
                            }
                        }}
                        className="flex items-center gap-2 px-2 py-1 text-sm hover:bg-red-100 text-red-600 rounded cursor-pointer"
                    >
                        <Trash2 size={14}/>
                        {selectedCount > 1
                            ? `删除 ${selectedCount} 个项目`
                            : '删除'
                        }
                    </ContextMenuItem>
                </ContextMenuContent>
            </ContextMenuPortal>
        </ContextMenu>
    );
};