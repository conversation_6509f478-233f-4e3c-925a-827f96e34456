import React, { useEffect, useRef } from 'react';
import { Menu } from 'antd';
import { 
  FileAddOutlined, 
  FolderAddOutlined, 
  EditOutlined, 
  DeleteOutlined 
} from '@ant-design/icons';

interface ContextMenuProps {
  visible: boolean;
  x: number;
  y: number;
  onNewFile: () => void;
  onNewFolder: () => void;
  onRename: () => void;
  onDelete: () => void;
  onClose: () => void;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  visible,
  x,
  y,
  onNewFile,
  onNewFolder,
  onRename,
  onDelete,
  onClose
}) => {
  
  const menuRef = useRef<HTMLDivElement>(null);
  
  // 点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log('event', event)
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (visible) {
      // 使用 click 事件而不是 mousedown，避免与菜单项点击冲突
      document.addEventListener('click', handleClickOutside);
      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [visible, onClose]);
  
  if (!visible) return null;



  const menuItems = [
    {
      key: 'newFile',
      icon: <FileAddOutlined />,
      label: '新建文件',
      onClick: (e: any) => {
        onNewFile();
        onClose();
      }
    },
    {
      key: 'newFolder',
      icon: <FolderAddOutlined />,
      label: '新建文件夹',
      onClick: (e: any) => {
        onNewFolder();
        onClose();
      }
    },
    {
      key: 'rename',
      icon: <EditOutlined />,
      label: '重命名',
      onClick: (e: any) => {
        onRename();
        onClose();
      }
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      onClick: (e: any) => {
        onDelete();
        onClose();
      }
    }
  ];

  return (
    <div
      ref={menuRef}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
      style={{
        position: 'absolute',
        left: x,
        top: y,
        zIndex: 1000000000,
        backgroundColor: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        minWidth: '120px',
        userSelect: 'none'
      }}
    >
      <Menu
        mode="vertical"
        items={menuItems}
        style={{
          border: 'none',
          boxShadow: 'none'
        }}
      />
    </div>
  );
}; 