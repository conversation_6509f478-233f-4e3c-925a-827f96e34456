import React, { useState, useCallback, memo } from 'react';
import {
  BaseEdge,
  EdgeLabelRenderer,
  getBezierPath,
  // getSmoothStepPath,
  getStraightPath,
  EdgeProps,
  useStore,
  MarkerType,
  useReactFlow,
} from '@xyflow/react';
import { useFlowStore, isEdgeEditing } from '@/store/flow-store';
import { updateEdgeLabel } from '@/api/canvas';

// 自定义箭头组件
const EdgeArrow: React.FC<{
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  strokeColor: string;
}> = ({ sourceX, sourceY, targetX, targetY, strokeColor }) => {
  // 计算箭头角度
  const angle = Math.atan2(targetY - sourceY, targetX - sourceX) * (180 / Math.PI);

  // 箭头位置（稍微偏移，避免重叠）
  const arrowOffset = 4;
  const arrowX = targetX - Math.cos(angle * Math.PI / 180) * arrowOffset;
  const arrowY = targetY - Math.sin(angle * Math.PI / 180) * arrowOffset;

  return (
    <g>
      <path
        // d="M 0 0 L 10 5 L 0 10 z"
        d={`M ${targetX - 10} ${targetY - 5} L ${targetX} ${targetY} L ${targetX - 10} ${targetY + 5}`}
        style={{
          fill: strokeColor,
          stroke: 'none'
        }}
      />
    </g>
  )
};

// 动画圆圈组件
const AnimatedCircle: React.FC<{
  edgePath: string;
  strokeColor: string;
}> = ({ edgePath, strokeColor }) => (
  <circle r="10" fill={strokeColor}>
    <animateMotion dur="2s" repeatCount="indefinite" path={edgePath} />
  </circle>
);

const EditableEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  source,
  target,
}) => {
  // 全局编辑状态
  const isEditing = isEdgeEditing(id);
  const currentLabel = (data?.label as string) || '';
  const [tempLabel, setTempLabel] = useState(currentLabel);
  const { setEditingEdgeId } = useFlowStore();
  const {setEdges} = useReactFlow()

  const edges = useStore(state=>state.edges)


  // 路径类型（可扩展为 props）
  const useBezier = !!(style as any)?.straight;
  const pathParams = { sourceX, sourceY, sourcePosition, targetX, targetY, targetPosition };
  const [edgePath, labelX, labelY] = useBezier ? getStraightPath(pathParams) : getBezierPath(pathParams);

  // 当前缩放比例
  const zoom = useStore((state) => state.transform[2]);

  // 编辑逻辑
  const startEdit = useCallback(() => {
    setEditingEdgeId(id);
    setTempLabel(currentLabel);
  }, [id, currentLabel, setEditingEdgeId]);

  const confirmEdit = useCallback(async () => {
    setEditingEdgeId(null);
    try {
      const result = await updateEdgeLabel({ eid: id, label: tempLabel });
      const resultData = result.data || result;
      if (resultData.created && resultData.eid !== id) {
        // 新建边，更新ID
        const updatedEdges = edges.map(edge =>
          edge.id === id
            ? {
              id: resultData.eid,
              source,
              target,
              type: 'editableEdge',
              data: { ...data, label: tempLabel },
            }
            : edge
        );
        setEdges(updatedEdges);
      } else {
        // 只更新标签
        const updatedEdges = edges.map(edge =>
          edge.id === id
            ? { ...edge, data: { ...edge.data, label: tempLabel } }
            : edge
        );
        setEdges(updatedEdges);
      }
    } catch (error) {
      setTempLabel(currentLabel);
    }
  }, [id, tempLabel, data, setEditingEdgeId, edges, setEdges, source, target, currentLabel]);

  const cancelEdit = useCallback(() => {
    setEditingEdgeId(null);
    setTempLabel(currentLabel);
  }, [currentLabel, setEditingEdgeId]);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        confirmEdit();
      } else if (event.key === 'Escape') {
        event.preventDefault();
        cancelEdit();
      }
    },
    [confirmEdit, cancelEdit]
  );

  // 显示文本
  const getDisplayText = (text: string) =>
    text.length > 10 ? text.substring(0, 10) + '...' : text;

  // 标签渲染
  const renderLabel = () =>
    currentLabel ? (
      <div
        onClick={startEdit}
        title={currentLabel ? `完整标签: ${currentLabel}` : '点击编辑标签'}
      >
        {getDisplayText(currentLabel)}
      </div>
    ) : null;

  const strokeColor = style.stroke || '#6b6f76'
  const animation = !!style?.animation
  const isDashed = !!style?.strokeDasharray

  // 使用 getMarkerEnd 获取箭头
  // const markerEnd = getMarkerEnd(EdgeMarkerType.ArrowClosed);
  // 实线带箭头  strokeDasharray: false -> none
  // 虚线不带箭头 strokeDasharray: true -> 8

  // 创建动画样式
  const edgeStyle: React.CSSProperties = {
    ...style,
    strokeDasharray: isDashed ? '5,5' : 'none',
  };

  // 如果是虚线且需要动画，添加动画效果
  if (isDashed) {
    edgeStyle.animation = 'dash 1s linear infinite';
    edgeStyle.strokeDasharray = '5,5';
  }

  return (
    <>
      {/* 添加 CSS 动画定义 */}
      <style>
        {`
          @keyframes dash {
            to {
              stroke-dashoffset: -20;
            }
          }
        `}
      </style>
      {/* 实线边 */}
      <BaseEdge
        id={id}
        path={edgePath}
        style={edgeStyle}
        markerEnd={MarkerType.ArrowClosed}
      />
      {/* 自定义箭头 */}
      {!style?.strokeDasharray && <EdgeArrow
        sourceX={sourceX}
        sourceY={sourceY}
        targetX={targetX}
        targetY={targetY}
        strokeColor={strokeColor}
      />}
      {/* 动画圆圈 */}
      {animation && <AnimatedCircle
        edgePath={edgePath}
        strokeColor={strokeColor}
      />}
      {/* 标签输入/显示 */}
      <EdgeLabelRenderer>
        <div
          style={{
            transform: `translate(-50%, -100%) translate(${labelX}px,${labelY}px)`,
            cursor: 'text',
            zIndex: 1000,
            position: 'absolute',
          }}
          className="nodrag nopan pointer-events-auto max-w-[200px]"
          onClick={(e) => e.stopPropagation()}
        >
          {isEditing ? (
            <textarea
              value={tempLabel}
              onChange={(e) => setTempLabel(e.target.value)}
              onBlur={confirmEdit}
              onKeyDown={handleKeyDown}
              autoFocus
              style={{ maxWidth: 200 }}
              placeholder="输入标签"
            />
          ) : (
            renderLabel()
          )}
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default memo(EditableEdge); 