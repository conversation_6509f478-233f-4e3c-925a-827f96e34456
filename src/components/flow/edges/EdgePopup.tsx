import React, { useCallback, useEffect, useRef, useState } from "react";
import { Edge, useReactFlow, useStore } from "@xyflow/react";
import { useFlowStore } from "@/store/flow-store";
import { useUpdateEdge } from "./use-edge-update";
import { useEdgeConnect } from "../hooks/edge-connect";
import "./index.less";

interface EdgePopupProps {
  edge: Edge;
  position: { left: number; top: number };
  onClose: () => void;
}

const iconBtn =
  "w-9 h-9 flex items-center justify-center rounded-lg  transition-colors text-[#6b6f76] hover:text-primary text-xl";

const colorList = [
  "#FFDF7E",
  "#FFAB7E",
  "#B692FC",
  "#03D2FC",
  "#DAF329",
  "#fff",
];

const strokeWidths = [1, 2, 3, 4, 5, 6, 8];

const EdgePopup: React.FC<EdgePopupProps> = ({ edge, position, onClose }) => {
  const { setEditingEdgeId } = useFlowStore.getState();
  const { getEdges, setEdges } = useReactFlow();
  const [showWidthSelector, setShowWidthSelector] = useState(false);
  const [showColorSelector, setShowColorSelector] = useState(false);
  const edges = useStore((state) => state.edges);

  // 拖拽相关状态
  const [popupPos, setPopupPos] = useState(position);
  const popupRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);
  const startMouseXRef = useRef(0);
  const startMouseYRef = useRef(0);
  const startLeftRef = useRef(0);
  const startTopRef = useRef(0);

  // 外部位置变更时同步（非拖拽中）
  useEffect(() => {
    if (!isDraggingRef.current) {
      setPopupPos(position);
    }
  }, [position.left, position.top]);

  useEffect(() => {
    return () => {
      // 组件卸载时清理事件
      window.removeEventListener("mousemove", handleMouseMove as any);
      window.removeEventListener("mouseup", handleMouseUp as any);
    };
  }, []);

  // 监听全局点击事件，实现点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 如果点击的是弹框外部，则关闭弹框
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        // 检查是否点击了边本身（避免点击边时立即关闭）
        const clickedElement = event.target as HTMLElement;
        const isEdgeClick = clickedElement.closest('.react-flow__edge') || 
                           clickedElement.closest('.react-flow__edgelabel');
        
        if (!isEdgeClick) {
          onClose();
        }
      }
    };

    // 延迟添加事件监听器，避免立即触发
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 0);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // 监听边是否仍然存在，如果边被删除则自动关闭弹框
  useEffect(() => {
    const currentEdge = edges.find(e => e.id === edge.id);
    if (!currentEdge) {
      // 边已被删除，关闭弹框
      onClose();
    }
  }, [edges, edge.id, onClose]);

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingRef.current) return;
    const deltaX = e.clientX - startMouseXRef.current;
    const deltaY = e.clientY - startMouseYRef.current;
    setPopupPos({
      left: startLeftRef.current + deltaX,
      top: startTopRef.current + deltaY,
    });
  };

  const handleMouseUp = () => {
    if (!isDraggingRef.current) return;
    isDraggingRef.current = false;
    window.removeEventListener("mousemove", handleMouseMove as any);
    window.removeEventListener("mouseup", handleMouseUp as any);
  };

  const handleDragStart: React.MouseEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    isDraggingRef.current = true;
    startMouseXRef.current = e.clientX;
    startMouseYRef.current = e.clientY;
    startLeftRef.current = popupPos.left;
    startTopRef.current = popupPos.top;

    window.addEventListener("mousemove", handleMouseMove as any);
    window.addEventListener("mouseup", handleMouseUp as any);
  };

  const targetEdge = edges.find((e) => e.id === edge.id);

  const isStraight = !!(targetEdge?.style as any)?.straight;
  const dashArray = !!(targetEdge?.style as any)?.strokeDasharray;
  const animation = !!(targetEdge?.style as any)?.animation;

  const { updateEdge } = useUpdateEdge();

  // 删除边
  const handleDelete = () => {
    const edges = getEdges();
    setEdges(edges.filter((e) => e.id !== edge.id));
    onClose();
  };

  // 编辑标签
  const handleEditLabel = () => {
    setEditingEdgeId(edge.id);
    onClose()
  };

  // 线宽选择器
  const handleWidthIconClick = () => {
    setShowWidthSelector((v) => !v);
    setShowColorSelector(false);
  };

  const handleWidthSelect = (w: number) => {
    const updateEdgeData = {
      strokeWidth: w,
    };

    updateEdge(edge.id, updateEdgeData);
    setShowWidthSelector(false);
    onClose()
  };

  // 颜色选择器
  const handleColorIconClick = () => {
    setShowColorSelector((v) => !v);
    setShowWidthSelector(false);
    
  };

  const handleColorSelect = (color: string) => {
    const updateEdgeData = {
      stroke: color,
    };

    updateEdge(edge.id, updateEdgeData);

    setShowColorSelector(false);
    onClose()
  };

  // 切换直线/曲线
  const handleToggleType = useCallback(() => {
    const targetEdge = edges.find((e) => e.id === edge.id);
    const straight = !(targetEdge?.style as any)?.straight;
    const updateEdgeData = {
      straight,
    };
    updateEdge(edge.id, updateEdgeData);
    onClose()
  }, [edges, onClose]);

  return (
    <div
      ref={popupRef}
      className="edge-popup fixed z-[9999] bg-white border border-[#e5e7ef] rounded-2xl shadow-xl flex items-center px-3"
      style={{
        left: popupPos.left,
        top: popupPos.top,
        transform: "translate(-50%, -100%)",
      }}
    >
      {/* 拖拽手柄 */}
      {/* <div
        className="mr-1 h-9 w-3 cursor-grab active:cursor-grabbing flex items-center justify-center text-[#b2b5bb]"
        onMouseDown={handleDragStart}
        // title="拖动"
      >
        <svg width="8" height="18" viewBox="0 0 8 18" xmlns="http://www.w3.org/2000/svg">
          <circle cx="2" cy="3" r="1" fill="currentColor" />
          <circle cx="6" cy="3" r="1" fill="currentColor" />
          <circle cx="2" cy="9" r="1" fill="currentColor" />
          <circle cx="6" cy="9" r="1" fill="currentColor" />
          <circle cx="2" cy="15" r="1" fill="currentColor" />
          <circle cx="6" cy="15" r="1" fill="currentColor" />
        </svg>
      </div> */}
      {/* 颜色盘 */}
      <div className="relative">
        <button className={iconBtn} title="颜色" onClick={handleColorIconClick}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
          >
            <path
              fill="currentColor"
              d="M17.5 12a1.5 1.5 0 0 1-1.5-1.5A1.5 1.5 0 0 1 17.5 9a1.5 1.5 0 0 1 1.5 1.5a1.5 1.5 0 0 1-1.5 1.5m-3-4A1.5 1.5 0 0 1 13 6.5A1.5 1.5 0 0 1 14.5 5A1.5 1.5 0 0 1 16 6.5A1.5 1.5 0 0 1 14.5 8m-5 0A1.5 1.5 0 0 1 8 6.5A1.5 1.5 0 0 1 9.5 5A1.5 1.5 0 0 1 11 6.5A1.5 1.5 0 0 1 9.5 8m-3 4A1.5 1.5 0 0 1 5 10.5A1.5 1.5 0 0 1 6.5 9A1.5 1.5 0 0 1 8 10.5A1.5 1.5 0 0 1 6.5 12M12 3a9 9 0 0 0-9 9a9 9 0 0 0 9 9a1.5 1.5 0 0 0 1.5-1.5c0-.39-.15-.74-.39-1c-.23-.27-.38-.62-.38-1a1.5 1.5 0 0 1 1.5-1.5H16a5 5 0 0 0 5-5c0-4.42-4.03-8-9-8"
            />
          </svg>
        </button>
        {showColorSelector && (
          <div className="absolute top-12 left-1/2 -translate-x-1/2 bg-white border border-[#e5e7ef] rounded-xl shadow-lg flex px-3 py-2 z-50">
            {colorList.map((color) => (
              <button
                key={color}
                className="mx-1 w-6 h-6 rounded-full border-2 flex items-center justify-center"
                style={{
                  background: color,
                  borderColor:
                    edge.style?.stroke === color ? "#3b82f6" : "#e5e7ef",
                  boxShadow:
                    edge.style?.stroke === color
                      ? "0 0 0 2px #3b82f6"
                      : undefined,
                  outline: "none",
                  transition: "box-shadow 0.15s, border-color 0.15s",
                }}
                onClick={() => handleColorSelect(color)}
                title={color}
              >
                {edge.style?.stroke === color && (
                  <svg width="14" height="14" fill="none">
                    <circle cx="7" cy="7" r="5" stroke="#fff" strokeWidth="2" />
                  </svg>
                )}
              </button>
            ))}
          </div>
        )}
      </div>
      {/* 线宽 */}
      <div className="relative">
        <button className={iconBtn} title="线宽" onClick={handleWidthIconClick}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <line
              x1="3"
              y1="6"
              x2="21"
              y2="6"
              stroke="currentColor"
              strokeWidth="1"
            />
            <line
              x1="3"
              y1="12"
              x2="21"
              y2="12"
              stroke="currentColor"
              strokeWidth="3"
            />
            <line
              x1="3"
              y1="18"
              x2="21"
              y2="18"
              stroke="currentColor"
              strokeWidth="5"
            />
          </svg>
        </button>
        {showWidthSelector && (
          <div className="absolute top-12 left-1/2 -translate-x-1/2 bg-white border border-[#e5e7ef] rounded-xl shadow-lg flex px-3 py-3.5 z-50">
            {strokeWidths.map((w) => (
              <button
                key={w}
                className={`mx-1 flex flex-col items-center group ${edge.style?.strokeWidth === w ? "text-[#3b82f6]" : "text-[#6b6f76]"}`}
                onClick={() => handleWidthSelect(w)}
                title={`${w}px`}
                style={{ transition: "color 0.15s" }}
              >
                <svg width="28" height="16">
                  <line
                    x1="4"
                    y1="8"
                    x2="24"
                    y2="8"
                    stroke='black'
                    strokeWidth={w}
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            ))}
          </div>
        )}
      </div>
      {/* T 文本 */}
      <button className={iconBtn} title="编辑标签" onClick={handleEditLabel}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
        >
          <path
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M10 19h2m0 0h2m-2 0V5m0 0H6v1m6-1h6v1"
          />
        </svg>
      </button>
      {/* 动画控制 */}
      <button
        className={iconBtn}
        title={animation ? "隐藏动画" : "显示动画"}
        onClick={() => {
          const state = !animation;
          updateEdge(edge.id, {
            animation: state,
          });
          onClose()
        }}
      >
        {!animation ? (
          // 动画开启图标
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 13 13"
            fill="none"
          >
            <path
              d="M10.0104 2.63379C8.42973 2.63379 7.11253 3.68755 6.3222 6.32196C5.53188 8.95637 4.21468 10.0101 2.63403 10.0101"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              stroke-dasharray="1.81 1.81"
            />
            <circle cx="6.5" cy="6.5" r="1.5" fill="currentColor" />
          </svg>
        ) : (
          // 动画关闭图标
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="13"
            height="13"
            viewBox="0 0 13 13"
            fill="none"
          >
            <path
              d="M10.0104 2.63379C8.42973 2.63379 7.11253 3.68755 6.3222 6.32196C5.53188 8.95637 4.21468 10.0101 2.63403 10.0101"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              stroke-dasharray="1.81 1.81"
            />
            <circle cx="6.5" cy="6.5" r="1.5" fill="currentColor" />
          </svg>
        )}
      </button>
      {/* 虚实线切换 */}
      <button
        className={iconBtn}
        title={dashArray ? "切换为实线" : "切换为虚线"}
        onClick={() => {
          const updateInfo = {
            strokeDasharray: !dashArray,
          };
          updateEdge(edge.id, updateInfo);
          onClose()
        }}
      >
        {!dashArray ? (
          // 虚线图标
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 13 13"
            fill="none"
          >
            <path
              d="M10.0104 2.63379C6.32221 6.32196 10.0104 2.63379 6.32221 6.32196C2.63403 10.0101 6.32221 6.32196 2.63403 10.0101"
              stroke="currentColor"
              strokeWidth="0.903226"
              strokeLinecap="round"
              strokeLinejoin="round"
              stroke-dasharray="1.81 1.81"
            />
          </svg>
        ) : (
          // 实线图标
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 13 13"
            fill="none"
          >
            <path
              d="M10.0104 2.63379C6.32221 6.32196 10.0104 2.63379 6.32221 6.32196C2.63403 10.0101 6.32221 6.32196 2.63403 10.0101"
              stroke="currentColor"
              strokeWidth="0.903226"
              strokeLinecap="round"
              strokeLinejoin="round"
              stroke-dasharray="1.81 1.81"
            />
          </svg>
        )}
      </button>
      {/* 曲线/直线切换 */}
      <button
        className={iconBtn}
        title={isStraight ? "切换为直线" : "切换为曲线"}
        onClick={handleToggleType}
      >
        {isStraight ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 13 13"
            fill="none"
          >
            <path
              d="M10.0104 2.63379C8.42973 2.63379 7.11253 3.68755 6.3222 6.32196C5.53188 8.95637 4.21468 10.0101 2.63403 10.0101"
              stroke="currentColor"
              strokeWidth="0.903226"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 13 13"
            fill="none"
          >
            <path
              d="M10.0104 2.63379C8.42973 2.63379 7.11253 3.68755 6.3222 6.32196C5.53188 8.95637 4.21468 10.0101 2.63403 10.0101"
              stroke="currentColor"
              strokeWidth="0.903226"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </button>

      {/* 删除 */}
      <button className={iconBtn} title="删除" onClick={handleDelete}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 11 11"
          fill="none"
        >
          <path
            d="M8.25 4.125L7.86509 7.97409C7.80677 8.55732 7.77761 8.84894 7.64523 9.06927C7.52864 9.26333 7.35718 9.4185 7.15248 9.51521C6.92007 9.625 6.627 9.625 6.04086 9.625H4.95914C4.373 9.625 4.07993 9.625 3.84752 9.51521C3.64282 9.4185 3.47136 9.26333 3.35477 9.06927C3.22239 8.84894 3.19323 8.55732 3.13491 7.97409L2.75 4.125"
            stroke="currentColor"
            strokeWidth="0.8"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M6.1875 7.10417V4.8125"
            stroke="currentColor"
            strokeWidth="0.8"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.8125 7.10417V4.8125"
            stroke="currentColor"
            strokeWidth="0.8"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M2.0625 2.97917H4.17788M4.17788 2.97917L4.35468 1.75472C4.40618 1.53156 4.59127 1.375 4.8036 1.375H6.1964C6.40873 1.375 6.59382 1.53156 6.64532 1.75472L6.82212 2.97917M4.17788 2.97917H6.82212M6.82212 2.97917H8.9375"
            stroke="currentColor"
            strokeWidth="0.8"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </div>
  );
};

export default EdgePopup;
