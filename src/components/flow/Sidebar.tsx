import React, { forwardRef } from "react";
import {
  House,
  FolderOpen,
  MessageSquare,
  FileText,
  Tag,
  Share,
  Undo2,
} from "lucide-react";
import { TabPanelControls } from "./TabPanelControls";
import { useNavigate } from "react-router-dom";
import { usePreventZoomOnly } from "@/hooks/usePreventBrowserZoom";

// Figma 装饰元素 SVG 资产
const decorativeTop =
  "/Users/<USER>/code/PDF/src/assets/figma/c7902e927bb256aa0e5a273703e091469f176b22.svg";
const decorativeBottom =
  "/Users/<USER>/code/PDF/src/assets/figma/95c4b49b81a6f3771a57466f6777e65f02dab914.svg";

export type SidebarEventType =
  | "home"
  | "note"
  | "chat"
  | "pdf"
  | "tag"
  | "card-list";
export interface SidebarProps {
  active: SidebarEventType;
  onSelect: (type: SidebarEventType) => void;
  noteOpen?: boolean;
  chatOpen?: boolean;
  pdfOpen?: boolean;
  tagOpen?: boolean;
  cardListOpen?: boolean;
}

const SidebarComponent = forwardRef<HTMLDivElement, SidebarProps>((props, ref) => {
  const {
    active,
    onSelect,
    noteOpen,
    chatOpen,
    pdfOpen,
    tagOpen,
    cardListOpen,
  } = props;
  const navigate = useNavigate();
  const sidebarRef = React.useRef<HTMLDivElement>(null);
  
  // 为 Sidebar 添加防缩放功能
  usePreventZoomOnly(sidebarRef, true);
  
  return (<div ref={sidebarRef} className="relative h-full w-16 flex-shrink-0">
    {tagOpen || cardListOpen ? (
      <div className="flex justify-center pt-6 w-[61px]">
        <button
          className="w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted text-[#666]"
          onClick={() => {
            if (tagOpen) {
              onSelect("tag");
            } else if (cardListOpen) {
              onSelect("card-list");
            }
          }}
        >
          <Undo2 />
        </button>
      </div>
    ) : (
      <>
        {/* 主侧边栏容器 */}
        <div className="absolute bg-[#f9fafc] h-[420px] left-0 rounded-[0_20px_20px_0] top-[85px] w-[61px] shadow-lg" />
        {/* 图标按钮区域 */}
        <div className="absolute left-0 top-[85px] w-[61px] flex flex-col items-center pt-6 z-10">
          <>
            <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted ${
                active === "home" ? "text-primary" : "text-[#666]"
              }`}
              onClick={() => onSelect("home")}
            >
              {/* <House /> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="46"
                height="46"
                viewBox="0 0 46 46"
                fill="none"
              >
                <path
                  opacity="0.921343"
                  d="M21.7588 12.4396C22.4904 11.8533 23.5317 11.8533 24.2627 12.4406L32.5059 19.0646C33.4505 19.8239 34 20.9709 34 22.1828V30.0002C34 32.2093 32.2091 34.0002 30 34.0002H16C13.7909 34.0002 12 32.2093 12 30.0002V22.1848C12.0001 20.9713 12.5514 19.8238 13.498 19.0646L21.7588 12.4396ZM24.2129 15.3049C23.4965 14.7583 22.5035 14.7583 21.7871 15.3049L15.1797 20.3459C14.4362 20.9134 14.0001 21.7954 14 22.7307V29.5383C14 30.9189 15.1193 32.0383 16.5 32.0383H19V29.0031C19.0002 26.7942 20.791 25.0031 23 25.0031C25.209 25.0031 26.9998 26.7942 27 29.0031V32.0383H29.5C30.8807 32.0383 32 30.9189 32 29.5383V22.7307C31.9999 21.7954 31.5638 20.9134 30.8203 20.3459L24.2129 15.3049ZM23 27.0021C21.8957 27.0021 21.0004 27.8979 21 29.0021V32.0383H25V29.0021C24.9996 27.8979 24.1043 27.0021 23 27.0021Z"
                  fill="currentColor"
                />
              </svg>
            </button>
            <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted ${
                pdfOpen ? "text-primary" : "text-[#666]"
              }`}
              onClick={() => onSelect("pdf")}
            >
              {/* <FolderOpen /> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="46"
                height="46"
                viewBox="0 0 46 46"
                fill="none"
              >
                <path
                  d="M18 34H16C13.7909 34 12 32.2091 12 30V16C12 13.7909 13.7909 12 16 12H18V34ZM34 30C34 32.2091 32.2091 34 30 34H20V19H34V30ZM30 12C32.2091 12 34 13.7909 34 16V17H20V12H30Z"
                  fill="currentColor"
                />
              </svg>
            </button>
            <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted ${
                chatOpen ? "text-primary" : "text-[#666]"
              }`}
              onClick={() => onSelect("chat")}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <g
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                >
                  <path d="M14.17 20.89c4.184-.277 7.516-3.657 7.79-7.9c.053-.83.053-1.69 0-2.52c-.274-4.242-3.606-7.62-7.79-7.899a33 33 0 0 0-4.34 0c-4.184.278-7.516 3.657-7.79 7.9a20 20 0 0 0 0 2.52c.1 1.545.783 2.976 1.588 4.184c.467.845.159 1.9-.328 2.823c-.35.665-.526.997-.385 1.237c.14.24.455.248 1.084.263c1.245.03 2.084-.322 2.75-.813c.377-.279.566-.418.696-.434s.387.09.899.3c.46.19.995.307 1.485.34c1.425.094 2.914.094 4.342 0" />
                  <path d="m7.5 15l1.842-5.526a.694.694 0 0 1 1.316 0L12.5 15m3-6v6m-7-2h3" />
                </g>
              </svg>
            </button>
            {/* <FileText /> */}
            {/* <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted ${
                noteOpen ? "text-primary" : "text-[#666]"
              }`}
              onClick={() => onSelect("note")}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="46"
                height="46"
                viewBox="0 0 46 46"
                fill="none"
              >
                <path
                  d="M30 12C32.2091 12 34 13.7909 34 16V30C34 32.2091 32.2091 34 30 34H16C13.7909 34 12 32.2091 12 30V16C12 13.7909 13.7909 12 16 12H30ZM17 14C15.3431 14 14 15.3431 14 17V29C14 30.6569 15.3431 32 17 32H29C30.6569 32 32 30.6569 32 29V17C32 15.3431 30.6569 14 29 14H17ZM22 26C22.5523 26 23 26.4477 23 27C23 27.5523 22.5523 28 22 28H18C17.4477 28 17 27.5523 17 27C17 26.4477 17.4477 26 18 26H22ZM25 22C25.5523 22 26 22.4477 26 23C26 23.5523 25.5523 24 25 24H18C17.4477 24 17 23.5523 17 23C17 22.4477 17.4477 22 18 22H25ZM28 18C28.5523 18 29 18.4477 29 19C29 19.5523 28.5523 20 28 20H18C17.4477 20 17 19.5523 17 19C17 18.4477 17.4477 18 18 18H28Z"
                  fill="currentColor"
                />
              </svg>
            </button> */}
            <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted text-[#666]`}
              onClick={() => navigate("/tag-management")}
              title="标签管理"
            >
              <Tag />
            </button>
            <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted text-[#666]`}
              onClick={() => navigate("/card-list")}
              title="卡片列表"
            >
              {/* <Share /> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                viewBox="0 0 30 30"
                fill="none"
              >
                <path
                  d="M19.5042 20.1086V22.5466C19.5042 23.1932 19.2473 23.8133 18.7901 24.2705C18.3329 24.7278 17.7127 24.9846 17.0661 24.9846H7.31406C6.66745 24.9846 6.04733 24.7278 5.59012 24.2705C5.1329 23.8133 4.87604 23.1932 4.87604 22.5466V12.7945C4.87604 12.1479 5.1329 11.5278 5.59012 11.0706C6.04733 10.6134 6.66745 10.3565 7.31406 10.3565H9.75208M9.75208 7.91849C9.75208 7.27189 10.0089 6.65177 10.4662 6.19455C10.9234 5.73733 11.5435 5.48047 12.1901 5.48047H21.9422C22.5888 5.48047 23.2089 5.73733 23.6661 6.19455C24.1233 6.65177 24.3802 7.27189 24.3802 7.91849V17.6706C24.3802 18.3172 24.1233 18.9373 23.6661 19.3945C23.2089 19.8517 22.5888 20.1086 21.9422 20.1086H12.1901C11.5435 20.1086 10.9234 19.8517 10.4662 19.3945C10.0089 18.9373 9.75208 18.3172 9.75208 17.6706V7.91849Z"
                  stroke="currentColor"
                  strokeWidth="1.82577"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            <button
              className={`w-12 h-12 mb-4 border-none bg-transparent rounded-xl flex items-center justify-center cursor-pointer text-[22px] hover:bg-muted ${
                'text-[#666]'
              }`}
              onClick={() => {
                navigate("/tools");
              }}
            >
              {/* <Share /> */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="26"
                height="25"
                viewBox="0 0 30 28"
                fill="none"
              >
                <circle
                  cx="15"
                  cy="16"
                  r="11"
                  stroke="#717172"
                  strokeWidth="2"
                />
                <circle
                  cx="5"
                  cy="23"
                  r="4.25"
                  fill="white"
                  stroke="#717172"
                  strokeWidth="1.5"
                />
                <circle
                  cx="25"
                  cy="23"
                  r="4.25"
                  fill="white"
                  stroke="#717172"
                  strokeWidth="1.5"
                />
                <circle
                  cx="15"
                  cy="5"
                  r="4.25"
                  fill="white"
                  stroke="#717172"
                  strokeWidth="1.5"
                />
              </svg>
            </button>
          </>
        </div>

        {/* TabPanel 控制区域 */}
        <TabPanelControls />
      </>
    )}
  </div>)
});

SidebarComponent.displayName = 'Sidebar';

export const Sidebar = SidebarComponent;
