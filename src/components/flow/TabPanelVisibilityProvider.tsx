import React, { useState, ReactNode } from "react";
import { TabPanelVisibilityContext } from "./TabPanelControls";

interface TabPanelVisibilityProviderProps {
  children: ReactNode;
}

export const TabPanelVisibilityProvider: React.FC<TabPanelVisibilityProviderProps> = ({ children }) => {
  const [hiddenPanels, setHiddenPanels] = useState<Set<string>>(new Set());

  const togglePanelVisibility = (panelId: string) => {
    setHiddenPanels(prev => {
      const newSet = new Set(prev);
      if (newSet.has(panelId)) {
        newSet.delete(panelId);
      } else {
        newSet.add(panelId);
      }
      return newSet;
    });
  };

  return (
    <TabPanelVisibilityContext.Provider value={{ hiddenPanels, togglePanelVisibility }}>
      {children}
    </TabPanelVisibilityContext.Provider>
  );
};