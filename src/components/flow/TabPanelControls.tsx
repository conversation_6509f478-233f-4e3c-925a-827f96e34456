import React from "react";
import { Eye, EyeOff, X, FileText, Layers, ChevronUp, ChevronDown, Copy, Maximize2 } from "lucide-react";
import { usePanelOpenStore, TabPanelInfo } from "@/store/panel-open-store";
import { useWorkerSpaceStore } from "@/store/workerspace-store/store";
import { Tooltip } from "antd";
import { usePdfStore } from "@/store/pdf-store";

// 导出隐藏状态的 Context
export const TabPanelVisibilityContext = React.createContext<{
  hiddenPanels: Set<string>;
  togglePanelVisibility: (panelId: string) => void;
}>({
  hiddenPanels: new Set(),
  togglePanelVisibility: () => {},
});

export const useTabPanelVisibility = () => React.useContext(TabPanelVisibilityContext);

export const TabPanelControls: React.FC = () => {
  const { tabPanels, removeTabPanel } = usePanelOpenStore();
  const wid = useWorkerSpaceStore(state => state.wid);
  const { panels } = usePdfStore();
  const { hiddenPanels, togglePanelVisibility } = useTabPanelVisibility();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const [hoveredPanel, setHoveredPanel] = React.useState<string | null>(null);

  // 关闭 Panel
  const handleClosePanel = (panelId: string) => {
    removeTabPanel(panelId);
    // 从隐藏列表中移除
    if (hiddenPanels.has(panelId)) {
      togglePanelVisibility(panelId);
    }
  };

  // 如果没有 TabPanel，不显示控制区域
  if (!tabPanels || tabPanels.size === 0) {
    return null;
  }

  // 将 Map 转换为数组进行渲染（按项目过滤）
  const panelList = Array.from(tabPanels.values()).filter(p => !p.projectId || p.projectId === wid);
  // 若当前项目没有任何面板，则完全不渲染
  if (panelList.length === 0) return null;
  const visibleCount = panelList.filter(p => !hiddenPanels.has(p.id)).length;
  const hiddenCount = panelList.filter(p => hiddenPanels.has(p.id)).length;

  return (
    <div className="absolute left-2 bottom-[120px] flex flex-col items-start z-20">
      {/* 主容器 */}
      <div className={`
        bg-background border border-border rounded-xl shadow-lg
        transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-12' : 'w-56'}
      `}>
        {/* 顶部标题栏 */}
        <div className="px-2 py-2 border-b border-border flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Layers size={14} className="text-foreground" />
            {!isCollapsed && (
              <span className="text-[10px] font-medium text-foreground">
                {panelList.length}
              </span>
            )}
          </div>
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-0.5 hover:bg-accent rounded transition-colors"
          >
            {isCollapsed ? 
              <ChevronUp size={12} className="text-muted-foreground" /> : 
              <ChevronDown size={12} className="text-muted-foreground" />
            }
          </button>
        </div>

        {/* Panel 列表容器 */}
        {!isCollapsed && (
          <div className="p-3 max-h-[320px] overflow-y-auto scrollbar-custom">
            <div className="flex flex-col gap-2">
              {panelList.map((panel: TabPanelInfo) => {
                const isHidden = hiddenPanels.has(panel.id);
                const panelState = panels.get(panel.id);
                const tabCount = panelState?.tabItems?.length || 1;
                
                return (
                  <div
                    key={panel.id}
                    className="relative"
                    onMouseEnter={() => setHoveredPanel(panel.id)}
                    onMouseLeave={() => setHoveredPanel(null)}
                  >
                    {/* Panel 卡片 */}
                    <div className={`
                      relative rounded-lg transition-all duration-200 border
                      ${isHidden 
                        ? 'bg-secondary/50 border-border/50 opacity-70' 
                        : 'bg-background border-border hover:border-primary/30 hover:shadow-sm'
                      }
                      ${hoveredPanel === panel.id ? 'ring-1 ring-primary/20 shadow-md' : ''}
                    `}>
                      {/* 主要内容区 */}
                      <div className="p-3">
                        {/* 图标和标题 */}
                        <div className="flex items-start gap-2">
                          <div className={`
                            w-9 h-9 rounded-lg flex items-center justify-center shrink-0
                            ${isHidden 
                              ? 'bg-muted text-muted-foreground' 
                              : 'bg-primary/10 text-primary'
                            }
                          `}>
                            <FileText size={16} />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            {/* 标题 */}
                            <div className="text-[13px] font-medium text-foreground truncate leading-snug" title={panel.label}>
                              {panel.label}
                            </div>
                            
                            {/* 标签数量和状态 */}
                            <div className="flex items-center gap-2 mt-1">
                              <span className="text-[11px] text-muted-foreground">
                                {tabCount} {tabCount > 1 ? '标签页' : '标签页'}
                              </span>
                              {isHidden && (
                                <span className="text-[10px] text-muted-foreground bg-secondary px-1.5 py-0.5 rounded">
                                  已隐藏
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* 状态指示器 */}
                        <div className="absolute top-2 right-2">
                          <div className={`
                            w-2 h-2 rounded-full animate-pulse
                            ${isHidden ? 'bg-muted-foreground' : 'bg-green-500'}
                          `} />
                        </div>
                      </div>

                      {/* 悬浮操作栏 */}
                      {hoveredPanel === panel.id && (
                        <div className="absolute inset-x-0 bottom-0 bg-background/95 backdrop-blur-sm rounded-b-lg border-t border-border">
                          <div className="flex items-center justify-around px-2 py-1.5">
                            {/* 显示/隐藏 */}
                            <Tooltip title={isHidden ? "显示" : "隐藏"} placement="top">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  togglePanelVisibility(panel.id);
                                }}
                                className={`
                                  p-1.5 rounded transition-colors
                                  ${isHidden 
                                    ? 'hover:bg-accent text-muted-foreground hover:text-foreground' 
                                    : 'hover:bg-accent text-foreground'
                                  }
                                `}
                              >
                                {isHidden ? <Eye size={12} /> : <EyeOff size={12} />}
                              </button>
                            </Tooltip>

                            {/* 最大化 */}
                            <Tooltip title="最大化" placement="top">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // TODO: 实现最大化功能
                                }}
                                className="p-1.5 rounded hover:bg-accent text-foreground transition-colors"
                              >
                                <Maximize2 size={12} />
                              </button>
                            </Tooltip>

                            {/* 复制 */}
                            <Tooltip title="复制Panel" placement="top">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // TODO: 实现复制功能
                                }}
                                className="p-1.5 rounded hover:bg-accent text-foreground transition-colors"
                              >
                                <Copy size={12} />
                              </button>
                            </Tooltip>

                            {/* 关闭 */}
                            <Tooltip title="关闭" placement="top">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleClosePanel(panel.id);
                                }}
                                className="p-1.5 rounded hover:bg-red-50 text-red-600 transition-colors"
                              >
                                <X size={12} />
                              </button>
                            </Tooltip>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 底部状态栏 */}
        {!isCollapsed && panelList.length > 0 && (
          <div className="px-2 py-1.5 border-t border-border">
            <div className="flex items-center justify-between text-[9px] text-muted-foreground">
              <span>{visibleCount} 显示</span>
              {hiddenCount > 0 && (
                <>
                  <span className="mx-1">·</span>
                  <span>{hiddenCount} 隐藏</span>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 折叠状态下的快速指示器 */}
      {isCollapsed && (
        <div className="mt-2 flex flex-col items-center gap-1">
          <Tooltip title={`${visibleCount} 个显示的Panel`} placement="right">
            <div className="w-12 h-8 bg-background border border-border rounded-lg flex items-center justify-center cursor-pointer hover:bg-accent transition-colors">
              <div className="flex flex-col items-center gap-0.5">
                <Eye size={10} className="text-foreground" />
                <span className="text-[9px] text-foreground font-medium">
                  {visibleCount}
                </span>
              </div>
            </div>
          </Tooltip>
          
          {hiddenCount > 0 && (
            <Tooltip title={`${hiddenCount} 个隐藏的Panel`} placement="right">
              <div className="w-12 h-8 bg-secondary/50 border border-border rounded-lg flex items-center justify-center cursor-pointer hover:bg-secondary transition-colors opacity-70">
                <div className="flex flex-col items-center gap-0.5">
                  <EyeOff size={10} className="text-muted-foreground" />
                  <span className="text-[9px] text-muted-foreground font-medium">
                    {hiddenCount}
                  </span>
                </div>
              </div>
            </Tooltip>
          )}
        </div>
      )}
    </div>
  );
};

export default TabPanelControls;