import { useCallback } from "react";
import { NodeType } from "@/store/flow-store.ts";
import { CreateNodeType } from "@/api/canvas";
import { getWsId } from "@/tools/params.ts";
import { useNodeLayout } from "@/components/flow/hooks/node-layout.ts";
import {
  getDefaultNodeSize,
  calculateNodeHeight,
} from "@/components/flow/constants/node-defaults";
import { useReactFlow, XYPosition } from "@xyflow/react";
import { NODE_TYPE_MAPS } from "@/components/canvas/components/node";
import { createNode } from "@/components/canvas/lib/node-factory";
import { useReactFlowInstanceStore } from "@/store/reactflow-instance-store";

const DEFAULT_COLOR = "#FFE28F";
const useNodeAdd = () => {
  // const {addNodes, setCenter, addEdges} = useFlowStore((state) => ({
  //     addNodes: state.addNodes,
  //     setCenter: state.setCenter,
  //     addEdges: state.addEdges,
  // }))
  const { addNodes, setCenter, addEdges } = useReactFlow();
  const { calculateNodePosition } = useNodeLayout();
  // 新增节点
  // const handleAddNode = useCallback(async (prop?: Partial<CreateNodeType> & {
  //     setCenter?: boolean,
  //     refInfo?: Record<any,any>,
  //     offsetIndex?: number,
  //     customWidth?: number,
  //     customHeight?: number,
  //     autoHeight?: boolean
  // }, strokeDasharray?: boolean) => {
  //     // 如果传入了具体的 x, y 坐标，使用传入的坐标；否则计算节点位置
  //     let x: number, y: number;
  //     if (prop?.x !== undefined && prop?.y !== undefined) {
  //         x = prop.x;
  //         y = prop.y;
  //     } else {
  //         const calculatedPosition = calculateNodePosition(prop?.pids);
  //         x = calculatedPosition.x;
  //         y = calculatedPosition.y + (prop?.offsetIndex ?? 0) * 40;
  //     }

  //     const defaultNode = {
  //         wid: getWsId(),
  //         content: "",
  //         pids: [],
  //         color: DEFAULT_COLOR.slice(1),
  //         x: x,
  //         y: y,
  //         type: "default" as CreateNodeType["type"],
  //     }

  //     try {
  //         const isCenter = prop?.setCenter ?? false
  //         delete prop?.setCenter
  //         const node = {...defaultNode, ...prop}
  //         // const res = await createNode({
  //         //     ...node,
  //         //     style: {
  //         //         strokeDasharray: strokeDasharray ,
  //         //     }
  //         // })

  //         // 计算节点尺寸
  //         const defaultSize = getDefaultNodeSize();
  //         let nodeWidth = prop?.customWidth || defaultSize.width;
  //         let nodeHeight = prop?.customHeight || defaultSize.height;

  //         // 如果启用了自动高度计算
  //         if (prop?.autoHeight) {
  //             nodeHeight = calculateNodeHeight(node.content);
  //         }

  //         const newNode = {
  //             id: (res.data as any).nid,
  //             type: NodeType.markdownNode,
  //             position: {x: node.x, y: node.y},
  //             data: {
  //                 title: "",
  //                 content: node.content,
  //                 color: `#${node.color}`,
  //             },
  //             selected: true,
  //             width: nodeWidth,
  //             height: nodeHeight,
  //             measured: {
  //                 width: nodeWidth,
  //                 height: nodeHeight,
  //             },
  //         }
  //         addNodes([newNode])
  //         const newEdges = (res.data as any).edges || [];

  //         // const newEdges = prop?.pids?.map((pid) => ({
  //         //     id: `tmp-${newNode.id}-${pid}`,
  //         //     source: pid,
  //         //     target: newNode.id,
  //         // })) || [];
  //         if (newEdges.length > 0) {
  //             addEdges(newEdges)
  //         }
  //         if (isCenter) {
  //             setCenter(newNode.position.x, newNode.position.y)
  //         }
  //         return res
  //     } catch (e) {
  //         console.log(e)
  //         return Promise.reject(e)
  //     }
  // }, [addNodes, setCenter, calculateNodePosition])
  const handleAddNode = useCallback(
    ({
      position,
      data,
    }: {
      position: XYPosition;
      data: {
        content: string;
      };
    }) => {
      return;
    },
    [addNodes]
  );
  return {
    handleAddNode,
  };
};

const useAddNode = () => {
  const { addNodes, setCenter,getViewport } = useReactFlow();
  const { calculateNodePosition } = useNodeLayout();

  const { instance } = useReactFlowInstanceStore(); // PDF 相关的操作需要使用全局实例
  // 屏幕正中间添加节点，并且提添加内容
  const addNodeFromChat = useCallback(
    (content: string) => {
      // 使用自动布局计算节点位置
      const defaultPosition = calculateNodePosition([]);
      const nodeHeight = calculateNodeHeight(content);
      
      const newNode = createNode(NODE_TYPE_MAPS.markdown, {
        x: defaultPosition.x,
        y: defaultPosition.y,
      });
      
      addNodes([
        {
          ...newNode,
          height: nodeHeight,
          data: {
            ...newNode.data,
            content: content,
          },
        },
      ]);
      const zoom = getViewport().zoom;
      setCenter(newNode.position.x, newNode.position.y, { zoom: zoom });
    },
    [addNodes, calculateNodePosition]
  );
  // PDF 高亮添加节点
  const addNodeFromHighlight = useCallback(
    ({
      position,
      data,
    }: {
      position: XYPosition;
      data: {
        content: string;
        color: string;
        aid: string;
        mark: string;
        mid: string;
        setCenter: boolean;
        autoHeight: boolean;
      };
    }) => {
      const defaultNode = calculateNodePosition([]);
      const nodeHeight = calculateNodeHeight(data.content);

      const node = createNode(NODE_TYPE_MAPS.markdown, {
        x: defaultNode.x,
        y: defaultNode.y,
      });
      const newNode = {
        ...node,
        height: nodeHeight,
        data: {
          ...node.data,
          ...data,
        },
      };
      instance?.addNodes([newNode]);
      const zoom = instance?.getViewport().zoom;
      instance?.setCenter(newNode.position.x, newNode.position.y, { zoom: zoom });
      return newNode;
    },
    [addNodes]
  );
  const addNodeFromFlow = useCallback(
    ({
      position,
      data,
    }: {
      position: XYPosition;
      data: {
        content: string;
      };
    }) => {
      const node = createNode(NODE_TYPE_MAPS.markdown, position);
      const newNode = {
        ...node,
        data: {
          ...node.data,
          ...data,
        },
      };
      const zoom = instance?.getViewport().zoom;
      addNodes([newNode]);
      setCenter(newNode.position.x, newNode.position.y, { zoom: zoom });
      return newNode;
    },
    [addNodes]
  );
  // 添加自由节点
  const addNodeFromFree = useCallback(
    ({
      position,
      data,
    }: {
      position: XYPosition;
      data: {
        content?: string;
        type?: string;
        title?: string;
        [key: string]: any;
      };
    }) => {
      // 根据数据类型决定创建哪种节点
      let nodeType = NODE_TYPE_MAPS.markdown;
      if (data.type === "llm") {
        nodeType = NODE_TYPE_MAPS.llm;
      } else if (data.type === "endNode") {
        nodeType = NODE_TYPE_MAPS.llmEndNode;
      }

      const node = createNode(nodeType, position);
      const newNode = {
        ...node,
        data: {
          ...node.data,
          ...data,
        },
      };
      addNodes([newNode]);
      // const zoom = instance?.getViewport().zoom;

      // setCenter(newNode.position.x, newNode.position.y, { zoom: zoom });
      return newNode;
    },
    [addNodes]
  );
  
  // 文本文件添加节点
  const addNodeFromText = useCallback(
    ({
      content,
      aid,
      color,
      textPosition,
    }: {
      content: string;
      aid?: string;
      color?: string;
      textPosition?: any; // 文本位置信息
    }) => {
      const position = calculateNodePosition([]);
      const nodeHeight = calculateNodeHeight(content);
      
      const node = createNode(NODE_TYPE_MAPS.markdown, position);
      const newNode = {
        ...node,
        height: nodeHeight,
        data: {
          ...node.data,
          content: content,
          color: color || "#FFE28F",
          aid: aid || 'text-editor',
          textPosition: textPosition, // 将文本位置信息存储到节点数据中
        },
      };
      
      addNodes([newNode]);
      const zoom = instance?.getViewport().zoom || 1;
      setCenter(newNode.position.x, newNode.position.y, { zoom: zoom });
      return newNode;
    },
    [addNodes, calculateNodePosition, setCenter, instance]
  );
  
  return {
    addNodeFromChat,
    addNodeFromHighlight,
    addNodeFromFlow,
    addNodeFromFree,
    addNodeFromText,
  };
};

export { useNodeAdd, useAddNode };
