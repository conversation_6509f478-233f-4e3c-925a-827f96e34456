import { useEffect } from "react";
// import { useFlowStore } from "@/store/flow-store";
import { useNodeAdd } from "@/components/flow/hooks/node-add";
import { useReactFlow } from "@xyflow/react";

export const useTabAddNode = () => {
    const { handleAddNode } = useNodeAdd();
    const {getNodes} = useReactFlow()

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (
                e.key === "Tab" &&
                !e.defaultPrevented &&
                document.activeElement?.tagName !== "INPUT" &&
                document.activeElement?.tagName !== "TEXTAREA"
            ) {
                const selectedNodes = getNodes().filter(node=>node.selected)

                if (selectedNodes.length > 0) {
                    e.preventDefault();
                    selectedNodes.forEach((node, idx) => {
                        handleAddNode({ position: {x: 0, y: 0}, data: { content: "" } });
                    });
                }
            }
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [handleAddNode]);
};