import {useCallback} from "react";
import {getNodeHighlight, openPdfTab, usePdfStore} from "@/store/pdf-store.ts";
import {CustomNode, useFlowStore} from "@/store/flow-store.ts";
import {OnSelectionChangeParams, useReactFlow} from "@xyflow/react";

const useFlowSelection = () => {
    const{getNodes} = useReactFlow()
    // const {setSelectedNodes} = useFlowStore((state) => ({
    //     setSelectedNodes: state.setSelectedNodes,
    // }))
    const onSelectionChange = useCallback((params: OnSelectionChangeParams) => {
        // setSelectedNodes(params.nodes as CustomNode[])
        if (params.nodes.length === 1) {
            const node = params.nodes[0] as CustomNode;
            // 点击高亮选中的节点，无需滚动到高亮位置
            if (node.selectedFromHighlight && node.selectedFromHighlight === true) {
                return
            }
            const highlights = getNodeHighlight([node.id])
            if (highlights.length === 0) {
                return
            }
            const highlight = highlights[0]
            const activeAid = usePdfStore.getState().activeAid
            if (activeAid === highlight.aid) {
                usePdfStore.getState().pdfs.get(highlight.aid)?.pdfHighlighterUtils?.scrollToHighlight(highlight)
            } else {
                // 打开PDF
                openPdfTab(highlight.aid);
                setTimeout(() => {
                    usePdfStore.getState().pdfs.get(highlight.aid)?.pdfHighlighterUtils?.scrollToHighlight(highlight);
                }, 500);
            }
        }
    }, [])
    return {
        onSelectionChange,
    }
}

export {useFlowSelection}