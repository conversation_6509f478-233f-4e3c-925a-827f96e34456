import { useFlowStore } from "@/store/flow-store";
import React, { useState, useCallback } from "react";
import { useNodeAdd } from "@/components/flow/hooks/node-add";
import { useNodeChat } from "@/components/flow/hooks/node-chat";
import { usePanelOpenStore } from "@/store/panel-open-store";
import { useReactFlow } from "@xyflow/react";
import { useNodeDelete } from "./node-delete";
import { useNodeNote } from "@/components/flow/hooks/node-note";
import { message } from "antd";
import TagSearch from "../../tag-management/tag-search";
import { useNavigate } from "react-router-dom";

export const useNodeContextMenu = () => {
    const navigate = useNavigate();
    const [menuState, setMenuState] = useState<{
        open: boolean;
        x: number;
        y: number;
        nodeId?: string;
        insertNodeVisble?: boolean;
        inserChildVisble?: boolean;
        node?: Record<any, any>;
        multiSelected: boolean;
    }>({ open: false, x: 0, y: 0, multiSelected: false });

    // 标签管理弹窗状态
    const [tagModalState, setTagModalState] = useState<{
        visible: boolean;
        nodeId?: string;
        node?: Record<any, any>;
    }>({ visible: false });

    const { insertNodeVisble = false, inserChildVisble = false, } = menuState

    const { handleAddNode } = useNodeAdd()
    const { handleChat } = useNodeChat()
    // const { importChatToNotepad } = useChatPad(); 
    const { notePanelOpen, toggleNotePanel } = usePanelOpenStore();
    const { screenToFlowPosition } = useReactFlow();

    const { updateNodes } = useFlowStore((state) => ({
        updateNodes: state.updateNodes,
        nodes: state.nodes,
    }))

    const { handleDeleteNode } = useNodeDelete()
    // const { setInsertContent } = useNoteStore();
    // 记事本
    const { handleCopyToNotebook } = useNodeNote()

    // 右键触发
    const onNodeContextMenu = useCallback(
        (event: React.MouseEvent, node?: any) => {
            event.preventDefault();
            event?.stopPropagation()

            const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
            // 多选时不改变选中状态
            if (node) {
                let keepMultiSelect = false;
                if (selectedNodes.length > 1 && selectedNodes.some(n => n.id === node.id)) {
                    keepMultiSelect = true;
                }
                if (!keepMultiSelect) {
                    updateNodes(
                        [
                            ...useFlowStore.getState().nodes.map(n => ({ id: n.id, selected: n.id === node.id })),
                        ]
                    );
                }
                setMenuState({
                    open: true,
                    x: event.clientX,
                    y: event.clientY,
                    nodeId: node?.id,
                    inserChildVisble: true,
                    node,
                    multiSelected: keepMultiSelect || selectedNodes.length > 1
                });
            } else {
                // 只有在非多选状态下才清空选中
                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                if (selectedNodes.length <= 1) {
                    updateNodes(
                        [
                            ...useFlowStore.getState().nodes.map(n => ({ id: n.id, selected: false })),
                        ]
                    );
                }
                setMenuState({
                    open: true,
                    x: event.clientX,
                    y: event.clientY,
                    insertNodeVisble: true,
                    multiSelected: selectedNodes.length > 1
                });
            }
        },
        [setMenuState]
    );

    // 关闭菜单
    const closeMenu = useCallback(() => {
        setMenuState((s) => ({ ...s, open: false }));
    }, []);

    // 标签管理弹窗处理函数
    const handleTagModalCancel = useCallback(() => {
        setTagModalState({ visible: false });
    }, []);

    const handleTagModalOk = useCallback((selectedTags: string[]) => {
        // 这里应该调用API保存节点的标签
        console.log('节点标签已更新:', { nodeId: tagModalState.nodeId, selectedTags });
        
        // 更新节点数据中的标签
        if (tagModalState.nodeId) {
            const { updateNodes } = useFlowStore.getState();
            updateNodes([
                {
                    id: tagModalState.nodeId,
                    data: {
                        ...tagModalState.node?.data,
                        tags: selectedTags
                    }
                }
            ]);
        }
        
        setTagModalState({ visible: false });
        message.success('标签已更新');
    }, [tagModalState.nodeId, tagModalState.node]);

    // 菜单 DOM
    const MENU_HEIGHT = 580; // 菜单高度
    const MENU_WIDTH = 200; // 菜单宽度
    const calcMenuPosition = (x: number, y: number) => {
        const winH = window.innerHeight;
        const winW = window.innerWidth;
        let top = y;
        let left = x;
        if (y + MENU_HEIGHT > winH) {
            top = Math.max(0, winH - MENU_HEIGHT - 10);
        }
        if (x + MENU_WIDTH > winW) {
            left = Math.max(0, winW - MENU_WIDTH - 10);
        }
        return { top, left };
    };

    const ContextMenuDom = menuState.open ? (
        (() => {
            const { top, left } = calcMenuPosition(menuState.x, menuState.y);
            return (
                <div
                    className="flow-context-menu"
                    style={{
                        position: "fixed",
                        left,
                        top,
                        zIndex: 9999,
                        background: "#fff",
                        borderRadius: 8,
                        boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                        padding: "8px 0",
                        minWidth: 180,
                    }}
                    onContextMenu={(e) => e.preventDefault()}
                    onClick={(e) => e?.stopPropagation()}
                    onMouseDown={(e) => e?.stopPropagation()}
                >
                    <style>{`
                        .flow-context-menu-item {
                            transition: background 0.15s;
                        }
                        .flow-context-menu-item:not(.disabled):hover {
                            background: #f0f3fa;
                        }
                        .flow-context-menu-item.disabled {
                            pointer-events: none;
                        }
                    `}</style>
                    <div className={`flow-context-menu-item${insertNodeVisble && !menuState.multiSelected ? '' : ' disabled'}`}
                        style={{ color: insertNodeVisble && !menuState.multiSelected ? '' : "#bbb", padding: "8px 16px", cursor: insertNodeVisble && !menuState.multiSelected ? 'pointer' : 'not-allowed' }}
                        onClick={() => {
                            if (insertNodeVisble && !menuState.multiSelected) {
                                // 将屏幕坐标转换为流坐标
                                const position = screenToFlowPosition({ x: menuState.x, y: menuState.y });
                                handleAddNode({ position: position, data: { content: "" } })
                                closeMenu()
                            }
                        }}
                    >插入自由节点</div>
                    <div className={`flow-context-menu-item${inserChildVisble && menuState?.nodeId ? '' : ' disabled'}`}
                        style={{ color: inserChildVisble && menuState?.nodeId ? '' : "#bbb", padding: "8px 16px", cursor: inserChildVisble && menuState?.nodeId ? 'pointer' : 'not-allowed' }}
                        onClick={() => {
                            if (menuState?.nodeId && inserChildVisble) {
                                handleAddNode({ position: {x: 0, y: 0}, data: { content: "" } })
                                closeMenu()
                            }
                        }}
                    >插入子主题</div>
                    <div className={`flow-context-menu-item${menuState.multiSelected ? '' : ' disabled'}`}
                        style={{ color: menuState.multiSelected ? '' : '#bbb', padding: '8px 16px', cursor: menuState.multiSelected ? 'pointer' : 'not-allowed' }}
                        onClick={() => {
                            if (menuState.multiSelected) {
                                // 生成概括节点逻辑
                                // 这里可以调用 handleAddNode({ pids: 选中的节点id数组 })
                                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                                handleAddNode({ position: {x: 0, y: 0}, data: { content: "" } });
                                closeMenu();
                            }
                        }}
                    >生成概括节点</div>
                    <div className="flow-context-menu-item" style={{ borderTop: "1px solid #eee", margin: "4px 0" }} />
                    <div className={`flow-context-menu-item${menuState.multiSelected || menuState?.nodeId ? '' : ' disabled'}`}
                        style={{ padding: "8px 16px", cursor: menuState.multiSelected || menuState?.nodeId ? 'pointer' : 'not-allowed', color: menuState.multiSelected || menuState?.nodeId ? undefined : '#bbb' }}
                        onClick={() => {
                            if (menuState.multiSelected) {
                                // 多选批量 ToChat
                                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                                const hasContent = selectedNodes.some((n) => !!n?.data?.content?.trim())
                                if (!hasContent) {
                                    message.error('当前节点为空')
                                    closeMenu();
                                    return
                                }

                                selectedNodes.forEach(n => {
                                    if (n?.data?.content?.trim()) {
                                        handleChat?.(n.id)
                                    }
                                });
                                closeMenu();
                            } else if (menuState?.nodeId) {
                                if (!menuState?.node?.data?.content?.trim()) {
                                    message.error('当前节点为空')
                                    closeMenu();
                                    return
                                }
                                handleChat?.(menuState?.nodeId)
                                closeMenu()
                            }
                        }}
                    >ToChat</div>
                    {menuState.multiSelected ? <div className={`flow-context-menu-item${menuState.multiSelected ? '' : ' disabled'}`}
                        style={{ padding: "8px 16px", cursor: menuState.multiSelected ? 'pointer' : 'not-allowed', color: menuState.multiSelected ? undefined : '#bbb' }}
                        onClick={() => {
                            if (menuState.multiSelected) {
                                // 多选批量 ToNotePad
                                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                                const hasContent = selectedNodes.some((n) => !!n?.data?.content)
                                if (!hasContent) {
                                    message.error('当前节点为空')
                                    closeMenu();
                                    return
                                }

                                handleCopyToNotebook(selectedNodes.map(n => n.id))
                                closeMenu();
                            }
                        }}
                    >ToNotePad</div>
                        :
                        <div className={`flow-context-menu-item${menuState.multiSelected ? ' disabled' : ''}`}
                            style={{ padding: "8px 16px", cursor: menuState.multiSelected ? 'not-allowed' : 'pointer', color: menuState.multiSelected ? '#bbb' : undefined }}
                            onClick={() => {
                                if (!menuState.multiSelected) {
                                    if (!notePanelOpen) {
                                        toggleNotePanel(true);
                                    }
                                    const title = menuState?.node?.data?.title
                                    const content = menuState?.node?.data?.content

                                    if (title || content) {
                                        if (menuState?.nodeId) {
                                            handleCopyToNotebook([menuState?.nodeId])
                                        }
                                    } else {
                                        message.error('当前节点为空')
                                    }
                                    closeMenu();
                                }
                            }}
                        >ToNotePad</div>
                    }

                    <div className="flow-context-menu-item disabled" style={{ padding: "8px 16px", cursor: 'not-allowed' }}>一键编制</div>
                    <div className="flow-context-menu-item disabled" style={{ padding: "8px 16px", cursor: 'not-allowed' }}>导出为png</div>
                    <div className="flow-context-menu-item disabled" style={{ padding: "8px 16px", color: '#bbb', cursor: 'not-allowed' }}>复制</div>
                    <div className="flow-context-menu-item disabled" style={{ padding: "8px 16px", color: '#bbb', cursor: 'not-allowed' }}>剪切</div>
                    <div className="flow-context-menu-item disabled" style={{ padding: "8px 16px", color: '#bbb', cursor: 'not-allowed' }}>粘贴</div>
                    <div className={`flow-context-menu-item${menuState?.nodeId ? '' : ' disabled'}`}
                        style={{ padding: "8px 16px", cursor: menuState?.nodeId ? 'pointer' : 'not-allowed', color: menuState?.nodeId ? '' : '#bbb' }}
                        onClick={() => {
                            // 导航到标签管理页面
                            navigate('/tag-management');
                            closeMenu();
                        }}
                    >标签管理</div>
                    <div className="flow-context-menu-item" style={{ padding: "8px 16px", cursor: "pointer" }}
                        onClick={() => {
                            if (menuState.multiSelected) {
                                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                                const nodes = selectedNodes.map((n) => {
                                    return {
                                        id: n.id,
                                        data: { title: '', content: '' }
                                    }
                                })

                                if (nodes.length > 0) {
                                    updateNodes(nodes)
                                }
                                closeMenu();
                            }

                            if (menuState?.nodeId) {
                                updateNodes([
                                    {
                                        id: menuState.nodeId,
                                        data: { title: '', content: '' }
                                    }
                                ]);
                                closeMenu();
                            }
                        }}
                    >清空节点内容</div>
                    <div className={`flow-context-menu-item${(menuState?.nodeId || menuState.multiSelected) ? '' : ' disabled'}`}
                        style={{ padding: "8px 16px", cursor: (menuState?.nodeId || menuState.multiSelected) ? 'pointer' : 'not-allowed', color: (menuState?.nodeId || menuState.multiSelected) ? '' : '#bbb' }}
                        onClick={() => {
                            if (menuState.multiSelected) {
                                const selectedNodes = useFlowStore.getState().nodes.filter(n => n.selected);
                                handleDeleteNode(selectedNodes.map(n => n.id));
                                closeMenu();
                                return
                            }

                            if (menuState?.nodeId) {
                                // 删除节点
                                handleDeleteNode([menuState.nodeId]);
                                closeMenu();
                            }
                        }}
                    >删除</div>
                </div >
            );
        })()
    ) : null;

    // 点击空白关闭
    const handleGlobalClick = useCallback((e?: MouseEvent) => {
        // 只要菜单是打开的，点击任何非菜单区域都关闭
        if (menuState.open) {
            // 如果点击在菜单内部则不关闭
            const menuDom = document.querySelector('.flow-context-menu');
            if (menuDom && e && menuDom.contains(e.target as Node)) {
                return;
            }
            closeMenu();
        }
    }, [menuState.open, closeMenu]);

    // 挂载全局事件
    React.useEffect(() => {
        if (menuState.open) {
            document.addEventListener("click", handleGlobalClick);
            return () => document.removeEventListener("click", handleGlobalClick);
        }
    }, [menuState.open, handleGlobalClick]);

    return { 
        onNodeContextMenu, 
        ContextMenuDom, 
        closeMenu, 
        menuState,
        // 标签管理弹窗
        TagModalDom: tagModalState.visible ? (
            <TagSearch
                onClose={handleTagModalCancel}
            />
        ) : null
    };
};