import type { JSX } from 'react';
import * as React from 'react';
import { useEffect, useState, useRef, useMemo } from 'react';
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import { ClearEditorPlugin } from '@lexical/react/LexicalClearEditorPlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { SelectionAlwaysOnDisplay } from '@lexical/react/LexicalSelectionAlwaysOnDisplay';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin';
// import { useLexicalEditable } from '@lexical/react/useLexicalEditable';
import { CAN_USE_DOM } from '@lexical/utils';
import { useSettings } from './context/SettingsContext';
import AutoLinkPlugin from './plugins/AutoLinkPlugin';
import CodeActionMenuPlugin from './plugins/CodeActionMenuPlugin';
import CodeHighlightPrismPlugin from './plugins/CodeHighlightPrismPlugin';
// import CursorToEndPlugin from './plugins/CursorToEndPlugin';
import CodeHighlightShikiPlugin from './plugins/CodeHighlightShikiPlugin';
import DragDropPaste from './plugins/DragDropPastePlugin';
import DraggableBlockPlugin from './plugins/DraggableBlockPlugin';
import FloatingTextFormatToolbarPlugin from './plugins/FloatingTextFormatToolbarPlugin';
import EnhancedFloatingToolbarPlugin from './plugins/EnhancedFloatingToolbarPlugin';
import { usePdfStore } from '@/store/pdf-store';
import { useTabPanelStore } from '@/store/tab-panel-store';
import { usePanelContext } from '@/components/pdf/PanelContext';
import ImagesPlugin from './plugins/ImagesPlugin';
import LinkPlugin from './plugins/LinkPlugin';
import MarkHighlightPlugin from './plugins/MarkHighlightPlugin';
import MarkdownShortcutPlugin from './plugins/MarkdownShortcutPlugin';
import { MaxLengthPlugin } from './plugins/MaxLengthPlugin';
import ShortcutsPlugin from './plugins/ShortcutsPlugin';
import TableHoverActionsPlugin from './plugins/TableHoverActionsPlugin';
import { TableOfContentsPlugin, TableOfContentsComponent, TocToggleButton } from './plugins/TableOfContentsPlugin';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import HistoryShortcutPlugin from './plugins/HistoryShortcutPlugin';
import StyleTestPlugin from './plugins/StyleTestPlugin';
import HeadingFontSizePlugin from './plugins/HeadingFontSizePlugin';
import SlashCommandPlugin from './plugins/SlashCommandPlugin';
import ToolbarPlugin from './plugins/ToolbarPlugin';
import ContentEditable from './ui/ContentEditable';
import { $createParagraphNode, $createTextNode, $getRoot, BLUR_COMMAND, EditorState, LexicalEditor, LexicalNode } from 'lexical';
import { useFetchData } from "@/components/note/hooks/fetch-data.ts";

import './index.css'
import { useNoteStore } from '@/store/note-store';
import { $convertFromMarkdownString, $convertToMarkdownString } from '@lexical/markdown';
import { useNoteSave } from '../note/hooks/note-save';
import { PLAYGROUND_TRANSFORMERS } from './plugins/MarkdownTransformers';
// import { EditorWrapper } from './App';
// import useNodeEdit from '../flow/hooks/node-edit';
import { $createInlineImageNode } from './nodes/InlineImageNode/InlineImageNode';
import { folderService } from '@/local/services/folder.service';
import { cn } from '@/lib/utils';
import { EditorEvents, EDITOR_EVENTS } from '@/local/services/editor-events';
import { BlockList } from 'net';
import { LexicalJumpManager } from './utils/lexicalJumpUtils';

export enum NoteTypeEnum {
  FLOW = 'flow',
  NOTE = 'note',
  NODE = 'node'
}

export type NoteType = (typeof NoteTypeEnum)[keyof typeof NoteTypeEnum]

export default function Editor({ 
  onChange, 
  onBlur,
  editClass,
  hideToc = false,
  hideDraggable = false,
  useEnhancedToolbar = false,
  fileId,
  toolBar = true,
  heightLightToNode = false
}: {
  editClass?:string,
  onBlur?: () => void,
  onChange?: (editor: EditorState) => void,
  hideToc?: boolean,
  hideDraggable?: boolean,
  useEnhancedToolbar?: boolean,
  fileId?: string, 
  toolBar?: boolean,
  heightLightToNode?: boolean
}): JSX.Element {
  const {
    settings: {
      isCodeHighlighted,
      isCodeShiki,
      isCollab,
      isAutocomplete,
      isMaxLength,
      // isCharLimit,
      hasLinkAttributes,
      // isCharLimitUtf8,
      isRichText,
      showTreeView,
      // showTableOfContents,
      // shouldUseLexicalContextMenu,
      // shouldPreserveNewLinesInMarkdown,
      tableCellMerge,
      tableCellBackgroundColor,
      tableHorizontalScroll,
      // shouldAllowHighlightingWithBrackets,
      selectionAlwaysOnDisplay,
      listStrictIndent,
    },
  } = useSettings();
  // const isEditable = useLexicalEditable();
  const placeholder = isCollab
    ? 'Enter some collaborative rich text...'
    : isRichText
      ? 'Enter some rich text...'
      : 'Enter some plain text...';
  const [floatingAnchorElem, setFloatingAnchorElem] =
    useState<HTMLDivElement | null>(null);
  const [isSmallWidthViewport, setIsSmallWidthViewport] =
    useState<boolean>(false);
  const [editor] = useLexicalComposerContext();
  const [activeEditor, setActiveEditor] = useState(editor);
  const [isLinkEditMode, setIsLinkEditMode] = useState<boolean>(false);
  const [showToc, setShowToc] = useState<boolean>(false);
  const [tocKey, setTocKey] = useState<number>(0);

  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };


  useEffect(() => {
    const updateViewPortWidth = () => {
      const isNextSmallWidthViewport =
        CAN_USE_DOM && window.matchMedia('(max-width: 1025px)').matches;

      if (isNextSmallWidthViewport !== isSmallWidthViewport) {
        setIsSmallWidthViewport(isNextSmallWidthViewport);
      }
    };
    updateViewPortWidth();
    window.addEventListener('resize', updateViewPortWidth);

    return () => {
      window.removeEventListener('resize', updateViewPortWidth);
    };
  }, [isSmallWidthViewport]);

  // 订阅 store 以便在拖拽面板变化时重新计算 panelId
  const { tabItems, windows, findFileLocation } = usePdfStore((state) => ({
    tabItems: state.tabItems,
    windows: state.windows,
    findFileLocation: state.findFileLocation,
  }));
  const { tabPanels } = useTabPanelStore();
  // 优先使用 PanelContext 的 panelId（当在 TabPanel 内渲染时存在）
  const panelCtx = usePanelContext();
  const derivedPanelId = useMemo(() => {
    if (panelCtx?.panelId) return panelCtx.panelId; // 来自 TabPanel 的上下文
    if (!fileId) return undefined;
    return findFileLocation?.(fileId)?.id || undefined;
  }, [panelCtx?.panelId, fileId, tabPanels, tabItems, windows, findFileLocation]);
  // 调试
  // eslint-disable-next-line no-console
  // console.log('[Editor] panelId derive', { fileId, derivedPanelId, fromContext: panelCtx?.panelId ?? null });

  return (
    <>
      {toolBar && <ToolbarPlugin
        editor={editor}
        activeEditor={activeEditor}
        setActiveEditor={setActiveEditor}
        setIsLinkEditMode={setIsLinkEditMode}
      />}
      {/* 快捷键插件 */}
      {isRichText && (
        <ShortcutsPlugin
          editor={activeEditor}
          setIsLinkEditMode={setIsLinkEditMode}
        />
      )}
      <div className="flex h-full overflow-hidden">
        {/* 目录面板 - 左侧 */}
        {showToc && !hideToc && (
          <div className="w-64 sm:w-72 md:w-80 border-r bg-background flex-shrink-0 flex flex-col h-full">
            <div className="bg-background border-b p-3 flex items-center justify-start flex-shrink-0">
              <TocToggleButton
                showToc={showToc}
                onToggle={() => {
                  setShowToc(!showToc);
                  if (!showToc) {
                    setTimeout(() => setTocKey(prev => prev + 1), 50);
                  }
                }}
                variant="minimal"
                className="text-gray-600 hover:text-gray-800 hover:bg-gray-100 mr-2"
              />
              <span className="text-sm font-medium text-gray-800">目录</span>
            </div>
            <div className="p-3 overflow-y-auto flex-1 toc-scroll">
              <TableOfContentsComponent key={tocKey} />
            </div>
          </div>
        )}

        <div className={`flex-1 h-full relative`}>
          {/* 目录切换按钮 - 左上角悬浮 */}
          {!showToc && !hideToc && (
            <div className="absolute top-3 left-3 sm:top-4 sm:left-4 z-10">
              <TocToggleButton
                showToc={showToc}
                onToggle={() => {
                  setShowToc(!showToc);
                  if (!showToc) {
                    setTimeout(() => setTocKey(prev => prev + 1), 50);
                  }
                }}
                variant="minimal"
                className="text-gray-700 hover:text-gray-900 border border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50"
              />
            </div>
          )}

          <div className={`h-full editor-container overflow-auto ${toolBar ? 'hide-scrollbar' : ''}`}>
            {isMaxLength && <MaxLengthPlugin maxLength={30} />}
            <DragDropPaste />
            {selectionAlwaysOnDisplay && <SelectionAlwaysOnDisplay />}
            <ClearEditorPlugin />
            {/* <EmojiPickerPlugin /> */}
            {/* <AutoEmbedPlugin /> */}
            {/* <MentionsPlugin /> */}
            {/* <EmojisPlugin /> */}
            {/* <HashtagPlugin /> */}
            {/* <KeywordsPlugin /> */}
            {/* <SpeechToTextPlugin /> */}
            <AutoLinkPlugin />
            {/* <CursorToEndPlugin /> */}
            <>

              <RichTextPlugin
                contentEditable={
                  <div className="editor-scroller">
                    <div className="editor" ref={onRef}>
                      <ContentEditable className={cn(editClass)} placeholder={placeholder} onBlur={onBlur} />
                    </div>
                  </div>
                }
                ErrorBoundary={LexicalErrorBoundary}
              />
              <MarkdownShortcutPlugin />
              {isCodeHighlighted &&
                (isCodeShiki ? (
                  <CodeHighlightShikiPlugin />
                ) : (
                  <CodeHighlightPrismPlugin />
                ))}
              <ListPlugin hasStrictIndent={listStrictIndent} />
              <CheckListPlugin />
              <TablePlugin
                hasCellMerge={tableCellMerge}
                hasCellBackgroundColor={tableCellBackgroundColor}
                hasHorizontalScroll={tableHorizontalScroll}
              />
              <HorizontalRulePlugin />
              <ImagesPlugin />
              <LinkPlugin hasLinkAttributes={hasLinkAttributes} />
              <TableOfContentsPlugin />
              <HistoryPlugin />
              <HistoryShortcutPlugin />
              <HeadingFontSizePlugin />
              <SlashCommandPlugin />
              {process.env.NODE_ENV === 'development' && <StyleTestPlugin />}
              {floatingAnchorElem && !isSmallWidthViewport && (
                <>
                  {!hideDraggable && <DraggableBlockPlugin anchorElem={floatingAnchorElem} />}
                  <CodeActionMenuPlugin anchorElem={floatingAnchorElem} />
                  <TableHoverActionsPlugin anchorElem={floatingAnchorElem} />
                  <MarkHighlightPlugin anchorElem={floatingAnchorElem} />
                  {useEnhancedToolbar ? (
                    <EnhancedFloatingToolbarPlugin
                      anchorElem={floatingAnchorElem}
                      isLinkEditMode={isLinkEditMode}
                      setIsLinkEditMode={setIsLinkEditMode}
                      panelId={derivedPanelId}
                      fileId={fileId}
                      heightLightToNode={heightLightToNode}
                    />
                  ) : (
                    <FloatingTextFormatToolbarPlugin
                      anchorElem={floatingAnchorElem}
                      setIsLinkEditMode={setIsLinkEditMode}
                    />
                  )}
                </>
              )}
            </>
          </div>
        </div>
      </div>
    </>
  );
}

const useInitData = () => {
  const [editor] = useLexicalComposerContext();
  // 获取数据
  const { fetchData } = useFetchData()

  useEffect(() => {
    //  初始化数据
    fetchData().then((e) => {
      editor.update(() => {
        $convertFromMarkdownString(e, PLAYGROUND_TRANSFORMERS);
      });
    })
  }, [editor])

}


export const globalInstance: { editor: LexicalEditor | null } = {
  editor: null,
}

export const scrollEditorToEnd = (lexicalEditor: LexicalEditor | null) => {
  if (!lexicalEditor) return;
  // 放到微任务后，确保布局刷新完成
  setTimeout(() => {
    const rootEl = lexicalEditor.getRootElement();
    if (!rootEl) return;
    const container = rootEl.closest('.editor-container') as HTMLElement | null;
    if (container) {
      container.scrollTop = container.scrollHeight;
    } else {
      rootEl.scrollIntoView({ block: 'end', behavior: 'smooth' });
    }
  }, 0);
}
// TODO 后续把三种情况的编辑器都拆分单独文件
export const GlobalEditorWithHook = () => {
  const [editor] = useLexicalComposerContext();
  const { saveNote } = useNoteSave()

  useInitData()

  useEffect(() => {
    globalInstance.editor = editor
  }, [editor])

  return (
    <>
      <Editor useEnhancedToolbar={true} />
      <MyBlurPlugin onChange={(value) => {
        saveNote(value)
      }} />
    </>
  )
}
export interface EditorProps {
  value?: string,
  className?:string,
  onChange?: (value: string) => void,
  onBlur?: () => void,
  editClass?:string
  toolBar?:boolean,
  serialize?: 'markdown' | 'json'
}
// flow节点弹框编辑器
export const FlowEditorWithHook = (props: EditorProps) => {

  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const input = props?.value || '';
    // 支持 JSON 或 Markdown 初始值
    try {
      const parsed = JSON.parse(input);
      if (parsed && typeof parsed === 'object' && parsed.root) {
        const editorState = editor.parseEditorState(input);
        editor.setEditorState(editorState);
        return;
      }
    } catch {}
    editor.update(() => {
      $convertFromMarkdownString(input, PLAYGROUND_TRANSFORMERS);
    });
  }, [editor])

  return (
    <>
      <Editor 
      toolBar={props?.toolBar}
      editClass={props?.editClass} hideToc={true} hideDraggable={true} useEnhancedToolbar={true} />
      {/* 实时内容变化监听 */}
      <MyRealtimeChangePlugin onChange={(val) => {
        props?.onChange?.(val)
      }} serialize={props?.serialize ?? 'markdown'} />
      {/* 失焦时的处理 */}
      <MyBlurPlugin 
        onChange={() => {}}
        onBlur={props?.onBlur}
        serialize={props?.serialize ?? 'markdown'}
      />
    </>
  )
}

// flow节点内部编辑器
export const NodeEditorWithHook = (props: {
  onChange?: (value: string) => void,
  content?: string,
  onBlur?: () => void,
  id: string,
}) => {

  const [editor] = useLexicalComposerContext();
  useEffect(() => {

    if (props?.content) {
      editor.update(() => {
        $convertFromMarkdownString(props?.content || '', PLAYGROUND_TRANSFORMERS);
      });
    }
  }, [props?.content, editor])

  return <>
    <Editor useEnhancedToolbar={true} />
    <MyBlurPlugin onBlur={props?.onBlur} onChange={(value) => {
      props?.onChange?.(value)
    }} />
  </>
}

export const ResourceNoteHook = (
  props: {
    onChange?: (value: string) => void,
    content?: string,
    onBlur?: () => void,
    id: string,
    heightLightToNode?: BlockList
  }
) => {

  const [editor] = useLexicalComposerContext();
  
  // 注册编辑器到跳转管理器
  useEffect(() => {
    if (props?.id && editor) {
      LexicalJumpManager.registerEditor(props.id, editor);
      
      // 组件卸载时取消注册
      return () => {
        LexicalJumpManager.unregisterEditor(props.id);
      };
    }
  }, [props?.id, editor]);

  useEffect(() => {

    if (props?.id) {
      folderService?.getFileById(props?.id).then((res: any) => {
        console.log(res)
        const content = res?.content || ''
        editor.update(() => {
          $convertFromMarkdownString(content || '', PLAYGROUND_TRANSFORMERS);
        });
      })
    }

  }, [props?.id, editor])

  // 订阅来自全局的按 id 刷新请求
  useEffect(() => {
    const handler = (targetId: string) => {
      if (!targetId || targetId !== props.id) return;
      folderService?.getFileById(props.id).then((res: any) => {
        const content = res?.content || ''
        editor.update(() => {
          $convertFromMarkdownString(content || '', PLAYGROUND_TRANSFORMERS);
        }, {
          onUpdate: () => {
            scrollEditorToEnd(editor);
            editor.focus();
          }
        });
      })
    }
    EditorEvents.on(EDITOR_EVENTS.RELOAD_REQUEST, handler)
    return () => {
      EditorEvents.off(EDITOR_EVENTS.RELOAD_REQUEST, handler)
    }
  }, [props.id, editor])

  return <>
    <Editor useEnhancedToolbar={true} fileId={props.id} 
    heightLightToNode={!!props?.heightLightToNode}
    />
    <MyBlurPlugin onBlur={props?.onBlur} onChange={(value) => {
      folderService?.updateFileInfo({
        id: props?.id,
        content: value
      })
      props?.onChange?.(value)
    }} />
  </>
}

// 实时内容变化监听插件 - 优化性能
const MyRealtimeChangePlugin = ({ onChange, serialize = 'markdown' }: { onChange?: (content: string) => void, serialize?: 'markdown' | 'json' }) => {
  const [editor] = useLexicalComposerContext();
  const lastContentRef = useRef<string>('');

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      // 使用 requestAnimationFrame 优化性能
      requestAnimationFrame(() => {
        editorState.read(() => {
          const output = serialize === 'json'
            ? JSON.stringify(editor.getEditorState())
            : $convertToMarkdownString(PLAYGROUND_TRANSFORMERS, undefined, false);

          if (output !== lastContentRef.current) {
            lastContentRef.current = output;
            onChange?.(output);
          }
        });
      });
    });
  }, [editor, onChange]);

  return null;
};


const MyBlurPlugin = ({ onChange, onBlur, serialize = 'markdown' }: { onChange?: (editor: string) => void, onBlur?: () => void, serialize?: 'markdown' | 'json' }) => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerCommand(
      BLUR_COMMAND,
      () => {
        editor.read(() => {
          const output = serialize === 'json'
            ? JSON.stringify(editor.getEditorState())
            : $convertToMarkdownString(PLAYGROUND_TRANSFORMERS, undefined, false);
          onBlur?.()
          onChange?.(output)
        })
        // 在这里处理失焦逻辑

        return false; // 返回 false 允许命令继续传播
      },
      0 // 优先级
    );
  }, [editor, onChange]);

  return null;
}

// 检测base64编码的正则表达式
const BASE64_IMAGE_REGEX = /data:image\/([a-zA-Z]+);base64,([a-zA-Z0-9+/=]+)/g;

// 提取base64编码并转换为图片节点的函数
const processContentWithBase64 = (content: string) => {
  const nodes: Array<LexicalNode> = [];
  let lastIndex = 0;
  let match;

  // 重置正则表达式的lastIndex
  BASE64_IMAGE_REGEX.lastIndex = 0;

  while ((match = BASE64_IMAGE_REGEX.exec(content)) !== null) {
    const [fullMatch, imageType, base64Data] = match;
    const matchStart = match.index;
    const matchEnd = matchStart + fullMatch.length;

    // 添加base64编码前的文本
    if (matchStart > lastIndex) {
      const textBefore = content.slice(lastIndex, matchStart);
      if (textBefore.trim()) {
        nodes.push($createTextNode(textBefore));
      }
    }

    // 创建图片节点
    const imageSrc = `data:image/${imageType};base64,${base64Data}`;
    const imageNode = $createInlineImageNode({
      src: imageSrc,
      altText: `Base64 Image (${imageType})`,
      width: 300,
      height: 200,
    });
    nodes.push(imageNode);

    lastIndex = matchEnd;
  }

  // 添加剩余的文本
  if (lastIndex < content.length) {
    const remainingText = content.slice(lastIndex);
    if (remainingText.trim()) {
      nodes.push($createTextNode(remainingText));
    }
  }

  return nodes;
};

export const _tempInsertContent = (content: string) => {
  globalInstance?.editor?.update(() => {
    const root = $getRoot();

    // 检查content是否包含base64编码
    if (BASE64_IMAGE_REGEX.test(content)) {
      // 重置正则表达式的lastIndex
      BASE64_IMAGE_REGEX.lastIndex = 0;

      // 处理包含base64编码的内容
      const nodes = processContentWithBase64(content);

      // 为每个节点创建段落
      nodes.forEach(node => {
        const paragraphNode = $createParagraphNode();
        paragraphNode.append(node);
        root.append(paragraphNode);
      });
    } else {
      // 原来的逻辑：创建文本节点
      const paragraphNode = $createParagraphNode();
      const textNode = $createTextNode(content?.trim() || '');
      paragraphNode.append(textNode);
      root.append(paragraphNode);
    }
  }, {
    onUpdate: () => {
      // 在编辑器更新后执行滚动
      if (!globalInstance?.editor) return
      setTimeout(() => {
        const editorRootElement = globalInstance?.editor?.getRootElement();
        if (editorRootElement) {
          editorRootElement.scrollIntoView({ block: 'end', behavior: 'smooth' });
          // 滚动完成后让编辑器获取焦点
          globalInstance?.editor?.focus();
        }
      }, 10)
    }
  });
}