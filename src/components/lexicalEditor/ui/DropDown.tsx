/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type {JSX} from 'react';

import {isDOMNode} from 'lexical';
import * as React from 'react';
import {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {createPortal} from 'react-dom';

type DropDownContextType = {
  registerItem: (ref: React.RefObject<HTMLButtonElement>) => void;
  onClose: () => void;
};

const DropDownContext = React.createContext<DropDownContextType | null>(null);

const dropDownPadding = 4;

// 全局下拉菜单管理
const activeDropDowns = new Set<() => void>();

const registerDropDown = (closeCallback: () => void) => {
  activeDropDowns.add(closeCallback);
  return () => {
    activeDropDowns.delete(closeCallback);
  };
};

const closeAllDropDowns = (except?: () => void) => {
  activeDropDowns.forEach(close => {
    if (close !== except) {
      close();
    }
  });
};

export function DropDownItem({
  children,
  className,
  onClick,
  title,
  closeOnClick = true,
}: {
  children: React.ReactNode;
  className: string;
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  title?: string;
  closeOnClick?: boolean;
}) {
  const ref = useRef<HTMLButtonElement>(null);

  const dropDownContext = React.useContext(DropDownContext);

  if (dropDownContext === null) {
    throw new Error('DropDownItem must be used within a DropDown');
  }

  const {registerItem, onClose} = dropDownContext;

  useEffect(() => {
    if (ref && ref.current) {
      registerItem(ref as React.RefObject<HTMLButtonElement>);
    }
  }, [ref, registerItem]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onClick(event);
    // 根据 closeOnClick 决定是否关闭下拉菜单
    if (closeOnClick) {
      onClose();
    }
  };

  return (
    <button
      className={className}
      onClick={handleClick}
      ref={ref}
      title={title}
      type="button">
      {children}
    </button>
  );
}

function DropDownItems({
  children,
  dropDownRef,
  onClose,
}: {
  children: React.ReactNode;
  dropDownRef: React.Ref<HTMLDivElement>;
  onClose: () => void;
}) {
  const [items, setItems] = useState<React.RefObject<HTMLButtonElement>[]>();
  const [highlightedItem, setHighlightedItem] =
    useState<React.RefObject<HTMLButtonElement>>();

  const registerItem = useCallback(
    (itemRef: React.RefObject<HTMLButtonElement>) => {
      setItems((prev) => (prev ? [...prev, itemRef] : [itemRef]));
    },
    [setItems],
  );

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (!items) {
      return;
    }

    const key = event.key;

    if (['Escape', 'ArrowUp', 'ArrowDown', 'Tab'].includes(key)) {
      event.preventDefault();
    }

    if (key === 'Escape' || key === 'Tab') {
      onClose();
    } else if (key === 'ArrowUp') {
      setHighlightedItem((prev) => {
        if (!prev) {
          return items[0];
        }
        const index = items.indexOf(prev) - 1;
        return items[index === -1 ? items.length - 1 : index];
      });
    } else if (key === 'ArrowDown') {
      setHighlightedItem((prev) => {
        if (!prev) {
          return items[0];
        }
        return items[items.indexOf(prev) + 1];
      });
    }
  };

  const contextValue = useMemo(
    () => ({
      registerItem,
      onClose,
    }),
    [registerItem, onClose],
  );

  useEffect(() => {
    if (items && !highlightedItem) {
      setHighlightedItem(items[0]);
    }

    if (highlightedItem && highlightedItem.current) {
      highlightedItem.current.focus();
    }
  }, [items, highlightedItem]);

  return (
    <DropDownContext.Provider value={contextValue}>
      <div className="dropdown" ref={dropDownRef} onKeyDown={handleKeyDown}>
        {children}
      </div>
    </DropDownContext.Provider>
  );
}

export default function DropDown({
  disabled = false,
  buttonLabel,
  buttonAriaLabel,
  buttonClassName,
  buttonIconClassName,
  children,
  stopCloseOnClickSelf,
}: {
  disabled?: boolean;
  buttonAriaLabel?: string;
  buttonClassName: string;
  buttonIconClassName?: string;
  buttonLabel?: string;
  children: ReactNode;
  stopCloseOnClickSelf?: boolean;
}): JSX.Element {
  const dropDownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [showDropDown, setShowDropDown] = useState(false);

  const handleClose = () => {
    setShowDropDown(false);
    if (buttonRef && buttonRef.current) {
      buttonRef.current.focus();
    }
  };

  // 注册/取消注册下拉菜单，并锁定工具栏滚动
  useEffect(() => {
    if (showDropDown) {
      // 注册下拉菜单
      const unregister = registerDropDown(handleClose);
      
      // 锁定工具栏滚动
      const toolbar = document.querySelector('.toolbar') as HTMLElement;
      if (toolbar) {
        const originalOverflow = toolbar.style.overflow;
        toolbar.style.overflow = 'hidden';
        
        return () => {
          unregister();
          // 恢复工具栏滚动
          toolbar.style.overflow = originalOverflow;
        };
      }
      
      return unregister;
    }
  }, [showDropDown]);

  useEffect(() => {
    const button = buttonRef.current;
    const dropDown = dropDownRef.current;

    if (showDropDown && button !== null && dropDown !== null) {
      const {top, left} = button.getBoundingClientRect();
      dropDown.style.top = `${top + button.offsetHeight + dropDownPadding}px`;
      dropDown.style.left = `${Math.min(
        left,
        window.innerWidth - dropDown.offsetWidth - 20,
      )}px`;
    }
  }, [dropDownRef, buttonRef, showDropDown]);

  useEffect(() => {
    const button = buttonRef.current;

    if (button !== null && showDropDown) {
      const handle = (event: MouseEvent) => {
        const target = event.target;
        if (!isDOMNode(target)) {
          return;
        }
        
        // 如果点击在按钮内，不关闭（让按钮的 onClick 处理）
        if (button.contains(target)) {
          return;
        }
        
        // 如果点击在下拉菜单内，默认不关闭（除非明确设置 stopCloseOnClickSelf 为 false）
        if (dropDownRef.current && dropDownRef.current.contains(target)) {
          if (stopCloseOnClickSelf !== false) {
            return;
          }
        }
        
        // 点击在外部或者 stopCloseOnClickSelf 为 false 时关闭下拉菜单
        setShowDropDown(false);
      };
      document.addEventListener('click', handle, true); // 使用 capture 阶段

      return () => {
        document.removeEventListener('click', handle, true);
      };
    }
  }, [dropDownRef, buttonRef, showDropDown, stopCloseOnClickSelf]);

  useEffect(() => {
    const handleButtonPositionUpdate = () => {
      if (showDropDown) {
        const button = buttonRef.current;
        const dropDown = dropDownRef.current;
        if (button !== null && dropDown !== null) {
          const {top} = button.getBoundingClientRect();
          const newPosition = top + button.offsetHeight + dropDownPadding;
          if (newPosition !== dropDown.getBoundingClientRect().top) {
            dropDown.style.top = `${newPosition}px`;
          }
        }
      }
    };

    document.addEventListener('scroll', handleButtonPositionUpdate);

    return () => {
      document.removeEventListener('scroll', handleButtonPositionUpdate);
    };
  }, [buttonRef, dropDownRef, showDropDown]);

  return (
    <>
      <button
        type="button"
        disabled={disabled}
        aria-label={buttonAriaLabel || buttonLabel}
        className={buttonClassName}
        onClick={() => {
          // 如果当前菜单是关闭的，先关闭所有其他菜单，然后打开当前菜单
          if (!showDropDown) {
            closeAllDropDowns(handleClose); // 排除当前菜单
            setShowDropDown(true);
          } else {
            // 如果当前菜单是打开的，直接关闭
            setShowDropDown(false);
          }
        }}
        ref={buttonRef}>
        {buttonIconClassName && <span className={buttonIconClassName} />}
        {buttonLabel && (
          <span className="text dropdown-button-text">{buttonLabel}</span>
        )}
        <i className="chevron-down" />
      </button>

      {showDropDown &&
        createPortal(
          <DropDownItems dropDownRef={dropDownRef} onClose={handleClose}>
            {children}
          </DropDownItems>,
          document.body,
        )}
    </>
  );
}
