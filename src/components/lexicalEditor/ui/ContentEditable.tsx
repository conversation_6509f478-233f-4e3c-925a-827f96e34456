/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type {JSX} from 'react';

import './ContentEditable.css';

import {ContentEditable} from '@lexical/react/LexicalContentEditable';
import * as React from 'react';
import { cn } from '@/lib/utils';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

type Props = {
  className?: string;
  placeholderClassName?: string;
  placeholder: string;
  onBlur?: () => void;
};

export default function LexicalContentEditable({
  className,
  placeholder,
  placeholderClassName,
  onBlur,
}: Props): JSX.Element {
  const [editor] = useLexicalComposerContext();

  const handleContainerClick = (e: React.MouseEvent) => {
    // 如果点击的不是编辑器内容区域，让编辑器获取焦点
    const target = e.target as HTMLElement;
    const isEditorContent = target.closest('.ContentEditable__root');
    if (!isEditorContent) {
      e.preventDefault();
      editor.focus();
    }
  };

  return (
    <div onClick={handleContainerClick} className="w-full h-full">
      <ContentEditable
        onBlur={onBlur}
        className={cn(className, 'ContentEditable__root')}
        aria-placeholder={placeholder}
        placeholder={
          <div className={placeholderClassName ?? 'ContentEditable__placeholder'}>
            {placeholder}
          </div>
        }
      />
    </div>
  );
}
