import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $convertToMarkdownString, $convertFromMarkdownString } from '@lexical/markdown';
import { $getRoot, $createParagraphNode, $createTextNode } from 'lexical';
import { useEffect } from 'react';
import { PLAYGROUND_TRANSFORMERS } from '../MarkdownTransformers';

export default function StyleTestPlugin(): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // 仅在开发环境下添加测试
    if (process.env.NODE_ENV === 'development') {
      // 添加全局测试函数
      (window as any).testStyleSaving = () => {
        editor.update(() => {
          const root = $getRoot();
          root.clear();

          // 创建测试内容
          const paragraph = $createParagraphNode();
          
          // 普通文本
          const normalText = $createTextNode('普通文本 ');
          
          // 红色文本
          const redText = $createTextNode('红色文本 ');
          redText.setStyle('color: #ff0000');
          
          // 大号蓝色文本
          const bigBlueText = $createTextNode('大号蓝色文本 ');
          bigBlueText.setStyle('color: #0066cc; font-size: 20px');
          
          // 自定义字体和背景色
          const styledText = $createTextNode('自定义样式文本');
          styledText.setStyle('font-family: "Microsoft YaHei"; background-color: #ffff00; color: #ff0000; font-size: 18px');

          paragraph.append(normalText, redText, bigBlueText, styledText);
          root.append(paragraph);
        });

        // 测试保存
        setTimeout(() => {
          editor.read(() => {
            const markdown = $convertToMarkdownString(
              PLAYGROUND_TRANSFORMERS,
              undefined,
              false
            );
            // 测试恢复
            editor.update(() => {
              const root = $getRoot();
              root.clear();
              $convertFromMarkdownString(markdown, PLAYGROUND_TRANSFORMERS);
            });
          });
        }, 100);
      };

      (window as any).getCurrentMarkdown = () => {
        editor.read(() => {
          const markdown = $convertToMarkdownString(
            PLAYGROUND_TRANSFORMERS,
            undefined,
            false
          );
          return markdown;
        });
      };

      (window as any).testFontStyles = () => {
        editor.update(() => {
          const root = $getRoot();
          root.clear();

          // 创建测试内容
          const paragraph = $createParagraphNode();
          
          // 普通文本
          const normalText = $createTextNode('普通文本 ');
          
          // Arial 字体
          const arialText = $createTextNode('Arial字体 ');
          arialText.setStyle('font-family: Arial');
          
          // 微软雅黑 + 大号
          const yaheiText = $createTextNode('微软雅黑大号 ');
          yaheiText.setStyle('font-family: "Microsoft YaHei"; font-size: 20px');
          
          // 组合样式
          const combinedText = $createTextNode('组合样式文本');
          combinedText.setStyle('font-family: Georgia; font-size: 18px; color: #ff0000; background-color: #ffff00');

          paragraph.append(normalText, arialText, yaheiText, combinedText);
          root.append(paragraph);
        });

        // 立即检查样式
        setTimeout(() => {
          editor.read(() => {
            const markdown = $convertToMarkdownString(
              PLAYGROUND_TRANSFORMERS,
              undefined,
              false
            );
          });
        }, 100);
      };

    }
  }, [editor]);

  return null;
}