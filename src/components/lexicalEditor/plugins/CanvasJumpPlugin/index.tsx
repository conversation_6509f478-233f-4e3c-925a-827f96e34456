/**
 * Canvas Jump Plugin
 * 处理 Lexical 中的画布跳转链接（canvas://node-{nodeId}）
 */

import { $getSelection, $isRangeSelection } from 'lexical';
import { $createLinkNode, LinkNode } from '@lexical/link';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';
import { useReactFlowInstanceStore } from '@/store/reactflow-instance-store';

/**
 * 扩展 URL 验证以支持 canvas:// 协议
 */
export function validateCanvasUrl(url: string): boolean {
  // 支持 canvas:// 协议
  if (url.startsWith('canvas://node-')) {
    return true;
  }
  
  // 支持标准 URL 协议 - 使用简化的正则表达式
  const urlRegExp = /^https?:\/\/[\w\-._~:/?#[\]@!$&'()*+,;=]+$/i;
  
  return url === 'https://' || urlRegExp.test(url);
}

/**
 * 创建画布跳转链接
 * @param nodeId 画布节点ID
 * @param text 链接文本
 * @returns 画布链接 URL
 */
export function createCanvasLinkUrl(nodeId: string): string {
  return `canvas://node-${nodeId}`;
}

/**
 * 解析画布链接获取节点ID
 * @param url 链接URL
 * @returns 节点ID或null
 */
export function parseCanvasLinkUrl(url: string): string | null {
  const match = url.match(/^canvas:\/\/node-(.+)$/);
  return match ? match[1] : null;
}

/**
 * 在编辑器中创建画布跳转链接
 * @param editor Lexical 编辑器实例
 * @param nodeId 目标画布节点ID
 * @param selectedText 选中的文本
 */
export function createCanvasLink(editor: any, nodeId: string, selectedText: string) {
  editor.update(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const linkUrl = createCanvasLinkUrl(nodeId);
      const linkNode = $createLinkNode(linkUrl);
      
      // 如果有选中文本，替换选中内容
      if (selectedText) {
        selection.insertText(selectedText);
        selection.insertNodes([linkNode]);
      }
    }
  });
}

/**
 * Canvas Jump Plugin 组件
 */
export default function CanvasJumpPlugin() {
  const [editor] = useLexicalComposerContext();
  const { instance } = useReactFlowInstanceStore();

  useEffect(() => {
    // 监听点击事件
    const handleClick = (event: Event) => {
      const target = event.target as HTMLElement;
      
      // 检查是否点击了链接
      if (target.tagName === 'A' || target.closest('a')) {
        const linkElement = target.tagName === 'A' ? target : target.closest('a');
        const href = linkElement?.getAttribute('href');
        
        if (href && href.startsWith('canvas://node-')) {
          event.preventDefault();
          
          // 解析节点ID
          const nodeId = parseCanvasLinkUrl(href);
          if (nodeId && instance) {
            // 跳转到画布节点
            jumpToCanvasNode(nodeId);
          }
        }
      }
    };

    // 获取编辑器根元素
    const editorElement = editor.getRootElement();
    if (editorElement) {
      editorElement.addEventListener('click', handleClick);
      
      return () => {
        editorElement.removeEventListener('click', handleClick);
      };
    }
  }, [editor, instance]);

  /**
   * 跳转到指定的画布节点
   */
  const jumpToCanvasNode = (nodeId: string) => {
    if (!instance) return;

    try {
      // 获取目标节点
      const targetNode = instance.getNode(nodeId);
      if (!targetNode) {
        console.warn(`Canvas node not found: ${nodeId}`);
        return;
      }

      // 先取消所有节点选中
      const allNodes = instance.getNodes();
      const updatedNodes = allNodes.map(node => ({
        ...node,
        selected: node.id === nodeId,
        selectedFromLexical: node.id === nodeId
      }));
      
      // 更新节点状态
      instance.setNodes(updatedNodes);

      // 获取当前视口缩放比例
      const viewport = instance.getViewport();
      
      // 居中显示目标节点
      instance.setCenter(targetNode.position.x, targetNode.position.y, {
        zoom: viewport.zoom,
        duration: 500
      });

      console.log(`Jumped to canvas node: ${nodeId}`, targetNode);
    } catch (error) {
      console.error('Failed to jump to canvas node:', error);
    }
  };

  return null;
}