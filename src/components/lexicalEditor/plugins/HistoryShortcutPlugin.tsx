import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { UNDO_COMMAND, REDO_COMMAND } from 'lexical';
import { useEffect } from 'react';

export default function HistoryShortcutPlugin(): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Z (撤销)
      if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        editor.dispatchCommand(UNDO_COMMAND, undefined);
        return;
      }

      // Ctrl+Y 或 Ctrl+Shift+Z (重做)
      if (
        (event.ctrlKey && event.key === 'y') ||
        (event.ctrlKey && event.shiftKey && event.key === 'Z')
      ) {
        event.preventDefault();
        editor.dispatchCommand(REDO_COMMAND, undefined);
        return;
      }
    };

    // 在编辑器容器上监听键盘事件
    const editorContainer = editor.getRootElement();
    if (editorContainer) {
      editorContainer.addEventListener('keydown', handleKeyDown);
      return () => {
        editorContainer.removeEventListener('keydown', handleKeyDown);
      };
    }

    // 降级方案：在全局监听
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [editor]);

  return null;
}