/* Enhanced Floating Toolbar Styles - 基于原始工具栏样式 */
.enhanced-floating-toolbar {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.5s;
  height: auto;
  min-height: 32px;
  will-change: transform;
  pointer-events: auto;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOut {
  from {
    max-width: 0;
    opacity: 0;
  }
  to {
    max-width: 300px;
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    max-width: 300px;
    opacity: 1;
  }
  to {
    max-width: 0;
    opacity: 0;
  }
}