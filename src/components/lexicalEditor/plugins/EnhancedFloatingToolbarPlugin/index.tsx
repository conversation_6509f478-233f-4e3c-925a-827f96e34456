/**
 * Enhanced Floating Toolbar Plugin for Lexical Editor
 * 复用 PDF 高亮工具栏的逻辑
 */

import type { JSX } from 'react';
import './index.css';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import {
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_LOW,
  getDOMSelection,
  LexicalEditor,
  SELECTION_CHANGE_COMMAND,
} from 'lexical';
import { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { getDOMRangeRect } from '../../utils/getDOMRangeRect';
import { setFloatingElemPosition } from '../../utils/setFloatingElemPosition';
import { Button, Space, Tooltip } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { SketchPicker } from "react-color";
import { IconFont } from "@/components/IconFont";
import { useHighlightChat } from "@/components/pdf/hooks/highlight-chat";
import { CustomHighlight } from "@/store/pdf-store";
import { usePdfStore } from "@/store/pdf-store";
import { HighlightToolbar } from "@/components/pdf/components/highlight/HighlightToolbar";
// 使用统一的节点添加方法
import { useAddNode } from '@/components/flow/hooks/node-add';

// 增强的工具栏组件
function EnhancedFloatingToolbar({
  editor,
  anchorElem,
  selectedText,
  isCreatingNodeRef,
  onHighlightCreate,
  panelId,
}: {
  editor: LexicalEditor;
  anchorElem: HTMLElement;
  selectedText: string;
  isCreatingNodeRef: React.RefObject<boolean> | React.MutableRefObject<boolean>;
  onHighlightCreate: (color: string) => void;
  panelId?: string;
}): JSX.Element {
  const popupRef = useRef<HTMLDivElement | null>(null);
  const [showColorList, setShowColorList] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#FFFF00");
  
  // 复用 PDF 的 hooks
  const { handleChat } = useHighlightChat();
  
  // 颜色列表
  const colorList = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF"];
  
  // 订阅全局与面板状态，使其对 mode 变化响应
  const { globalMode, panel } = usePdfStore((state) => ({
    globalMode: state.mode,
    panel: panelId ? state.panels.get(panelId) : null,
  }));
  const mode = panel?.mode ?? globalMode;
  // 生产移除调试输出

  // 创建虚拟高亮对象用于传递给现有 hooks
  const createVirtualHighlight = useCallback((): CustomHighlight => {
    return {
      id: crypto.randomUUID(),
      aid: 'lexical-editor',
      type: 'text',
      content: { text: selectedText },
      position: {
        // 虚拟高亮：提供必需的 pageNumber 以满足类型 Scaled
        boundingRect: { width: 0, height: 0, pageNumber: 1 },
        rects: []
      },
      color: selectedColor,
      nid: ""
    };
  }, [selectedText, selectedColor]);

  // 处理 AI 聊天
  const handleAIChat = useCallback(() => {
    const virtualHighlight = createVirtualHighlight();
    handleChat(virtualHighlight);
  }, [createVirtualHighlight, handleChat]);


  // 处理高亮添加 - 调用父组件的回调
  const handleAddHighlight = useCallback((color: string) => {
    // 防重复检查
    if (isCreatingNodeRef.current) {
      return;
    }
    
    // 调用父组件的高亮创建回调
    onHighlightCreate(color);
  }, [isCreatingNodeRef, onHighlightCreate]);

  // 更新弹出框位置 - 使用与原始工具栏相同的逻辑
  const updatePosition = useCallback(() => {
    const selection = $getSelection();
    const popupElem = popupRef.current;
    const nativeSelection = getDOMSelection(editor._window);

    if (popupElem === null) {
      return;
    }

    const rootElement = editor.getRootElement();
    if (
      selection !== null &&
      nativeSelection !== null &&
      !nativeSelection.isCollapsed &&
      rootElement !== null &&
      rootElement.contains(nativeSelection.anchorNode)
    ) {
      const rangeRect = getDOMRangeRect(nativeSelection, rootElement);
      setFloatingElemPosition(rangeRect, popupElem, anchorElem, false);
    }
  }, [editor, anchorElem]);

  useEffect(() => {
    const scrollerElem = anchorElem.parentElement;

    const update = () => {
      editor.getEditorState().read(() => {
        updatePosition();
      });
    };

    window.addEventListener('resize', update);
    if (scrollerElem) {
      scrollerElem.addEventListener('scroll', update);
    }

    return () => {
      window.removeEventListener('resize', update);
      if (scrollerElem) {
        scrollerElem.removeEventListener('scroll', update);
      }
    };
  }, [editor, updatePosition, anchorElem]);

  useEffect(() => {
    editor.getEditorState().read(() => {
      updatePosition();
    });
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updatePosition();
        });
      }),

      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          updatePosition();
          return false;
        },
        COMMAND_PRIORITY_LOW,
      ),
    );
  }, [editor, updatePosition]);

  // 根据模式显示不同的工具栏内容
  return (
    <div ref={popupRef} className="enhanced-floating-toolbar">
      {mode === 1 ? (
        // 鼠标模式：显示完整工具栏
        <div className="bg-black rounded-md">
          <div className="w-full relative">
            <div className="w-full inline-flex justify-between items-center gap-2.5 p-2 rounded-md text-white cursor-pointer bg-transparent">
              <Space>
                {/* AI 聊天按钮 */}
                <Tooltip title="AI聊天">
                  <Button 
                    type="primary" 
                    shape="circle" 
                    size="small"
                    style={{ backgroundColor: "rgba(0,0,0,.5)" }}
                    icon={<IconFont type="icon-chat" style={{ color: "#fff" }} />}
                    onClick={handleAIChat}
                  />
                </Tooltip>
                
                {/* 颜色选择器 */}
                <Space
                  className="relative overflow-hidden"
                  onMouseEnter={() => setShowColorList(true)}
                  onMouseLeave={() => setShowColorList(false)}
                >
                  <Tooltip title="添加高亮">
                    <Button 
                      type="primary" 
                      shape="circle" 
                      size="small" 
                      icon={<PlusOutlined />}
                      style={{ backgroundColor: "rgba(0,0,0,.5)" }}
                    />
                  </Tooltip>
                  
                  <div className="flex justify-around items-center">
                    {showColorList && (
                      <Space style={{ animation: `${showColorList ? "slideOut" : "slideIn"} 0.3s ease-in-out forwards` }}>
                        {colorList.map((color, index) => (
                          <div
                            key={index}
                            className="w-5 h-5 rounded-full border border-white cursor-pointer"
                            style={{ backgroundColor: color }}
                            onClick={() => {
                              setSelectedColor(color);
                              handleAddHighlight(color);
                            }}
                          />
                        ))}
                        <Tooltip title="选择颜色">
                          <div 
                            className="w-6 h-6 rounded border border-white cursor-pointer"
                            style={{ backgroundColor: selectedColor }}
                            onClick={() => setShowColorPicker(!showColorPicker)}
                          />
                        </Tooltip>
                      </Space>
                    )}
                  </div>
                </Space>
              </Space>
            </div>
            
            {/* 颜色选择器面板 */}
            {showColorPicker && (
              <div className="absolute z-100 top-full mt-2">
                <SketchPicker 
                  color={selectedColor} 
                  onChange={(color) => {
                    setSelectedColor(color.hex);
                    handleAddHighlight(color.hex);
                  }}
                />
              </div>
            )}
          </div>
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}

// 主插件组件
export default function EnhancedFloatingToolbarPlugin({
  anchorElem = document.body,
  isLinkEditMode = false,
  panelId,
  fileId,
}: {
  anchorElem?: HTMLElement;
  isLinkEditMode?: boolean;
  setIsLinkEditMode?: (isLinkEditMode: boolean) => void;
  panelId?: string;
  fileId?: string;
}): JSX.Element | null {
  const [editor] = useLexicalComposerContext();
  const [isText, setIsText] = useState(false);
  const [selectedText, setSelectedText] = useState("");
  const [isSelecting, setIsSelecting] = useState(false); // 跟踪是否正在选择
  const [createdHighlight, setCreatedHighlight] = useState<CustomHighlight | null>(null); // 存储创建的高亮
  const [lastSelectionRect, setLastSelectionRect] = useState<DOMRect | null>(null); // 存储最后的选择位置
  const isCreatingNode = useRef(false); // 用于防止重复创建节点
  
  // 获取添加节点的方法
  const { addNodeFromText } = useAddNode();

  // 面板级模式：优先取 panelId 对应面板，否则退回全局
  const { globalMode, panelForPlugin } = usePdfStore((state) => ({
    globalMode: state.mode,
    panelForPlugin: panelId ? state.panels.get(panelId) : null,
  }));
  const localMode = panelForPlugin?.mode ?? globalMode;
  // 生产移除调试输出

  // 鼠标模式下的高亮创建回调
  const handleHighlightCreate = useCallback((color: string) => {
    // 防重复检查
    if (isCreatingNode.current) {
      return;
    }
    
    try {
      // 设置创建标志
      isCreatingNode.current = true;
      
      // 使用传入的 fileId 作为 aid，保证与附件表关联
      const aid = fileId || 'text-editor';

      // 使用统一的 addNodeFromText 方法创建节点
      const newNode = addNodeFromText({
        content: selectedText,
        aid: aid,
        color: color,
      });

      // 隐藏工具栏与状态清理
      setIsText(false);
      
      // 创建虚拟高亮对象
      if (newNode) {
        setCreatedHighlight({
          id: newNode.id,
          aid,
          type: 'text',
          content: { text: selectedText },
          position: { boundingRect: { width: 0, height: 0, pageNumber: 1 }, rects: [] },
          color,
          nid: newNode.id,
        });
      }
    } catch (error) {
      console.error('创建高亮节点失败:', error);
    } finally {
      // 重置创建标志
      isCreatingNode.current = false;
    }
  }, [selectedText, fileId, addNodeFromText]);

  // 创建节点的函数
  const createNodeFromSelection = useCallback((text: string, selectionRect: DOMRect) => {
    // 防重复检查
    if (isCreatingNode.current) {
      return;
    }
    
    if (localMode !== 1) {
      // 设置创建标志，防止重复创建
      isCreatingNode.current = true;
      
      // 非鼠标模式，使用统一的 addNodeFromText 方法
      const defaultColor = usePdfStore.getState().defaultColor;
      const aid = fileId || 'text-editor';

      try {
        // 使用统一的 addNodeFromText 方法创建节点
        const newNode = addNodeFromText({
          content: text,
          aid: aid,
          color: defaultColor,
        });

        // 设置创建的高亮和位置，用于显示 PDF 风格的工具栏
        setIsText(false);
        setSelectedText('');
        setIsSelecting(false);
        
        // 创建虚拟高亮对象，用于显示工具栏
        if (newNode) {
          setCreatedHighlight({
            id: newNode.id,
            aid,
            type: 'text',
            content: { text },
            position: { boundingRect: { width: 0, height: 0, pageNumber: 1 }, rects: [] },
            color: defaultColor,
            nid: newNode.id,
          });
          setLastSelectionRect(selectionRect);
        }
      } catch (e) {
        console.error('文本高亮创建失败(非鼠标模式):', e);
      } finally {
        isCreatingNode.current = false;
      }
    }
  }, [selectedText, fileId, addNodeFromText]);

  const updateToolbar = useCallback(() => {
    editor.getEditorState().read(() => {
      // 检查是否在输入法输入中
      if (editor.isComposing()) {
        return;
      }
      
      const selection = $getSelection();
      const nativeSelection = getDOMSelection(editor._window);
      
      if (
        $isRangeSelection(selection) &&
        nativeSelection !== null &&
        !nativeSelection.isCollapsed
      ) {
        const text = selection.getTextContent();
        setSelectedText(text);
        
        // 只有鼠标模式才显示实时工具栏（面板级）
        if (localMode === 1) {
          setIsText(true);
        }
        
        setIsSelecting(true); // 标记正在选择
        // 清除之前创建的高亮
        setCreatedHighlight(null);
        // 重置创建标志，允许新的创建
        isCreatingNode.current = false;
        
        // 存储当前选择的位置
        const rootElement = editor.getRootElement();
        if (rootElement) {
          const rangeRect = getDOMRangeRect(nativeSelection, rootElement);
          setLastSelectionRect(rangeRect);
        }
      } else {
        // 选择结束时的处理
        setIsText(false);
        setSelectedText("");
        setIsSelecting(false);
      }
    });
  }, [editor, localMode]);

  // 监听 mouseup 事件来处理笔记模式下的松手创建节点
  useEffect(() => {
    const handleMouseUp = (e: MouseEvent) => {
      // 如果点击的是工具栏或其子元素，不处理（作为双重保险）
      const target = e.target as Element;
      if (target.closest('.enhanced-floating-toolbar')) {
        return;
      }

      // 延迟一点确保选择状态已更新
      setTimeout(() => {
        if (isSelecting && selectedText.trim().length > 0 && lastSelectionRect) {
          createNodeFromSelection(selectedText, lastSelectionRect);
        }
      }, 10);
    };

    document.addEventListener('mouseup', handleMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isSelecting, selectedText, lastSelectionRect, createNodeFromSelection]);

  // 监听点击事件来隐藏创建的高亮工具栏
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      // 如果点击的不是工具栏或其子元素，则隐藏工具栏
      const target = e.target as Element;
      if (createdHighlight && !target.closest('.enhanced-floating-toolbar')) {
        setCreatedHighlight(null);
        setLastSelectionRect(null);
        // 重置创建标志
        isCreatingNode.current = false;
      }
    };

    // 监听整个文档的点击
    document.addEventListener('click', handleClick);
    
    // 特别监听编辑器的点击和mousedown（更快响应）
    const rootElement = editor.getRootElement();
    if (rootElement) {
      rootElement.addEventListener('click', handleClick);
      rootElement.addEventListener('mousedown', handleClick);
    }

    return () => {
      document.removeEventListener('click', handleClick);
      if (rootElement) {
        rootElement.removeEventListener('click', handleClick);
        rootElement.removeEventListener('mousedown', handleClick);
      }
    };
  }, [createdHighlight, editor]);

  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          updateToolbar();
          return false;
        },
        COMMAND_PRIORITY_LOW,
      ),
    );
  }, [editor, updateToolbar]);

  // 鼠标模式：显示选中文本的工具栏
  if (isText && !isLinkEditMode) {
    return createPortal(
      <EnhancedFloatingToolbar
        editor={editor}
        anchorElem={anchorElem}
        selectedText={selectedText}
        isCreatingNodeRef={isCreatingNode}
        onHighlightCreate={handleHighlightCreate}
        panelId={panelId}
      />,
      anchorElem,
    );
  }

  // 笔记模式：显示创建节点后的 PDF 风格工具栏
  if (createdHighlight && lastSelectionRect) {
    return createPortal(
      <div 
        ref={ref => {
          if (ref) {
            // 使用存储的选择位置来定位工具栏
            setFloatingElemPosition(lastSelectionRect, ref, anchorElem, false);
            ref.style.opacity = '1'; // 确保工具栏可见
          }
        }} 
        className="enhanced-floating-toolbar"
        onClick={(e) => e.stopPropagation()} // 防止点击工具栏时触发隐藏
        onMouseUp={(e) => e.stopPropagation()} // 防止工具栏上的mouseup事件冒泡
        onMouseDown={(e) => e.stopPropagation()} // 防止工具栏上的mousedown事件冒泡
        style={{ pointerEvents: 'auto' }} // 确保工具栏可以接收事件
      >
        <div
          onMouseUp={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <HighlightToolbar customHighlight={createdHighlight} hideNotebookButton={true} />
        </div>
      </div>,
      anchorElem,
    );
  }

  return null;
}