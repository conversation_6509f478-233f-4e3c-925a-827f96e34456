/**
 * 自定义样式转换器，支持字体、颜色等样式的 Markdown 保存和恢复
 */

import { 
  TextFormatTransformer, 
  TextMatchTransformer,
  ElementTransformer 
} from '@lexical/markdown';
import {
  $createTextNode,
  $isTextNode,
  TextNode,
  LexicalNode,
} from 'lexical';

// HTML 样式转换器 - 支持 span 标签中的样式
export const HTML_STYLE_TRANSFORMER: TextMatchTransformer = {
  dependencies: [],
  export: (node: LexicalNode) => {
    if (!$isTextNode(node)) {
      return null;
    }

    const textNode = node as TextNode;
    const text = textNode.getTextContent();
    const style = textNode.getStyle();
    
    if (!style) {
      return text;
    }

    // 将样式转换为 HTML span 标签
    return `<span style="${style}">${text}</span>`;
  },
  importRegExp: /<span style="([^"]*)">(.*?)<\/span>/,
  regExp: /<span style="([^"]*)">(.*?)<\/span>$/,
  replace: (textNode, match) => {
    const [, styleString, text] = match;
    const newTextNode = $createTextNode(text);
    
    // 解析并应用样式
    if (styleString) {
      newTextNode.setStyle(styleString);
    }
    
    textNode.replace(newTextNode);
  },
  trigger: '>',
  type: 'text-match',
};

// 字体颜色转换器 - 使用简化的 markdown 扩展语法
export const FONT_COLOR_TRANSFORMER: TextMatchTransformer = {
  dependencies: [],
  export: (node: LexicalNode) => {
    if (!$isTextNode(node)) {
      return null;
    }

    const textNode = node as TextNode;
    const text = textNode.getTextContent();
    const style = textNode.getStyle();
    
    if (!style) {
      return text;
    }

    // 提取颜色信息
    const colorMatch = style.match(/color:\s*([^;]+)/);
    const bgColorMatch = style.match(/background-color:\s*([^;]+)/);
    const fontFamilyMatch = style.match(/font-family:\s*([^;]+)/);
    const fontSizeMatch = style.match(/font-size:\s*([^;]+)/);

    let result = text;
    
    // 应用颜色标记
    if (colorMatch) {
      result = `{color:${colorMatch[1]}}${result}{/color}`;
    }
    
    if (bgColorMatch) {
      result = `{bg:${bgColorMatch[1]}}${result}{/bg}`;
    }
    
    if (fontFamilyMatch) {
      result = `{font:${fontFamilyMatch[1]}}${result}{/font}`;
    }
    
    if (fontSizeMatch) {
      result = `{size:${fontSizeMatch[1]}}${result}{/size}`;
    }

    return result;
  },
  importRegExp: /\{(color|bg|font|size):([^}]+)\}(.*?)\{\/\1\}/,
  regExp: /\{(color|bg|font|size):([^}]+)\}(.*?)\{\/\1\}$/,
  replace: (textNode, match) => {
    const [, styleType, styleValue, text] = match;
    const newTextNode = $createTextNode(text);
    
    // 构建样式字符串
    let styleString = '';
    switch (styleType) {
      case 'color':
        styleString = `color: ${styleValue}`;
        break;
      case 'bg':
        styleString = `background-color: ${styleValue}`;
        break;
      case 'font':
        styleString = `font-family: ${styleValue}`;
        break;
      case 'size':
        styleString = `font-size: ${styleValue}`;
        break;
    }
    
    newTextNode.setStyle(styleString);
    textNode.replace(newTextNode);
  },
  trigger: '}',
  type: 'text-match',
};

// 组合样式转换器 - 处理多个样式属性
export const COMBINED_STYLE_TRANSFORMER: TextMatchTransformer = {
  dependencies: [],
  export: (node: LexicalNode) => {
    if (!$isTextNode(node)) {
      return null;
    }

    const textNode = node as TextNode;
    const text = textNode.getTextContent();
    const style = textNode.getStyle();
    
    if (!style) {
      return text;
    }

    // 将所有样式信息编码到一个标记中
    const encodedStyle = btoa(style); // Base64 编码
    return `{style:${encodedStyle}}${text}{/style}`;
  },
  importRegExp: /\{style:([^}]+)\}(.*?)\{\/style\}/,
  regExp: /\{style:([^}]+)\}(.*?)\{\/style\}$/,
  replace: (textNode, match) => {
    const [, encodedStyle, text] = match;
    const newTextNode = $createTextNode(text);
    
    try {
      // Base64 解码样式
      const style = atob(encodedStyle);
      newTextNode.setStyle(style);
    } catch (error) {
      console.warn('Failed to decode style:', error);
    }
    
    textNode.replace(newTextNode);
  },
  trigger: '}',
  type: 'text-match',
};

// 保留原有格式的增强转换器 - 处理带有样式的格式化文本
export const ENHANCED_FORMAT_TRANSFORMERS: TextFormatTransformer[] = [
  // 粗体与样式结合
  {
    format: ['bold'],
    tag: '**',
    intraword: true,
    type: 'text-format',
  },
  // 斜体与样式结合
  {
    format: ['italic'],
    tag: '*',
    intraword: true,
    type: 'text-format',
  },
  // 下划线
  {
    format: ['underline'],
    tag: '<u>',
    type: 'text-format',
  },
  // 删除线
  {
    format: ['strikethrough'],
    tag: '~~',
    intraword: true,
    type: 'text-format',
  },
  // 代码
  {
    format: ['code'],
    tag: '`',
    type: 'text-format',
  },
];