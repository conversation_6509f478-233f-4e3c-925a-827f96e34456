/**
 * Mark 节点 Markdown 转换器
 * 将 Mark 节点序列化为自定义 Markdown 语法，支持双向转换
 */

import { TextMatchTransformer } from '@lexical/markdown';
import { $createMarkNode, $isMarkNode, MarkNode } from '@lexical/mark';
import { $createTextNode, $isTextNode, TextNode, LexicalNode } from 'lexical';

/**
 * Mark 节点转换器
 * 将 canvas-{nodeId} Mark 转换为 [text]{canvas-nodeId} 语法
 */
export const CANVAS_MARK_TRANSFORMER: TextMatchTransformer = {
  dependencies: [MarkNode],
  
  // 导出：将 Mark 节点转换为 Markdown
  export: (node: LexicalNode) => {
    if (!$isMarkNode(node)) {
      return null;
    }
    
    const ids = node.getIDs();
    const canvasId = ids.find((id: string) => id.startsWith('canvas-'));
    
    if (!canvasId) {
      return null;
    }
    
    // 获取 Mark 包含的子节点并检查是否有样式
    const children = node.getChildren();
    let hasStyles = false;
    let styledContent = '';
    
    // 遍历子节点，保留样式信息
    for (const child of children) {
      if ($isTextNode(child)) {
        const style = child.getStyle();
        if (style) {
          hasStyles = true;
          styledContent += `<span style="${style}">${child.getTextContent()}</span>`;
        } else {
          styledContent += child.getTextContent();
        }
      } else {
        styledContent += child.getTextContent();
      }
    }
    
    // 如果有样式，使用带样式的内容；否则使用纯文本
    const contentToSave = hasStyles ? styledContent : node.getTextContent();
    
    // 转换为自定义语法：[文本内容]{canvas-nodeId}
    return `[${contentToSave}]{${canvasId}}`;
  },
  
  // 导入：将 Markdown 转换为 Mark 节点  
  // 更新正则表达式以支持多行和复杂内容
  importRegExp: /\[([^\]]*(?:\[[^\]]*\])*[^\]]*)\]\{(canvas-[^}]+)\}/,
  regExp: /\[([^\]]*(?:\[[^\]]*\])*[^\]]*)\]\{(canvas-[^}]+)\}$/,
  
  // 这个函数用于导入时的转换
  replace: (textNode: TextNode, match: RegExpMatchArray) => {
    const [, textContent, canvasId] = match;
    
    // 创建 Mark 节点
    const markNode = $createMarkNode([canvasId]);
    
    // 检查内容是否包含样式
    const styleRegex = /<span style="([^"]*)">(.*?)<\/span>/g;
    let hasStyles = false;
    let match_span;
    let lastIndex = 0;
    
    // 解析样式内容
    while ((match_span = styleRegex.exec(textContent)) !== null) {
      hasStyles = true;
      const [fullMatch, styleString, spanText] = match_span;
      const matchStart = match_span.index;
      
      // 添加样式之前的普通文本
      if (matchStart > lastIndex) {
        const plainText = textContent.slice(lastIndex, matchStart);
        if (plainText) {
          markNode.append($createTextNode(plainText));
        }
      }
      
      // 添加带样式的文本节点
      const styledTextNode = $createTextNode(spanText);
      styledTextNode.setStyle(styleString);
      markNode.append(styledTextNode);
      
      lastIndex = matchStart + fullMatch.length;
    }
    
    // 如果没有样式或有剩余文本，添加普通文本节点
    if (!hasStyles) {
      // 没有样式，直接创建文本节点
      markNode.append($createTextNode(textContent));
    } else if (lastIndex < textContent.length) {
      // 有剩余的普通文本
      const remainingText = textContent.slice(lastIndex);
      if (remainingText) {
        markNode.append($createTextNode(remainingText));
      }
    }
    
    // 替换原文本节点
    textNode.replace(markNode);
  },
  
  trigger: '}',
  type: 'text-match',
};

/**
 * 扩展的 Mark 转换器（支持更多 Mark 类型）
 */
export const ENHANCED_MARK_TRANSFORMER: TextMatchTransformer = {
  dependencies: [MarkNode],
  
  export: (node: LexicalNode) => {
    if (!$isMarkNode(node)) {
      return null;
    }
    
    const ids = node.getIDs();
    const textContent = node.getTextContent();
    
    // 支持多种 Mark 类型，但排除 canvas- 类型（由专用转换器处理）
    const nonCanvasIds = ids.filter((id: string) => !id.startsWith('canvas-'));
    
    if (nonCanvasIds.length > 0) {
      const idList = nonCanvasIds.join(',');
      return `[${textContent}]{${idList}}`;
    }
    
    return null;
  },
  
  importRegExp: /\[([^\]]+)\]\{([^}]+)\}/,
  regExp: /\[([^\]]+)\]\{([^}]+)\}$/,
  
  replace: (textNode: TextNode, match: RegExpMatchArray) => {
    const [, textContent, idString] = match;
    
    // 过滤掉 canvas- 类型的 ID（由专用转换器处理）
    const ids = idString.split(',')
      .map((id: string) => id.trim())
      .filter((id: string) => id && !id.startsWith('canvas-'));
    
    if (ids.length === 0) {
      return;
    }
    
    // 创建 Mark 节点
    const markNode = $createMarkNode(ids);
    
    // 创建包含文本的文本节点
    const innerTextNode = $createTextNode(textContent);
    markNode.append(innerTextNode);
    
    // 替换原文本节点
    textNode.replace(markNode);
  },
  
  trigger: '}',
  type: 'text-match',
};