/**
 * Mark Highlight Plugin
 * 使用 Lexical Mark 节点实现双向跳转的高亮功能
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect, useState } from 'react';
import { 
  $getSelection, 
  $isRangeSelection,
  $createTextNode,
  COMMAND_PRIORITY_LOW,
  CLICK_COMMAND,
  $isTextNode,
  $setSelection
} from 'lexical';
import { $isMarkNode, MarkNode } from '@lexical/mark';
import { useReactFlowInstanceStore } from '@/store/reactflow-instance-store';
import { jumpToTextPositionInLexical, lexicalEditorManager } from '../../utils/lexicalJumpUtils';
import { createPortal } from 'react-dom';
import { HighlightToolbar } from '@/components/pdf/components/highlight/HighlightToolbar';
import { removeNode } from '@/api/canvas';
import { message } from 'antd';
import { useChatStore } from '@/store/workerspace-store/chat-store';

// 已弃用的函数，保持向后兼容
export function createCanvasMark() {}

/**
 * 查找包含指定画布节点ID的 Mark 节点
 * @param editor Lexical 编辑器实例
 * @param nodeId 画布节点ID
 * @returns Mark 节点或 null
 */
export function findMarkByCanvasNodeId(editor: any, nodeId: string): MarkNode | null {
  let foundMark: MarkNode | null = null;
  
  editor.getEditorState().read(() => {
    const root = editor.getEditorState()._nodeMap;
    
    for (const [, node] of root) {
      if ($isMarkNode(node)) {
        const ids = node.getIDs();
        if (ids.includes(`canvas-${nodeId}`)) {
          foundMark = node;
          break;
        }
      }
    }
  });
  
  return foundMark;
}


// 简化的跳转函数
export function jumpToMark(editor: any, markNode: MarkNode) {
  editor.update(() => {
    const textPosition = (markNode as any).__textPosition;
    if (textPosition) {
      jumpToTextPositionInLexical(editor, textPosition);
    }
  });
}

/**
 * Mark Highlight Plugin 组件
 */
export default function MarkHighlightPlugin({ 
  anchorElem = document.body 
}: { 
  anchorElem?: HTMLElement 
} = {}) {
  const [editor] = useLexicalComposerContext();
  const { instance } = useReactFlowInstanceStore();
  const { batchDelFooterRef } = useChatStore();
  
  // 注册编辑器到全局管理器
  useEffect(() => {
    lexicalEditorManager.setEditor(editor);
    
    return () => {
      lexicalEditorManager.clearEditor();
    };
  }, [editor]);
  
  // 工具栏状态管理 - 简化为类似 PDF Tip 的机制
  const [currentTip, setCurrentTip] = useState<{
    position: { top: number; left: number };
    nodeId: string;
    markNode: MarkNode;
    color: string;
    textContent: string;
    fileId?: string;
  } | null>(null);

  // 监听 Mark 节点状态，只有当 Mark 节点本身被删除时才隐藏工具栏
  useEffect(() => {
    const checkMarkExists = () => {
      if (currentTip) {
        // 检查 Mark 节点是否仍然存在于编辑器中
        const markStillExists = editor.getEditorState().read(() => {
          try {
            return currentTip.markNode.isAttached();
          } catch {
            return false; // Mark 节点已被删除或失效
          }
        });
        
        if (!markStillExists) {
          // Mark 节点已被删除，隐藏工具栏
          setCurrentTip(null);
        }
      }
    };

    // 定期检查 Mark 节点是否仍然存在（降低频率，减少性能消耗）
    const interval = setInterval(checkMarkExists, 2000);
    
    return () => {
      clearInterval(interval);
    };
  }, [currentTip, editor]);

  useEffect(() => {
    // 监听点击事件
    const removeClickListener = editor.registerCommand(
      CLICK_COMMAND,
      (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        
        // 如果点击的是工具栏，不处理
        if (target.closest('.mark-highlight-toolbar')) {
          return false;
        }
        
        // 检查是否点击了带有 Mark 的元素
        return editor.getEditorState().read(() => {
          const selection = $getSelection();
          if (!$isRangeSelection(selection)) return false;
          
          // 获取点击位置的节点
          const nodes = selection.getNodes();
          
          for (const node of nodes) {
            const parent = node.getParent();
            if ($isMarkNode(parent)) {
              const ids = parent.getIDs();
              const canvasId = ids.find(id => id.startsWith('canvas-'));
              
              if (canvasId) {
                event.preventDefault();
                
                // 提取画布节点ID
                const nodeId = canvasId.replace('canvas-', '');
                
                // 获取 Mark 节点的额外数据（安全读取）
                let highlightColor = '#FFFF00';
                let fileId: string | undefined;
                
                try {
                  highlightColor = (parent as any).__highlightColor || '#FFFF00';
                  fileId = (parent as any).__fileId;
                } catch (error) {
                  // 如果无法读取属性，使用默认值
                }
                const textContent = parent.getTextContent();
                // 跳转到画布节点
                jumpToCanvasNode(nodeId);
                
                // 显示工具栏
                showMarkToolbar(event, nodeId, parent, highlightColor, textContent, fileId);
                
                return true;
              }
            }
          }
          
          // 如果点击的不是 Mark，隐藏工具栏
          setCurrentTip(null);
          
          return false;
        });
      },
      COMMAND_PRIORITY_LOW
    );

    return () => {
      removeClickListener();
    };
  }, [editor, instance]);

  /**
   * 显示 Mark 工具栏 - 参考 PDF 高亮的简单实现
   */
  const showMarkToolbar = (
    event: MouseEvent,
    nodeId: string,
    markNode: MarkNode,
    color: string,
    textContent: string,
    fileId?: string
  ) => {
    // 获取被点击元素的位置
    const target = event.target as HTMLElement;
    const rect = target.getBoundingClientRect();
    
    // 获取编辑器容器的位置
    const rootElement = editor.getRootElement();
    if (!rootElement) return;
    
    const rootRect = rootElement.getBoundingClientRect();
    
    // 计算相对于编辑器的位置（参考 PDF 高亮的简单计算）
    const relativePosition = {
      top: rect.top - rootRect.top - 60, // 工具栏显示在元素上方
      left: rect.left - rootRect.left
    };
    
    // 设置 Tip（类似 PDF 高亮的机制）
    setCurrentTip({
      position: relativePosition,
      nodeId,
      markNode,
      color,
      textContent,
      fileId
    });
  };

  /**
   * 跳转到指定的画布节点
   */
  const jumpToCanvasNode = (nodeId: string) => {
    if (!instance) return;

    try {
      // 获取目标节点
      const targetNode = instance.getNode(nodeId);
      if (!targetNode) {
        return;
      }

      // 先取消所有节点选中
      const allNodes = instance.getNodes();
      const updatedNodes = allNodes.map(node => ({
        ...node,
        selected: node.id === nodeId,
        selectedFromMark: node.id === nodeId
      }));
      
      // 更新节点状态
      instance.setNodes(updatedNodes);

      // 获取当前视口缩放比例
      const viewport = instance.getViewport();
      
      // 居中显示目标节点
      instance.setCenter(targetNode.position.x, targetNode.position.y, {
        zoom: viewport.zoom,
        duration: 500
      });

    } catch (error) {
    }
  };

  /**
   * 处理 Mark 删除 - 支持孤儿 Mark 节点
   */
  const handleMarkDelete = async () => {
    if (!currentTip) return;

    const nodeId = currentTip.nodeId;
    const markNode = currentTip.markNode;

    try {
      // 1. 删除画布节点（如果还存在）
      if (instance?.getNode(nodeId)) {
        await removeNode([nodeId]);
        const allNodes = instance.getNodes();
        instance.setNodes(allNodes.filter(node => node.id !== nodeId));
        batchDelFooterRef([nodeId]);
      }
      // 如果画布节点不存在，说明是孤儿 Mark，只需删除 Mark 节点

      // 2. 删除 Mark 节点（保留文本内容）
      editor.update(() => {
        $setSelection(null);
        const textContent = markNode.getTextContent();
        const textNode = $createTextNode(textContent);
        markNode.replace(textNode);
      });
    } catch (error) {
      message.error("删除失败");
      console.error('删除操作失败:', error);
    } finally {
      setCurrentTip(null);
    }
  };

  /**
   * 处理 Mark 颜色变更 - 支持孤儿 Mark 节点
   */
  const handleColorChange = (newColor: string) => {
    if (!currentTip) return;

    // 1. 更新 Mark 高亮颜色
    editor.update(() => {
      const markNode = currentTip.markNode;
      const children = markNode.getChildren();
      
      children.forEach(child => {
        if ($isTextNode(child)) {
          const style = `background-color: ${newColor}; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: all 0.2s ease`;
          child.setStyle(style);
        }
      });
    });

    // 2. 更新画布节点颜色（如果还存在）
    if (instance?.getNode(currentTip.nodeId)) {
      const allNodes = instance.getNodes();
      const updatedNodes = allNodes.map(node => 
        node.id === currentTip.nodeId 
          ? { ...node, data: { ...node.data, color: newColor } }
          : node
      );
      instance.setNodes(updatedNodes);
    }
    // 如果画布节点不存在，说明是孤儿 Mark，只更新 Mark 自身即可

    // 3. 更新本地状态
    setCurrentTip({ ...currentTip, color: newColor });
  };


  // 监听点击文档其他地方来隐藏工具栏
  useEffect(() => {
    const handleDocumentClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // 如果点击的不是工具栏或其子元素，则隐藏工具栏
      if (!target.closest('.mark-highlight-toolbar')) {
        setCurrentTip(null);
      }
    };

    if (currentTip) {
      document.addEventListener('click', handleDocumentClick);
    }

    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [currentTip]);

  // 渲染工具栏 - 简化版本，参考 PDF 高亮
  const renderToolbar = () => {
    if (!currentTip) {
      return null;
    }

    // 创建虚拟高亮对象，复用 HighlightToolbar
    // 使用 onDelete 回调处理删除，这样可以灵活处理节点存在/不存在的情况
    const virtualHighlight = {
      id: `mark-${currentTip.nodeId}`,
      aid: currentTip.fileId || 'lexical-editor',
      type: 'text' as const,
      content: { text: currentTip.textContent },
      position: {
        boundingRect: { width: 0, height: 0, pageNumber: 1 },
        rects: []
      } as any,
      color: currentTip.color,
      nid: '', // 空字符串让 HighlightToolbar 使用 onDelete 回调
      // 添加 HighlightToolbar 组件需要的额外属性
      __highlightColor: currentTip.color,
    };

    return createPortal(
      <div
        className="mark-highlight-toolbar"
        style={{
          position: 'absolute',
          top: currentTip.position.top,
          left: currentTip.position.left,
          zIndex: 1000,
          pointerEvents: 'auto'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <HighlightToolbar
          customHighlight={virtualHighlight}
          onColorChange={handleColorChange}
          onDelete={handleMarkDelete}
          hideNotebookButton={true}
        />
      </div>,
      anchorElem
    );
  };

  return (
    <>
      {renderToolbar()}
    </>
  );
}