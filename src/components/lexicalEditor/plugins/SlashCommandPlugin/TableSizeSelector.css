.table-size-selector {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 16px;
  min-width: 320px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.table-size-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.table-size-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.table-size-dimension {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
  background: #eff6ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.table-grid {
  display: grid;
  gap: 2px;
  margin-bottom: 12px;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
}

.table-cell {
  width: 16px;
  height: 16px;
  border: 1px solid #d1d5db;
  background: white;
  cursor: pointer;
  transition: all 0.15s ease;
  border-radius: 2px;
}

.table-cell:hover {
  border-color: #3b82f6;
}

.table-cell.selected {
  background: #3b82f6;
  border-color: #2563eb;
}

.table-size-footer {
  text-align: center;
}

.table-size-hint {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

/* 动画效果 */
.table-size-selector {
  animation: tableSelector-fadeIn 0.15s ease-out;
}

@keyframes tableSelector-fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .table-size-selector {
    min-width: 280px;
    padding: 12px;
  }
  
  .table-grid {
    padding: 6px;
  }
  
  .table-cell {
    width: 14px;
    height: 14px;
  }
}