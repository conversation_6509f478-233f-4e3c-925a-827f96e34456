import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $getSelection,
  $isRangeSelection,
  TextNode,
} from 'lexical';
import { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import type { JSX } from 'react';

import { $createHeadingNode } from '@lexical/rich-text';
import { INSERT_HORIZONTAL_RULE_COMMAND } from '@lexical/react/LexicalHorizontalRuleNode';
import { INSERT_TABLE_COMMAND } from '@lexical/table';
import { formatQuote, formatCode, formatBulletList, formatNumberedList, formatCheckList } from '../ToolbarPlugin/utils';
import TableSizeSelector from './TableSizeSelector';

import './SlashCommandPlugin.css';

type SlashCommand = {
  key: string;
  label: string;
  icon: string;
  group: string;
  keywords: string[];
  onSelect: (editor: any) => void;
};

const SLASH_COMMANDS: SlashCommand[] = [
  // AI 相关
  {
    key: 'ai-create',
    label: 'AI 创作',
    icon: '✨',
    group: 'AI',
    keywords: ['ai', 'create', '创作', '生成'],
    onSelect: (_editor) => {
      // TODO: 实现AI创作功能
      console.log('AI创作功能待实现');
    },
  },
  
  // 基础文本
  {
    key: 'text',
    label: '文本',
    icon: 'T',
    group: '基础',
    keywords: ['text', '文本', 'paragraph'],
    onSelect: (_editor) => {
      // 默认就是文本，不需要特殊处理
    },
  },
  {
    key: 'heading1',
    label: '一级标题',
    icon: 'H1',
    group: '基础',
    keywords: ['heading', 'h1', '标题', '一级'],
    onSelect: (editor) => {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const headingNode = $createHeadingNode('h1');
          selection.insertNodes([headingNode]);
        }
      });
    },
  },
  {
    key: 'heading2',
    label: '二级标题',
    icon: 'H2',
    group: '基础',
    keywords: ['heading', 'h2', '标题', '二级'],
    onSelect: (editor) => {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const headingNode = $createHeadingNode('h2');
          selection.insertNodes([headingNode]);
        }
      });
    },
  },
  {
    key: 'heading3',
    label: '三级标题',
    icon: 'H3',
    group: '基础',
    keywords: ['heading', 'h3', '标题', '三级'],
    onSelect: (editor) => {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const headingNode = $createHeadingNode('h3');
          selection.insertNodes([headingNode]);
        }
      });
    },
  },
  {
    key: 'quote',
    label: '引用',
    icon: '💬',
    group: '基础',
    keywords: ['quote', 'blockquote', '引用'],
    onSelect: (editor) => {
      formatQuote(editor, 'paragraph');
    },
  },
  {
    key: 'code-block',
    label: '代码块',
    icon: '{}',
    group: '基础',
    keywords: ['code', 'codeblock', '代码', '代码块'],
    onSelect: (editor) => {
      formatCode(editor, 'paragraph');
    },
  },
  {
    key: 'bullet-list',
    label: '有序列表',
    icon: '1.',
    group: '基础',
    keywords: ['list', 'ol', 'numbered', '有序', '列表', '数字'],
    onSelect: (editor) => {
      formatNumberedList(editor, 'paragraph');
    },
  },
  {
    key: 'numbered-list',
    label: '无序列表',
    icon: '•',
    group: '基础',
    keywords: ['list', 'ul', 'bullet', '无序', '列表', '圆点'],
    onSelect: (editor) => {
      formatBulletList(editor, 'paragraph');
    },
  },
  {
    key: 'horizontal-rule',
    label: '分隔线',
    icon: '—',
    group: '基础',
    keywords: ['hr', 'horizontal', 'rule', '分隔线', '分割线'],
    onSelect: (editor) => {
      editor.dispatchCommand(INSERT_HORIZONTAL_RULE_COMMAND, undefined);
    },
  },
  {
    key: 'link',
    label: '链接',
    icon: '🔗',
    group: '基础',
    keywords: ['link', 'url', '链接', '超链接'],
    onSelect: (_editor) => {
      // TODO: 实现链接插入
      console.log('链接插入功能待实现');
    },
  },
  
  // 常用功能
  {
    key: 'task',
    label: '任务',
    icon: '☑️',
    group: '常用',
    keywords: ['task', 'todo', 'checklist', '任务', '待办', '复选框'],
    onSelect: (editor) => {
      formatCheckList(editor, 'paragraph');
    },
  },
  {
    key: 'table',
    label: '表格',
    icon: '📊',
    group: '常用',
    keywords: ['table', '表格'],
    onSelect: () => {
      // 这里不直接插入表格，而是显示表格尺寸选择器
      // 实际插入逻辑会在 handleTableSelect 中处理
    },
  },
  {
    key: 'image',
    label: '图片',
    icon: '🖼️',
    group: '常用',
    keywords: ['image', 'img', '图片', '图像'],
    onSelect: (_editor) => {
      // TODO: 实现图片上传
      console.log('图片上传功能待实现');
    },
  },
  {
    key: 'divider',
    label: '分栏',
    icon: '📑',
    group: '常用',
    keywords: ['divider', 'column', '分栏', '分列'],
    onSelect: (_editor) => {
      // TODO: 实现分栏功能
      console.log('分栏功能待实现');
    },
  },
  {
    key: 'highlight',
    label: '高亮块',
    icon: '📋',
    group: '常用',
    keywords: ['highlight', 'callout', '高亮', '高亮块'],
    onSelect: (_editor) => {
      // TODO: 实现高亮块功能
      console.log('高亮块功能待实现');
    },
  },
];

interface SlashCommandMenuProps {
  searchText: string;
  position: { x: number; y: number };
  filteredCommands: SlashCommand[];
  selectedIndex: number;
  executeCommand: (command: SlashCommand) => void;
  setSelectedIndex: (index: number) => void;
}

function SlashCommandMenu({
  searchText,
  position,
  filteredCommands,
  selectedIndex,
  executeCommand,
  setSelectedIndex,
}: SlashCommandMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  // 按组分组
  const groupedCommands = filteredCommands.reduce((groups, command) => {
    if (!groups[command.group]) {
      groups[command.group] = [];
    }
    groups[command.group].push(command);
    return groups;
  }, {} as Record<string, SlashCommand[]>);

  const allCommands = Object.values(groupedCommands).flat();

  if (allCommands.length === 0) {
    return null;
  }

  return createPortal(
    <div
      ref={menuRef}
      className="slash-command-menu"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 1000,
      }}
    >
      <div className="slash-command-search">
        <span className="slash-command-prefix">/</span>
        <span className="slash-command-search-text">{searchText}</span>
      </div>
      
      {Object.entries(groupedCommands).map(([groupName, commands]) => (
        <div key={groupName} className="slash-command-group">
          <div className="slash-command-group-title">{groupName}</div>
          {commands.map((command, _index) => {
            const globalIndex = allCommands.indexOf(command);
            return (
              <div
                key={command.key}
                className={`slash-command-item ${
                  globalIndex === selectedIndex ? 'selected' : ''
                }`}
                onClick={() => executeCommand(command)}
                onMouseEnter={() => setSelectedIndex(globalIndex)}
              >
                <span className="slash-command-icon">{command.icon}</span>
                <span className="slash-command-label">{command.label}</span>
              </div>
            );
          })}
        </div>
      ))}
    </div>,
    document.body
  );
}

export default function SlashCommandPlugin(): JSX.Element | null {
  const [editor] = useLexicalComposerContext();
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showTableSelector, setShowTableSelector] = useState(false);
  const [tableSelectorPosition, setTableSelectorPosition] = useState({ x: 0, y: 0 });

  // 计算过滤后的命令
  const filteredCommands = SLASH_COMMANDS.filter((command) =>
    command.keywords.some((keyword) =>
      keyword.toLowerCase().includes(searchText.toLowerCase())
    ) || command.label.toLowerCase().includes(searchText.toLowerCase())
  );

  const deleteSlashText = useCallback(() => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // 删除斜杠和搜索文本
        const anchor = selection.anchor;
        const anchorNode = anchor.getNode();
        
        if (anchorNode instanceof TextNode) {
          const textContent = anchorNode.getTextContent();
          const currentOffset = anchor.offset;
          
          // 向前查找斜杠的位置
          let slashIndex = -1;
          for (let i = currentOffset - 1; i >= 0; i--) {
            if (textContent[i] === '/') {
              // 检查斜杠前面是否是空白字符或者在行首
              if (i === 0 || /\s/.test(textContent[i - 1])) {
                slashIndex = i;
                break;
              }
            }
          }
          
          if (slashIndex !== -1) {
            // 选择从斜杠到当前位置的文本并删除
            const textNode = anchorNode;
            textNode.select(slashIndex, currentOffset);
            
            // 删除选中的文本（包括斜杠和搜索词）
            selection.removeText();
          }
        }
      }
    });
  }, [editor]);

  const showTableSizeSelector = useCallback((menuPosition: { x: number; y: number }) => {
    // 计算表格选择器的位置
    const selectorWidth = 320;
    const selectorHeight = 300;
    const menuWidth = 320;
    const padding = 10;
    
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 默认放在菜单右侧
    let x = menuPosition.x + menuWidth + 10;
    let y = menuPosition.y;
    
    // 如果右侧空间不够，放在菜单左侧
    if (x + selectorWidth > viewportWidth - padding) {
      x = menuPosition.x - selectorWidth - 10;
      // 如果左侧也不够，则贴着视口右边
      if (x < padding) {
        x = viewportWidth - selectorWidth - padding;
      }
    }
    
    // 检查垂直位置
    if (y + selectorHeight > viewportHeight - padding) {
      y = viewportHeight - selectorHeight - padding;
    }
    if (y < padding) {
      y = padding;
    }
    
    setTableSelectorPosition({ x, y });
    setShowTableSelector(true);
  }, []);

  const executeCommand = useCallback(
    (command: SlashCommand) => {
      // 如果是表格命令，显示表格尺寸选择器
      if (command.key === 'table') {
        showTableSizeSelector(position);
        return;
      }

      deleteSlashText();

      // 延迟执行命令，确保删除操作完成
      setTimeout(() => {
        command.onSelect(editor);
      }, 0);
      
      setIsOpen(false);
      setSearchText('');
    },
    [editor, position, showTableSizeSelector, deleteSlashText]
  );

  // 滚动到选中项的函数
  const scrollToSelectedItem = useCallback((index: number) => {
    // 延迟执行确保DOM已更新
    setTimeout(() => {
      const menuElement = document.querySelector('.slash-command-menu');
      if (!menuElement) return;
      
      // 通过索引找到对应的命令项
      const allItems = menuElement.querySelectorAll('.slash-command-item');
      const selectedElement = allItems[index];
      
      if (selectedElement) {
        const menuHeight = menuElement.clientHeight;
        const itemTop = (selectedElement as HTMLElement).offsetTop;
        const itemHeight = (selectedElement as HTMLElement).offsetHeight;
        const itemBottom = itemTop + itemHeight;
        
        const scrollTop = menuElement.scrollTop;
        const scrollBottom = scrollTop + menuHeight;
        
        // 检查是否需要滚动
        if (itemTop < scrollTop) {
          // 选中项在视口上方，滚动到顶部
          menuElement.scrollTop = itemTop;
        } else if (itemBottom > scrollBottom) {
          // 选中项在视口下方，滚动到底部
          menuElement.scrollTop = itemBottom - menuHeight;
        }
      }
    }, 0);
  }, []);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    setSearchText('');
    setSelectedIndex(0); // 重置选中索引
    setShowTableSelector(false); // 同时关闭表格选择器
  }, []);

  const handleTableSelect = useCallback((rows: number, cols: number) => {
    deleteSlashText();
    
    setTimeout(() => {
      editor.dispatchCommand(INSERT_TABLE_COMMAND, {
        columns: cols.toString(),
        rows: rows.toString(),
        includeHeaders: true,
      });
    }, 0);
    
    setShowTableSelector(false);
    setIsOpen(false);
    setSearchText('');
    setSelectedIndex(0);
  }, [editor, deleteSlashText]);

  const handleTableSelectorClose = useCallback(() => {
    setShowTableSelector(false);
  }, []);

  // 重置选中索引当搜索结果改变时
  useEffect(() => {
    if (isOpen) {
      setSelectedIndex(0);
      // 延迟滚动，确保菜单渲染完成
      if (filteredCommands.length > 0) {
        setTimeout(() => scrollToSelectedItem(0), 10);
      }
    }
  }, [searchText, isOpen, scrollToSelectedItem, filteredCommands.length]);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const selection = $getSelection();
        if (!$isRangeSelection(selection) || !selection.isCollapsed()) {
          if (isOpen) {
            setIsOpen(false);
            setSearchText('');
            setSelectedIndex(0);
            setShowTableSelector(false); // 重置表格选择器状态
          }
          return;
        }

        const anchor = selection.anchor;
        const anchorNode = anchor.getNode();
        
        if (!(anchorNode instanceof TextNode)) {
          if (isOpen) {
            setIsOpen(false);
            setSearchText('');
            setSelectedIndex(0);
            setShowTableSelector(false); // 重置表格选择器状态
          }
          return;
        }

        const textBeforeCursor = anchorNode.getTextContent().slice(0, anchor.offset);
        const slashMatch = textBeforeCursor.match(/(^|\s)\/([a-zA-Z\u4e00-\u9fa5]*)$/);
        
        if (slashMatch) {
          const searchTerm = slashMatch[2] || '';
          setSearchText(searchTerm);
          
          if (!isOpen) {
            // 重置选中索引
            setSelectedIndex(0);
            // 计算菜单位置
            setTimeout(() => {
              const domSelection = window.getSelection();
              if (domSelection && domSelection.rangeCount > 0) {
                const range = domSelection.getRangeAt(0);
                const rect = range.getBoundingClientRect();
                
                // 菜单的估计尺寸
                const menuWidth = 320;
                const menuHeight = 400; // 最大高度
                const padding = 10;
                
                // 视口尺寸
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                
                // 计算 x 位置
                let x = rect.left;
                if (x + menuWidth > viewportWidth - padding) {
                  x = viewportWidth - menuWidth - padding;
                }
                if (x < padding) {
                  x = padding;
                }
                
                // 计算 y 位置
                let y = rect.bottom + 5;
                // 如果菜单会超出视口底部，显示在光标上方
                if (y + menuHeight > viewportHeight - padding) {
                  y = rect.top - menuHeight - 5;
                  // 如果上方也不够空间，则贴着视口底部显示
                  if (y < padding) {
                    y = viewportHeight - menuHeight - padding;
                  }
                }
                
                setPosition({ x, y });
              }
            }, 0);
            setIsOpen(true);
          }
        } else if (isOpen) {
          setIsOpen(false);
          setSearchText('');
          setSelectedIndex(0);
          setShowTableSelector(false); // 重置表格选择器状态
        }
      });
    });
  }, [editor, isOpen]);

  // 注册键盘命令
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // 如果表格选择器打开，不处理键盘事件，让表格选择器自己处理
      if (showTableSelector) return;
      
      if (event.key === 'ArrowUp') {
        event.preventDefault();
        event.stopPropagation();
        setSelectedIndex((prev) => {
          const commands = filteredCommands;
          const newIndex = prev > 0 ? prev - 1 : commands.length - 1;
          scrollToSelectedItem(newIndex);
          return newIndex;
        });
        return;
      } else if (event.key === 'ArrowDown') {
        event.preventDefault();
        event.stopPropagation();
        setSelectedIndex((prev) => {
          const commands = filteredCommands;
          const newIndex = prev < commands.length - 1 ? prev + 1 : 0;
          scrollToSelectedItem(newIndex);
          return newIndex;
        });
        return;
      } else if (event.key === 'Enter') {
        event.preventDefault();
        event.stopPropagation();
        const commands = filteredCommands;
        if (commands[selectedIndex]) {
          executeCommand(commands[selectedIndex]);
        }
        return;
      } else if (event.key === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
        handleClose();
        return;
      }
    };

    // 使用 capture 阶段来确保我们的事件处理器优先执行
    document.addEventListener('keydown', handleKeyDown, true);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [isOpen, selectedIndex, filteredCommands, executeCommand, scrollToSelectedItem, handleClose, showTableSelector]);

  if (!isOpen) {
    return null;
  }

  return (
    <>
      <SlashCommandMenu
        searchText={searchText}
        position={position}
        filteredCommands={filteredCommands}
        selectedIndex={selectedIndex}
        executeCommand={executeCommand}
        setSelectedIndex={setSelectedIndex}
      />
      <TableSizeSelector
        isOpen={showTableSelector}
        onSelect={handleTableSelect}
        onClose={handleTableSelectorClose}
        position={tableSelectorPosition}
      />
    </>
  );
}