import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import type { JSX } from 'react';
import './TableSizeSelector.css';

interface TableSizeSelectorProps {
  isOpen: boolean;
  onSelect: (rows: number, cols: number) => void;
  onClose: () => void;
  position: { x: number; y: number };
}

export default function TableSizeSelector({
  isOpen,
  onSelect,
  onClose,
  position,
}: TableSizeSelectorProps): JSX.Element | null {
  const [hoveredRows, setHoveredRows] = useState(3);
  const [hoveredCols, setHoveredCols] = useState(3);
  const selectorRef = useRef<HTMLDivElement>(null);

  const maxRows = 10;
  const maxCols = 10;

  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
        onClose();
      } else if (event.key === 'Enter') {
        event.preventDefault();
        event.stopPropagation();
        onSelect(hoveredRows, hoveredCols);
      } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        event.stopPropagation();
        setHoveredRows(prev => Math.max(1, prev - 1));
      } else if (event.key === 'ArrowDown') {
        event.preventDefault();
        event.stopPropagation();
        setHoveredRows(prev => Math.min(maxRows, prev + 1));
      } else if (event.key === 'ArrowLeft') {
        event.preventDefault();
        event.stopPropagation();
        setHoveredCols(prev => Math.max(1, prev - 1));
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        event.stopPropagation();
        setHoveredCols(prev => Math.min(maxCols, prev + 1));
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown, true); // 使用 capture 阶段确保优先级

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [isOpen, onClose, onSelect, hoveredRows, hoveredCols]);

  const handleCellHover = (row: number, col: number) => {
    setHoveredRows(row);
    setHoveredCols(col);
  };

  const handleCellClick = () => {
    onSelect(hoveredRows, hoveredCols);
  };

  if (!isOpen) return null;

  const cells = [];
  for (let row = 1; row <= maxRows; row++) {
    for (let col = 1; col <= maxCols; col++) {
      const isSelected = row <= hoveredRows && col <= hoveredCols;
      cells.push(
        <div
          key={`${row}-${col}`}
          className={`table-cell ${isSelected ? 'selected' : ''}`}
          onMouseEnter={() => handleCellHover(row, col)}
          onClick={handleCellClick}
        />
      );
    }
  }

  return createPortal(
    <div
      ref={selectorRef}
      className="table-size-selector"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 1000,
      }}
    >
      <div className="table-size-header">
        <span className="table-size-title">插入支持富文本的表格</span>
        <span className="table-size-dimension">{hoveredCols} x {hoveredRows}</span>
      </div>
      
      <div 
        className="table-grid"
        style={{
          gridTemplateColumns: `repeat(${maxCols}, 1fr)`,
          gridTemplateRows: `repeat(${maxRows}, 1fr)`,
        }}
      >
        {cells}
      </div>
      
      <div className="table-size-footer">
        <span className="table-size-hint">
          使用方向键调整尺寸，回车确认，ESC 取消
        </span>
      </div>
    </div>,
    document.body
  );
}