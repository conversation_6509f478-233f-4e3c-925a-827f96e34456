.slash-command-menu {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 320px;
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.slash-command-search {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin-bottom: 4px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  color: #6b7280;
}

.slash-command-prefix {
  font-weight: 600;
  color: #374151;
  margin-right: 4px;
}

.slash-command-search-text {
  color: #111827;
  font-weight: 500;
}

.slash-command-group {
  margin-bottom: 4px;
}

.slash-command-group:last-child {
  margin-bottom: 0;
}

.slash-command-group-title {
  padding: 6px 16px 4px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.slash-command-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  font-size: 14px;
}

.slash-command-item:hover,
.slash-command-item.selected {
  background-color: #f3f4f6;
}

.slash-command-item.selected {
  background-color: #dbeafe;
}

.slash-command-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  font-size: 16px;
  border-radius: 4px;
  background-color: #f9fafb;
  flex-shrink: 0;
}

.slash-command-item.selected .slash-command-icon {
  background-color: #bfdbfe;
}

.slash-command-label {
  flex: 1;
  color: #111827;
  font-weight: 500;
}

.slash-command-item.selected .slash-command-label {
  color: #1e40af;
}

/* 特殊图标样式 */
.slash-command-icon.text-icon {
  font-family: 'Times New Roman', serif;
  font-weight: bold;
  font-size: 14px;
  color: #3b82f6;
}

.slash-command-icon.heading-icon {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  font-size: 12px;
  color: #6366f1;
}

/* 滚动条样式 */
.slash-command-menu::-webkit-scrollbar {
  width: 6px;
}

.slash-command-menu::-webkit-scrollbar-track {
  background: transparent;
}

.slash-command-menu::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.slash-command-menu::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .slash-command-menu {
    max-width: 280px;
    min-width: 240px;
  }
  
  .slash-command-item {
    padding: 10px 16px;
  }
}