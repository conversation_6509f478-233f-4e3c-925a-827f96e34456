import { useState } from 'react';
import TableOfContentsPlugin, { TocItem } from './TableOfContentsPlugin';
import { TableOfContentsUI } from './TableOfContentsUI';
import { useTocNavigation } from './useTocNavigation';
import './toc.css';

interface TableOfContentsComponentProps {
  className?: string;
}

export function TableOfContentsComponent({ className }: TableOfContentsComponentProps) {
  const [toc, setToc] = useState<TocItem[]>([]);
  const { scrollToHeading } = useTocNavigation();

  const handleItemClick = (item: TocItem) => {
    scrollToHeading(item);
  };

  return (
    <>
      {/* 插件：负责数据提取 */}
      <TableOfContentsPlugin onTocChange={setToc} />
      
      {/* UI：负责展示和交互 */}
      <TableOfContentsUI
        toc={toc}
        onItemClick={handleItemClick}
        className={className}
      />
    </>
  );
}