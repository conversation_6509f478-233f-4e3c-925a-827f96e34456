import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getRoot } from 'lexical';
import { $isHeadingNode } from '@lexical/rich-text';
import { useEffect, useState } from 'react';
import { mergeRegister } from '@lexical/utils';

export interface TocItem {
  id: string;
  text: string;
  level: number; // 1-6 对应 H1-H6
  element?: Element;
}

export interface TableOfContentsPluginProps {
  onTocChange?: (toc: TocItem[]) => void;
}

export default function TableOfContentsPlugin({ onTocChange }: TableOfContentsPluginProps) {
  const [editor] = useLexicalComposerContext();

  const extractToc = () => {
    editor.getEditorState().read(() => {
      const root = $getRoot();
      const headings: TocItem[] = [];
      
      // 遍历所有子节点寻找标题
      const nodes = root.getChildren();
      
      nodes.forEach((node, index) => {
        if ($isHeadingNode(node)) {
          const text = node.getTextContent();
          const level = Number(node.getTag().replace('h', ''));
          
          // 生成唯一 ID
          const id = `heading-${index}-${text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '')}`;
          
          headings.push({
            id,
            text,
            level,
          });
        }
      });

      onTocChange?.(headings);
    });
  };

  useEffect(() => {
    // 初始化时立即提取目录
    extractToc();

    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          const root = $getRoot();
          const headings: TocItem[] = [];
          
          // 遍历所有子节点寻找标题
          const nodes = root.getChildren();
          
          nodes.forEach((node, index) => {
            if ($isHeadingNode(node)) {
              const text = node.getTextContent();
              const level = Number(node.getTag().replace('h', ''));
              
              // 生成唯一 ID
              const id = `heading-${index}-${text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '')}`;
              
              headings.push({
                id,
                text,
                level,
              });
            }
          });

          onTocChange?.(headings);
        });
      })
    );
  }, [editor, onTocChange]);

  // 此插件不渲染任何 UI，只提供数据
  return null;
}