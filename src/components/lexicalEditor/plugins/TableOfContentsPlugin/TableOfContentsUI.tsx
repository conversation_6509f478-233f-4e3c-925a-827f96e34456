import { useState } from 'react';
import { TocItem } from './TableOfContentsPlugin';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight, List } from 'lucide-react';

interface TableOfContentsUIProps {
  toc: TocItem[];
  onItemClick?: (item: TocItem) => void;
  className?: string;
}

export function TableOfContentsUI({ toc, onItemClick, className }: TableOfContentsUIProps) {
  const [collapsed, setCollapsed] = useState(false);

  if (toc.length === 0) {
    return (
      <div className={cn("p-4 text-sm text-muted-foreground", className)}>
        暂无目录
      </div>
    );
  }

  return (
    <div 
      className={cn("bg-background", className)}
      onWheel={(e) => e.stopPropagation()}
      onScroll={(e) => e.stopPropagation()}
    >
      {/* 目录列表 - 直接显示，无边框 */}
      <div className="space-y-1">
        {toc.map((item, index) => (
          <div
            key={`${item.id}-${index}`}
            className={cn(
              "flex items-start py-2 px-2 cursor-pointer text-sm hover:bg-gray-50 transition-colors rounded-md",
              // 根据标题级别添加左边距
              {
                "pl-2": item.level === 1,
                "pl-6": item.level === 2,
                "pl-10": item.level === 3,
                "pl-14": item.level === 4,
                "pl-18": item.level === 5,
                "pl-22": item.level === 6,
              }
            )}
            onClick={() => onItemClick?.(item)}
          >
            {/* 标题文本 */}
            <span 
              className={cn(
                "text-gray-700 leading-relaxed",
                {
                  "font-semibold text-gray-900": item.level === 1,
                  "font-medium text-gray-800": item.level === 2,
                  "font-normal text-gray-700": item.level >= 3,
                }
              )}
              title={item.text}
            >
              {item.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}