import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getNodeByKey } from 'lexical';
import { $isHeadingNode } from '@lexical/rich-text';
import { TocItem } from './TableOfContentsPlugin';

export function useTocNavigation() {
  const [editor] = useLexicalComposerContext();

  const scrollToHeading = (item: TocItem) => {
    editor.getEditorState().read(() => {
      // 方法1: 通过节点 key 查找
      const node = $getNodeByKey(item.id);
      if (node && $isHeadingNode(node)) {
        // 获取编辑器容器
        const editorContainer = editor.getRootElement();
        if (editorContainer) {
          // 查找对应的 DOM 元素
          const headingElement = editorContainer.querySelector(`[data-lexical-editor] h${item.level}`);
          
          if (headingElement && headingElement.textContent?.includes(item.text)) {
            // 找到滚动容器（编辑器的容器）
            const scrollContainer = editorContainer.closest('.editor-container') as HTMLElement;
            
            if (scrollContainer) {
              // 计算标题元素相对于滚动容器的位置
              const headingRect = headingElement.getBoundingClientRect();
              const containerRect = scrollContainer.getBoundingClientRect();
              
              // 计算需要滚动的距离，减去一些偏移量（为工具栏留空间）
              // const toolbarOffset = 60; // 工具栏高度的近似值
              const scrollTop = scrollContainer.scrollTop + headingRect.top - containerRect.top;
              
              // 平滑滚动到计算出的位置
              scrollContainer.scrollTo({
                top: Math.max(0, scrollTop),
                behavior: 'smooth'
              });
            } else {
              // 降级方案：使用原来的 scrollIntoView
              headingElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest'
              });
            }
            
            // 可选: 高亮显示
            headingElement.classList.add('highlight-heading');
            setTimeout(() => {
              headingElement.classList.remove('highlight-heading');
            }, 2000);
            
            return;
          }
        }
      }

      // 方法2: 如果方法1失败，通过文本内容查找
      const editorContainer = editor.getRootElement();
      if (editorContainer) {
        const allHeadings = editorContainer.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const targetHeading = Array.from(allHeadings).find(
          heading => heading.textContent === item.text
        );
        
        if (targetHeading) {
          // 找到滚动容器（编辑器的容器）
          const scrollContainer = editorContainer.closest('.editor-container') as HTMLElement;
          
          if (scrollContainer) {
            // 计算标题元素相对于滚动容器的位置
            const headingRect = targetHeading.getBoundingClientRect();
            const containerRect = scrollContainer.getBoundingClientRect();
            
            // 计算需要滚动的距离，减去一些偏移量（为工具栏留空间）
            // const toolbarOffset = 60; // 工具栏高度的近似值
            const scrollTop = scrollContainer.scrollTop + headingRect.top - containerRect.top;
            
            // 平滑滚动到计算出的位置
            scrollContainer.scrollTo({
              top: Math.max(0, scrollTop),
              behavior: 'smooth'
            });
          } else {
            // 降级方案：使用原来的 scrollIntoView
            targetHeading.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
          
          // 可选: 高亮显示
          targetHeading.classList.add('highlight-heading');
          setTimeout(() => {
            targetHeading.classList.remove('highlight-heading');
          }, 2000);
        }
      }
    });
  };

  return {
    scrollToHeading,
  };
}