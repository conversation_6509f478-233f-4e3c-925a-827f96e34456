import { Button } from '@/components/ui/button';
import { List, X, ChevronLeft, ChevronRight } from 'lucide-react';

interface TocToggleButtonProps {
  showToc: boolean;
  onToggle: () => void;
  className?: string;
  variant?: 'default' | 'minimal';
}

export function TocToggleButton({ showToc, onToggle, className, variant = 'default' }: TocToggleButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onToggle();
  };

  if (variant === 'minimal') {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClick}
        onMouseDown={(e) => e.preventDefault()}
        className={`
          group relative
          w-7 h-7 p-0 rounded-lg
          transition-all duration-200 hover:scale-110 active:scale-95
          flex items-center justify-center
          ${className}
        `}
        title={showToc ? '收起目录' : '展开目录'}
      >
        {showToc ? (
          <ChevronLeft className="h-3.5 w-3.5" />
        ) : (
          <ChevronRight className="h-3.5 w-3.5" />
        )}
        {/* 悬浮提示文本 */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          {showToc ? '收起目录' : '展开目录'}
        </div>
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleClick}
      onMouseDown={(e) => e.preventDefault()}
      className={`
        rounded-xl px-3 py-2 text-sm font-medium
        transition-all duration-200 hover:scale-105 active:scale-95
        flex items-center gap-2
        ${className}
      `}
      title={showToc ? '收起目录' : '展开目录'}
    >
      {showToc ? (
        <>
          <X className="h-4 w-4" />
          <span className="hidden sm:inline">收起目录</span>
        </>
      ) : (
        <>
          <List className="h-4 w-4" />
          <span className="hidden sm:inline">展开目录</span>
        </>
      )}
    </Button>
  );
}