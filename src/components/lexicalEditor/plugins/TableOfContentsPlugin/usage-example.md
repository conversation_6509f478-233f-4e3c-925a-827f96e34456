# 在 Editor.tsx 中使用目录功能

## ✅ 已完成的集成

当前 `Editor.tsx` 已经完整集成了目录功能，包含：

1. **目录插件**：自动扫描标题（第 172 行）
2. **目录 UI**：右侧面板显示目录（第 190-194 行）
3. **切换按钮**：工具栏中的显示/隐藏按钮（第 127-130 行）

## 🎯 使用方法

### 方法 1：直接使用（无需修改）

```tsx
// 在你的组件中直接使用编辑器
import Editor from '@/components/lexicalEditor/Editor';

function MyNoteEditor() {
  return <Editor />;
}
```

用户可以：
- 点击工具栏右侧的"显示目录"按钮
- 在右侧面板中看到自动生成的目录
- 点击目录项跳转到对应标题位置

### 方法 2：默认显示目录

如果你希望默认显示目录，修改 Editor.tsx：

```tsx
// 第 89 行，将 false 改为 true
const [showToc, setShowToc] = useState<boolean>(true);
```

### 方法 3：使用 EditorWithToc 包装组件

如果你需要更灵活的布局，可以使用包装组件：

```tsx
import { EditorWithToc } from '@/components/lexicalEditor/plugins/TableOfContentsPlugin';
import Editor from '@/components/lexicalEditor/Editor';

function MyEditor() {
  return (
    <EditorWithToc showTocByDefault={true} tocPosition="right">
      <Editor />
    </EditorWithToc>
  );
}
```

## 🎨 界面效果

当目录功能启用时：

```
┌─────────────────────────┬─────────────────┐
│        工具栏           │   [显示目录]    │
├─────────────────────────┼─────────────────┤
│                         │                 │
│     编辑器主体          │     目录面板    │
│                         │                 │
│  # 标题 1               │ H1 标题 1       │
│  ## 标题 2              │ H2 标题 2       │
│  文本内容...            │ H2 标题 3       │
│  ## 标题 3              │                 │
│                         │                 │
└─────────────────────────┴─────────────────┘
```

## 🔧 自定义选项

### 修改目录宽度

```tsx
// 在 Editor.tsx 第 191 行修改
<div className="w-64 border-l bg-background p-2 overflow-auto">
//           ^^^^^ 改为 w-72、w-80 等
```

### 修改目录位置

将目录移到左侧：

```tsx
// 调整 HTML 结构顺序
<div className="flex h-full">
  {/* 目录面板 - 左侧 */}
  {showToc && (
    <div className="w-64 border-r bg-background p-2 overflow-auto">
      <TableOfContentsComponent />
    </div>
  )}
  
  <div className="flex-1 editor-container ...">
    {/* 编辑器内容 */}
  </div>
</div>
```

### 自定义样式

目录组件支持自定义样式：

```tsx
<TableOfContentsComponent className="bg-gray-50 rounded-lg shadow-sm" />
```

## 📝 测试方法

1. 在编辑器中输入以下内容：

```markdown
# 第一章：介绍
这是第一章的内容...

## 1.1 背景
背景介绍...

## 1.2 目标
目标说明...

# 第二章：方法
这是第二章的内容...

### 2.1.1 步骤一
详细步骤...
```

2. 点击"显示目录"按钮
3. 在右侧面板中查看生成的目录
4. 点击目录项测试跳转功能

## 🐛 注意事项

1. **标题格式**：确保使用标准的 Markdown 标题格式（# ## ### 等）
2. **滚动容器**：目录跳转依赖编辑器的滚动容器，确保容器有正确的高度
3. **动态更新**：目录会自动跟随编辑器内容变化而更新
4. **移动端**：在小屏幕设备上，建议隐藏目录或调整布局

目录功能已完全集成并可以直接使用！