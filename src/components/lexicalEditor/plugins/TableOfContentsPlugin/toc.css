/* 目录跳转高亮动画 */
.highlight-heading {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3b82f6;
  padding-left: 0.5rem;
  transition: all 0.3s ease-in-out;
  animation: highlightFade 2s ease-in-out;
}

@keyframes highlightFade {
  0% {
    background-color: rgba(59, 130, 246, 0.2);
    transform: translateX(4px);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.15);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateX(0);
  }
}

/* 目录滚动条样式 */
.toc-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.toc-scroll::-webkit-scrollbar {
  width: 6px;
}

.toc-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.toc-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.toc-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

.toc-scroll::-webkit-scrollbar-corner {
  background: transparent;
}