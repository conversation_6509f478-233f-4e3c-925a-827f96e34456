import React, { useState } from 'react';
import { TableOfContentsComponent } from './TableOfContentsComponent';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { List, X } from 'lucide-react';

interface EditorWithTocProps {
  children: React.ReactNode;
  showTocByDefault?: boolean;
  tocPosition?: 'left' | 'right';
  className?: string;
}

export function EditorWithToc({ 
  children, 
  showTocByDefault = false, 
  tocPosition = 'right',
  className 
}: EditorWithTocProps) {
  const [showToc, setShowToc] = useState(showTocByDefault);

  return (
    <div className={cn("flex h-full", className)}>
      {/* 目录侧边栏 - 左侧 */}
      {tocPosition === 'left' && showToc && (
        <div className="w-64 border-r bg-background p-2 overflow-auto">
          <div className="flex items-center justify-between mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowToc(false)}
              className="h-6 w-6"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <TableOfContentsComponent />
        </div>
      )}

      {/* 编辑器主体 */}
      <div className="flex-1 relative">
        {/* 目录切换按钮 */}
        {!showToc && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowToc(true)}
            className={cn(
              "absolute top-2 z-10 gap-2",
              tocPosition === 'left' ? 'left-2' : 'right-2'
            )}
          >
            <List className="h-4 w-4" />
            目录
          </Button>
        )}
        
        {children}
      </div>

      {/* 目录侧边栏 - 右侧 */}
      {tocPosition === 'right' && showToc && (
        <div className="w-64 border-l bg-background p-2 overflow-auto">
          <div className="flex items-center justify-between mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowToc(false)}
              className="h-6 w-6 ml-auto"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <TableOfContentsComponent />
        </div>
      )}
    </div>
  );
}