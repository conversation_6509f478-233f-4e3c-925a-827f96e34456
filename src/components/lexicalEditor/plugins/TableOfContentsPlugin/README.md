# 目录功能 (Table of Contents)

为 Lexical 编辑器添加了自动生成目录的功能，支持点击跳转到对应标题位置。

## 功能特性

- ✅ 自动扫描 H1-H6 标题生成目录
- ✅ 点击跳转到对应位置，带平滑滚动动画
- ✅ 标题高亮效果，视觉反馈更好
- ✅ 目录可折叠/展开
- ✅ 支持左侧或右侧显示
- ✅ 使用 shadcn/ui 设计风格
- ✅ 支持标题层级缩进和颜色区分

## 使用方法

### 方法 1: 使用 EditorWithToc 组件（推荐）

```tsx
import { EditorWithToc } from '@/components/lexicalEditor/plugins/TableOfContentsPlugin';

function MyEditor() {
  return (
    <EditorWithToc 
      showTocByDefault={true}
      tocPosition="right"
    >
      {/* 你的编辑器内容 */}
      <Editor />
    </EditorWithToc>
  );
}
```

### 方法 2: 手动集成

```tsx
import { TableOfContentsComponent } from '@/components/lexicalEditor/plugins/TableOfContentsPlugin';

function MyEditor() {
  return (
    <div className="flex">
      {/* 编辑器 */}
      <div className="flex-1">
        <Editor />
      </div>
      
      {/* 目录侧边栏 */}
      <div className="w-64 border-l p-2">
        <TableOfContentsComponent />
      </div>
    </div>
  );
}
```

### 方法 3: 自定义 UI

```tsx
import { TableOfContentsPlugin, TableOfContentsUI, useTocNavigation } from '@/components/lexicalEditor/plugins/TableOfContentsPlugin';

function CustomTocEditor() {
  const [toc, setToc] = useState([]);
  const { scrollToHeading } = useTocNavigation();

  return (
    <>
      {/* 插件负责数据提取 */}
      <TableOfContentsPlugin onTocChange={setToc} />
      
      {/* 自定义 UI */}
      <TableOfContentsUI 
        toc={toc}
        onItemClick={scrollToHeading}
        className="my-custom-toc"
      />
    </>
  );
}
```

## 配置选项

### EditorWithToc Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| showTocByDefault | boolean | false | 默认是否显示目录 |
| tocPosition | 'left' \| 'right' | 'right' | 目录位置 |
| className | string | - | 自定义样式类名 |

### TableOfContentsPlugin Props

| 属性 | 类型 | 描述 |
|------|------|------|
| onTocChange | (toc: TocItem[]) => void | 目录变化回调 |

### TocItem 数据结构

```tsx
interface TocItem {
  id: string;        // 唯一标识
  text: string;      // 标题文本
  level: number;     // 标题级别 (1-6)
  element?: Element; // DOM 元素引用
}
```

## 样式定制

目录组件使用 shadcn/ui 样式系统，你可以通过以下方式定制：

1. **使用 Tailwind 类名**：
```tsx
<TableOfContentsComponent className="bg-gray-50 rounded-xl" />
```

2. **覆盖 CSS 变量**：
```css
.my-custom-toc {
  --toc-bg: #f8f9fa;
  --toc-hover: #e9ecef;
}
```

3. **自定义高亮动画**：
```css
.highlight-heading {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  animation: customHighlight 1.5s ease-out;
}
```

## 注意事项

1. 确保编辑器已正确配置 HeadingNode
2. 插件会自动监听编辑器内容变化
3. 目录跳转使用平滑滚动，在移动端可能有兼容性问题
4. 建议在编辑器容器上设置合适的高度和滚动行为

## 集成到现有编辑器

目录插件已自动集成到主编辑器中，无需额外配置。如需显示目录 UI，参考上述使用方法。