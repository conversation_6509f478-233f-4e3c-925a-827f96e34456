import { LexicalEditor } from 'lexical';
import { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { $patchStyleText } from '@lexical/selection';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $isHeadingNode } from '@lexical/rich-text';
import { ChevronDown } from 'lucide-react';
import DropDown, { DropDownItem } from '@/components/lexicalEditor/ui/DropDown';

// 常用字体列表
const FONT_FAMILY_OPTIONS = [
  { value: 'Arial', label: 'Arial' },
  { value: 'Helvetica', label: 'Helvetica' },
  { value: 'Georgia', label: 'Georgia' },
  { value: '"Times New Roman"', label: 'Times New Roman' },
  { value: 'Verdana', label: 'Verdana' },
  { value: '"Courier New"', label: 'Courier New' },
  { value: 'monospace', label: 'Monospace' },
  { value: '"Microsoft YaHei"', label: '微软雅黑' },
  { value: '"PingFang SC"', label: '苹方' },
  { value: '"Source Han Sans SC"', label: '思源黑体' },
  { value: '"Source Han Serif SC"', label: '思源宋体' },
  { value: 'SimSun', label: '宋体' },
  { value: 'SimHei', label: '黑体' },
];

// 字体大小选项
const FONT_SIZE_OPTIONS = [
  { value: '8px', label: '8' },
  { value: '9px', label: '9' },
  { value: '10px', label: '10' },
  { value: '11px', label: '11' },
  { value: '12px', label: '12' },
  { value: '14px', label: '14' },
  { value: '16px', label: '16' },
  { value: '18px', label: '18' },
  { value: '20px', label: '20' },
  { value: '24px', label: '24' },
  { value: '28px', label: '28' },
  { value: '32px', label: '32' },
  { value: '36px', label: '36' },
  { value: '48px', label: '48' },
  { value: '60px', label: '60' },
  { value: '72px', label: '72' },
];

interface FontFamilySelectorProps {
  value: string;
  editor: LexicalEditor;
  disabled?: boolean;
}

export function FontFamilySelector({ value, editor, disabled }: FontFamilySelectorProps) {
  const handleFontFamilyChange = useCallback((fontFamily: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $patchStyleText(selection, {
          'font-family': fontFamily,
        });
      }
    });
  }, [editor]);

  // 获取当前字体的显示名称
  const currentFontLabel = FONT_FAMILY_OPTIONS.find(opt => opt.value === value)?.label || '字体';

  return (
    <DropDown
      disabled={disabled}
      buttonClassName="toolbar-item font-family"
      buttonLabel={currentFontLabel}
      buttonAriaLabel="选择字体"
    >
      {FONT_FAMILY_OPTIONS.map((option) => (
        <DropDownItem
          key={option.value}
          className={`item ${value === option.value ? 'active dropdown-item-active' : ''}`}
          onClick={() => handleFontFamilyChange(option.value)}
        >
          <span className="text" style={{ fontFamily: option.value }}>
            {option.label}
          </span>
        </DropDownItem>
      ))}
    </DropDown>
  );
}

interface FontSizeSelectorProps {
  value: string;
  editor: LexicalEditor;
  disabled?: boolean;
}

export function FontSizeSelector({ value, editor, disabled }: FontSizeSelectorProps) {
  const [isHeadingSelected, setIsHeadingSelected] = useState(false);
  const [headingLevel, setHeadingLevel] = useState<string>('');

  // 监听选择变化来检测是否在标题中
  useEffect(() => {
    const removeListener = editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const nodes = selection.getNodes();
          const headingNode = nodes.find(node => {
            const parent = node.getParent();
            return $isHeadingNode(parent);
          });
          
          if (headingNode) {
            const parent = headingNode.getParent();
            if ($isHeadingNode(parent)) {
              setIsHeadingSelected(true);
              setHeadingLevel(parent.getTag().toUpperCase());
              return;
            }
          }
        }
        setIsHeadingSelected(false);
        setHeadingLevel('');
      });
    });

    return removeListener;
  }, [editor]);

  const handleFontSizeChange = useCallback((fontSize: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // 检查选中的节点是否在标题中
        const nodes = selection.getNodes();
        const isInHeading = nodes.some(node => {
          const parent = node.getParent();
          return $isHeadingNode(parent);
        });
        
        // 如果在标题中，不允许修改字体大小
        if (isInHeading) {
          return;
        }
        
        // 对非标题文本应用字体大小（包括光标位置）
        $patchStyleText(selection, {
          'font-size': fontSize,
        });
      }
    });
  }, [editor]);

  // 获取当前字体大小的显示标签
  const currentSizeLabel = isHeadingSelected 
    ? headingLevel 
    : (value ? FONT_SIZE_OPTIONS.find(opt => opt.value === value)?.label || "16" : "16");

  return (
    <DropDown
      disabled={disabled || isHeadingSelected}
      buttonClassName={`toolbar-item font-size ${isHeadingSelected ? 'opacity-50' : ''}`}
      buttonLabel={currentSizeLabel}
      buttonAriaLabel="选择字体大小"
    >
      {!isHeadingSelected && FONT_SIZE_OPTIONS.map((option) => (
        <DropDownItem
          key={option.value}
          className={`item fontsize-item ${value === option.value ? 'active dropdown-item-active' : ''}`}
          onClick={() => handleFontSizeChange(option.value)}
        >
          <span className="text">{option.label}</span>
        </DropDownItem>
      ))}
    </DropDown>
  );
}

interface FontColorPickerProps {
  value: string;
  editor: LexicalEditor;
  disabled?: boolean;
}

export function FontColorPicker({ value, editor, disabled }: FontColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleColorChange = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $patchStyleText(selection, {
          color: color,
        });
      }
    });
    setIsOpen(false);
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      if (!isOpen && buttonRef.current) {
        // 计算按钮位置
        const rect = buttonRef.current.getBoundingClientRect();
        setPosition({
          top: rect.bottom + window.scrollY + 4,
          left: rect.left + window.scrollX
        });
      }
      setIsOpen(!isOpen);
    }
  };

  const colorOptions = [
    '#000000', '#333333', '#666666', '#999999', '#CCCCCC',
    '#FF0000', '#FF6600', '#FF9900', '#FFCC00', '#FFFF00',
    '#99FF00', '#33FF00', '#00FF33', '#00FF99', '#00FFFF',
    '#0099FF', '#0033FF', '#3300FF', '#9900FF', '#FF00FF',
    '#FF0099', '#FF0033', '#A52A2A', '#008000', '#0000FF',
    '#800080', '#FFA500', '#FFC0CB', '#808080', '#000080',
  ];

  // 点击外部关闭下拉菜单并管理工具栏滚动
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current && 
        !buttonRef.current.contains(event.target as Node) &&
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      // 锁定工具栏滚动
      const toolbar = document.querySelector('.toolbar') as HTMLElement;
      let originalOverflow = '';
      if (toolbar) {
        originalOverflow = toolbar.style.overflow;
        toolbar.style.overflow = 'hidden';
      }

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        // 恢复工具栏滚动
        if (toolbar) {
          toolbar.style.overflow = originalOverflow;
        }
      };
    }
  }, [isOpen]);

  const dropdownContent = isOpen ? (
    <div 
      ref={dropdownRef}
      className="fixed bg-white border border-gray-200 rounded-md shadow-lg p-2"
      style={{ 
        top: position.top,
        left: position.left,
        zIndex: 9999 
      }}
    >
      <div className="grid grid-cols-5 gap-1">
        {colorOptions.map((color) => (
          <button
            key={color}
            className="w-6 h-6 border border-gray-300 rounded cursor-pointer hover:scale-110 transition-transform"
            style={{ backgroundColor: color }}
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleColorChange(color);
            }}
            title={color}
            type="button"
          />
        ))}
      </div>
    </div>
  ) : null;

  return (
    <>
      <button
        ref={buttonRef}
        className="flex items-center gap-2 h-8 px-3 border border-gray-300 rounded text-sm bg-white hover:bg-gray-50 disabled:opacity-50"
        onClick={handleToggle}
        disabled={disabled}
        type="button"
      >
        <div 
          className="w-4 h-4 border border-gray-300 rounded"
          style={{ backgroundColor: value || '#000000' }}
        />
        <span>颜色</span>
        <ChevronDown className="w-3 h-3" />
      </button>
      
      {dropdownContent && createPortal(dropdownContent, document.body)}
    </>
  );
}

interface BackgroundColorPickerProps {
  value: string;
  editor: LexicalEditor;
  disabled?: boolean;
}

export function BackgroundColorPicker({ value, editor, disabled }: BackgroundColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleColorChange = (color: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        $patchStyleText(selection, {
          'background-color': color,
        });
      }
    });
    setIsOpen(false);
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      if (!isOpen && buttonRef.current) {
        // 计算按钮位置
        const rect = buttonRef.current.getBoundingClientRect();
        setPosition({
          top: rect.bottom + window.scrollY + 4,
          left: rect.left + window.scrollX
        });
      }
      setIsOpen(!isOpen);
    }
  };

  const colorOptions = [
    'transparent', '#FFFFFF', '#F0F0F0', '#E0E0E0', '#D0D0D0',
    '#FFE6E6', '#FFEDE6', '#FFF4E6', '#FEFEE6', '#F0FFE6',
    '#E6FFE6', '#E6FFF4', '#E6FFFE', '#E6F4FF', '#E6EDFF',
    '#EDE6FF', '#F4E6FF', '#FFE6FE', '#FFE6F4', '#FFE6ED',
    '#FFCCCC', '#FFDACC', '#FFE9CC', '#FFFCCC', '#E1FFCC',
    '#CCFFCC', '#CCFFE9', '#CCFFFC', '#CCE9FF', '#CCDAFF',
    '#DACCFF', '#E9CCFF', '#FFCCFC', '#FFCCE9', '#FFCCDA',
  ];

  // 点击外部关闭下拉菜单并管理工具栏滚动
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current && 
        !buttonRef.current.contains(event.target as Node) &&
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      // 锁定工具栏滚动
      const toolbar = document.querySelector('.toolbar') as HTMLElement;
      let originalOverflow = '';
      if (toolbar) {
        originalOverflow = toolbar.style.overflow;
        toolbar.style.overflow = 'hidden';
      }

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        // 恢复工具栏滚动
        if (toolbar) {
          toolbar.style.overflow = originalOverflow;
        }
      };
    }
  }, [isOpen]);

  const dropdownContent = isOpen ? (
    <div 
      ref={dropdownRef}
      className="fixed bg-white border border-gray-200 rounded-md shadow-lg p-2"
      style={{ 
        top: position.top,
        left: position.left,
        zIndex: 9999 
      }}
    >
      <div className="grid grid-cols-5 gap-1">
        {colorOptions.map((color) => (
          <button
            key={color}
            className="w-6 h-6 border border-gray-300 rounded cursor-pointer hover:scale-110 transition-transform"
            style={{ 
              backgroundColor: color === 'transparent' ? '#FFFFFF' : color,
              backgroundImage: color === 'transparent' ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)' : undefined,
              backgroundSize: color === 'transparent' ? '8px 8px' : undefined,
            }}
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleColorChange(color);
            }}
            title={color}
            type="button"
          />
        ))}
      </div>
    </div>
  ) : null;

  return (
    <>
      <button
        ref={buttonRef}
        className="flex items-center gap-2 h-8 px-3 border border-gray-300 rounded text-sm bg-white hover:bg-gray-50 disabled:opacity-50"
        onClick={handleToggle}
        disabled={disabled}
        type="button"
      >
        <div 
          className="w-4 h-4 border border-gray-300 rounded"
          style={{ 
            backgroundColor: value === 'transparent' ? '#FFFFFF' : value,
            backgroundImage: value === 'transparent' ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)' : undefined,
            backgroundSize: value === 'transparent' ? '8px 8px' : undefined,
          }}
        />
        <span>背景</span>
        <ChevronDown className="w-3 h-3" />
      </button>
      
      {dropdownContent && createPortal(dropdownContent, document.body)}
    </>
  );
}