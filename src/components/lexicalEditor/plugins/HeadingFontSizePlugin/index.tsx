import { useEffect } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $isHeadingNode } from '@lexical/rich-text';
import { $getSelection, $isRangeSelection } from 'lexical';

// 标题字体大小映射 - 这些是语义化的标题大小
export const HEADING_FONT_SIZES = {
  h1: '32px',  // 大标题
  h2: '24px',  // 中标题  
  h3: '20px',  // 小标题
  h4: '18px',  // 更小标题
  h5: '16px',  // 次级标题
  h6: '14px',  // 最小标题
};

/**
 * 改进的标题字体大小插件
 * 
 * 使用 CSS + JS 双重保护机制，确保标题字体大小不被覆盖
 */
export default function HeadingFontSizePlugin(): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // 1. 首先注入 CSS 样式作为基础保护
    const styleId = 'heading-font-size-plugin-styles';
    let styleElement = document.getElementById(styleId) as HTMLStyleElement;
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }
    
    // CSS 规则，使用最高优先级的选择器
    const cssRules = `
      /* 使用更高优先级的选择器，包括所有可能的组合 */
      .editor-scroller .editor h1,
      .editor-scroller .editor h1 *,
      .editor h1,
      .editor h1 * {
        font-size: ${HEADING_FONT_SIZES.h1} !important;
      }
      .editor-scroller .editor h2,
      .editor-scroller .editor h2 *,
      .editor h2,
      .editor h2 * {
        font-size: ${HEADING_FONT_SIZES.h2} !important;
      }
      .editor-scroller .editor h3,
      .editor-scroller .editor h3 *,
      .editor h3,
      .editor h3 * {
        font-size: ${HEADING_FONT_SIZES.h3} !important;
      }
      .editor-scroller .editor h4,
      .editor-scroller .editor h4 *,
      .editor h4,
      .editor h4 * {
        font-size: ${HEADING_FONT_SIZES.h4} !important;
      }
      .editor-scroller .editor h5,
      .editor-scroller .editor h5 *,
      .editor h5,
      .editor h5 * {
        font-size: ${HEADING_FONT_SIZES.h5} !important;
      }
      .editor-scroller .editor h6,
      .editor-scroller .editor h6 *,
      .editor h6,
      .editor h6 * {
        font-size: ${HEADING_FONT_SIZES.h6} !important;
      }
      
      /* 附加样式 */
      .editor h1, .editor h2, .editor h3, .editor h4, .editor h5, .editor h6 {
        font-weight: bold;
        line-height: 1.3;
        margin: 4px 0;
      }
    `;
    
    styleElement.textContent = cssRules;

    // 2. 然后添加 JS 监听器作为额外保护，移除内联的 font-size 样式
    const removeUpdateListener = editor.registerUpdateListener(({ editorState, prevEditorState }) => {
      // 使用 setTimeout 避免在同一个更新周期内修改
      setTimeout(() => {
        editor.getEditorState().read(() => {
          const rootElement = editor.getRootElement();
          if (!rootElement) return;

          // 查找所有标题元素
          const headings = rootElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
          
          headings.forEach((heading) => {
            // 移除所有子元素的 font-size 内联样式
            const allChildren = heading.querySelectorAll('*');
            allChildren.forEach((child) => {
              if (child instanceof HTMLElement && child.style.fontSize) {
                child.style.removeProperty('font-size');
              }
            });
            
            // 也移除标题本身的 font-size 内联样式
            if (heading instanceof HTMLElement && heading.style.fontSize) {
              heading.style.removeProperty('font-size');
            }
          });
        });
      }, 0);
    });
    
    // 清理函数
    return () => {
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
      removeUpdateListener();
    };
  }, [editor]);

  return null;
}

/**
 * 获取标题对应的字体大小
 */
export function getHeadingFontSize(headingTag: keyof typeof HEADING_FONT_SIZES): string {
  return HEADING_FONT_SIZES[headingTag];
}

/**
 * 检查是否为标题字体大小
 */
export function isHeadingFontSize(fontSize: string): boolean {
  return Object.values(HEADING_FONT_SIZES).includes(fontSize);
}