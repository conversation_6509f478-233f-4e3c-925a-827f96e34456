import { useState } from 'react';
import { LexicalEditor } from 'lexical';
import { INSERT_TABLE_COMMAND } from '@lexical/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface InsertTableDialogProps {
  activeEditor: LexicalEditor;
  onClose: () => void;
  open: boolean;
}

export function InsertTableDialog({ activeEditor, onClose, open }: InsertTableDialogProps) {
  const [rows, setRows] = useState(2);
  const [columns, setColumns] = useState(2);

  const handleInsertTable = () => {
    activeEditor.dispatchCommand(INSERT_TABLE_COMMAND, {
      columns: String(columns),
      rows: String(rows),
      includeHeaders: true,
    });
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>插入表格</DialogTitle>
          <DialogDescription>
            设置表格的行数和列数
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rows" className="text-right">
              行数
            </Label>
            <Input
              id="rows"
              type="number"
              min="1"
              max="20"
              value={rows}
              onChange={(e) => setRows(Number(e.target.value))}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="columns" className="text-right">
              列数
            </Label>
            <Input
              id="columns"
              type="number"
              min="1"
              max="10"
              value={columns}
              onChange={(e) => setColumns(Number(e.target.value))}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handleInsertTable}>
            插入表格
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}