/**
 * Lexical 编辑器跳转工具函数
 * 用于实现从高亮到文本位置的跳转功能
 */

import { LexicalEditor, $getRoot, $isTextNode, $getSelection, $createRangeSelection, $isRangeSelection, $setSelection } from 'lexical';
import { $isElementNode, $getNodeByKey } from 'lexical';
import { $isMarkNode } from '@lexical/mark';
import { EditorEvents, EDITOR_EVENTS } from '@/local/services/editor-events';

export interface TextPosition {
  textContent: string;
  startOffset: number;
  endOffset: number;
  paragraphIndex?: number;
  nodeKey?: string;
}

/**
 * 全局 Lexical 编辑器管理器
 * 用于在不同组件间共享编辑器实例
 */
class LexicalEditorManager {
  private editor: LexicalEditor | null = null;
  
  setEditor(editor: LexicalEditor) {
    this.editor = editor;
  }
  
  getEditor(): LexicalEditor | null {
    return this.editor;
  }
  
  clearEditor() {
    this.editor = null;
  }
}

export const lexicalEditorManager = new LexicalEditorManager();

/**
 * 在 Lexical 编辑器中查找指定文本并跳转
 * @param editor Lexical 编辑器实例
 * @param targetText 要查找的文本
 * @param highlightColor 高亮颜色（可选，用于更精确的匹配）
 * @returns 是否找到并跳转成功
 */
export const jumpToTextInLexical = (
  editor: LexicalEditor | null, 
  targetText: string, 
  highlightColor?: string
): boolean => {
  if (!editor || !targetText.trim()) {
    return false;
  }

  let found = false;

  editor.update(() => {
    const root = $getRoot();
    const allNodes = root.getChildren();
    
    // 递归搜索文本节点
    const findTextInNodes = (nodes: any[]): boolean => {
      for (const node of nodes) {
        if ($isTextNode(node)) {
          const textContent = node.getTextContent();
          const targetIndex = textContent.indexOf(targetText);
          
          if (targetIndex !== -1) {
            // 检查是否有特定颜色的样式匹配
            if (highlightColor) {
              const style = node.getStyle();
              if (!style.includes(`background-color: ${highlightColor}`)) {
                continue;
              }
            }
            
            // 创建选择范围
            const selection = $createRangeSelection();
            selection.anchor.set(node.getKey(), targetIndex, 'text');
            selection.focus.set(node.getKey(), targetIndex + targetText.length, 'text');
            
            // 设置选择
            root.selectEnd();
            selection.insertNodes([node]);
            
            found = true;
            return true;
          }
        } else if ($isElementNode(node)) {
          if (findTextInNodes(node.getChildren())) {
            return true;
          }
        }
      }
      return false;
    };
    
    found = findTextInNodes(allNodes);
  });

  // 如果找到了文本，滚动到对应位置
  if (found) {
    setTimeout(() => {
      scrollToSelection(editor);
    }, 100);
  }

  return found;
};

/**
 * 通过文本内容和位置信息进行更精确的跳转
 * @param editor Lexical 编辑器实例
 * @param position 文本位置信息
 * @returns 是否跳转成功
 */
export const jumpToTextPositionInLexical = (
  editor: LexicalEditor | null, 
  position: TextPosition
): boolean => {
  if (!editor || !position.textContent.trim()) {
    return false;
  }

  let found = false;

  editor.update(() => {
    const root = $getRoot();
    
    // 如果有节点键，直接查找
    if (position.nodeKey) {
      try {
        const targetNode = $getNodeByKey(position.nodeKey);
        if (targetNode && $isTextNode(targetNode)) {
          const selection = $createRangeSelection();
          selection.anchor.set(
            targetNode.getKey(), 
            position.startOffset, 
            'text'
          );
          selection.focus.set(
            targetNode.getKey(), 
            position.endOffset, 
            'text'
          );
          
          root.selectEnd();
          selection.insertNodes([targetNode]);
          found = true;
          return;
        }
      } catch (e) {
        console.warn('无法通过节点键查找:', e);
      }
    }
    
    // 否则通过文本内容查找
    found = jumpToTextInLexical(editor, position.textContent);
  });

  return found;
};

/**
 * 滚动编辑器到当前选择位置
 * @param editor Lexical 编辑器实例
 */
export const scrollToSelection = (editor: LexicalEditor): void => {
  const rootElement = editor.getRootElement();
  if (!rootElement) return;

  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) return;

  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();
  
  if (rect.height === 0) return;

  // 找到编辑器容器
  const editorContainer = rootElement.closest('.editor-container') || rootElement.closest('.editor-scroller');
  
  if (editorContainer) {
    const containerRect = editorContainer.getBoundingClientRect();
    const relativeTop = rect.top - containerRect.top;
    const scrollTarget = editorContainer.scrollTop + relativeTop - containerRect.height / 2;
    
    editorContainer.scrollTo({
      top: Math.max(0, scrollTarget),
      behavior: 'smooth'
    });
  } else {
    // 回退方案：使用全局滚动
    const targetElement = range.startContainer.nodeType === Node.TEXT_NODE 
      ? range.startContainer.parentElement 
      : range.startContainer as Element;
      
    if (targetElement && targetElement.scrollIntoView) {
      targetElement.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
    }
  }

  // 高亮选中文本（短暂闪烁效果）
  // highlightSelectedText(range);
};

/**
 * 高亮选中的文本（视觉反馈）
 * @param range 文本范围
 */
export const highlightSelectedText = (range: Range): void => {
  const span = document.createElement('span');
  span.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
  span.style.transition = 'background-color 1s ease-out';
  
  try {
    range.surroundContents(span);
    
    // 1秒后移除高亮
    setTimeout(() => {
      if (span.parentNode) {
        const parent = span.parentNode;
        while (span.firstChild) {
          parent.insertBefore(span.firstChild, span);
        }
        parent.removeChild(span);
      }
    }, 1000);
  } catch (e) {
    // 如果无法包围内容（比如跨越多个元素），使用选择高亮
    window.getSelection()?.removeAllRanges();
    window.getSelection()?.addRange(range);
    
    setTimeout(() => {
      window.getSelection()?.removeAllRanges();
    }, 1000);
  }
};

/**
 * 存储文本位置信息到高亮数据中
 * 在创建高亮时调用，用于后续跳转
 * @param editor Lexical 编辑器实例
 * @param selectedText 选中的文本
 * @returns 位置信息对象
 */
export const captureTextPosition = (
  editor: LexicalEditor, 
  selectedText: string
): TextPosition | null => {
  let position: TextPosition | null = null;

  editor.getEditorState().read(() => {
    const selection = $getSelection();
    if (!selection || !$isRangeSelection(selection) || selection.getTextContent() !== selectedText) {
      return;
    }

    const anchor = selection.anchor;
    const focus = selection.focus;
    
    if (anchor.key === focus.key) {
      // 同一节点内的选择
      position = {
        textContent: selectedText,
        startOffset: Math.min(anchor.offset, focus.offset),
        endOffset: Math.max(anchor.offset, focus.offset),
        nodeKey: anchor.key,
      };
    } else {
      // 跨节点选择，使用文本内容匹配
      position = {
        textContent: selectedText,
        startOffset: 0,
        endOffset: selectedText.length,
      };
    }
  });

  return position;
};

/**
 * 跳转到指定的 Mark 高亮
 * @param editor Lexical 编辑器实例
 * @param nodeId 画布节点ID
 * @returns 是否跳转成功
 */
export const jumpToMarkHighlight = (editor: LexicalEditor, nodeId: string): boolean => {
  let found = false;

  editor.update(() => {
    const root = $getRoot();
    const allNodes = root.getAllTextNodes();
    
    // 遍历所有文本节点，查找带有指定 Mark 的节点
    for (const textNode of allNodes) {
      const parent = textNode.getParent();
      
      if ($isMarkNode(parent)) {
        const markIds = parent.getIDs();
        const canvasMarkId = `canvas-${nodeId}`;
        
        if (markIds.includes(canvasMarkId)) {
          // 找到了对应的 Mark 节点
          try {
            // 创建选择范围，但不要选中整个文本（避免破坏 Mark）
            // 只是将光标定位到 Mark 位置
            const selection = $createRangeSelection();
            
            // 将光标设置到 Mark 文本的开始位置
            selection.anchor.set(textNode.getKey(), 0, 'text');
            selection.focus.set(textNode.getKey(), 0, 'text'); // 光标而不是选区
            
            // 设置选择
            $setSelection(selection);
            
            found = true;
            break;
          } catch (error) {
            console.warn('设置 Mark 选择失败:', error);
          }
        }
      }
    }
  });

  // 如果找到了，滚动到对应位置并短暂高亮
  if (found) {
    setTimeout(() => {
      scrollToSelection(editor);
      // 添加短暂的视觉反馈，但不破坏 Mark
      highlightMarkTemporarily(editor, nodeId);
    }, 100);
  }

  return found;
};

/**
 * 短暂高亮 Mark 节点（视觉反馈）
 * @param editor Lexical 编辑器实例
 * @param nodeId 节点ID
 */
function highlightMarkTemporarily(editor: LexicalEditor, nodeId: string) {
  // 通过 DOM 操作添加临时高亮效果
  const rootElement = editor.getRootElement();
  if (!rootElement) return;

  // 更精确的方法：通过 Lexical 编辑器状态找到对应的 DOM 元素
  editor.getEditorState().read(() => {
    const root = $getRoot();
    const allNodes = root.getAllTextNodes();
    
    for (const textNode of allNodes) {
      const parent = textNode.getParent();
      
      if ($isMarkNode(parent)) {
        const markIds = parent.getIDs();
        const canvasMarkId = `canvas-${nodeId}`;
        
        if (markIds.includes(canvasMarkId)) {
          // 找到了对应的 Mark 节点，获取其 DOM 元素
          const domElement = editor.getElementByKey(textNode.getKey());
          if (domElement) {
            // 找到 mark 元素（可能是父级或自身）
            const markElement = domElement.closest('mark') || domElement.querySelector('mark');
            
            if (markElement) {
              // 添加临时高亮效果
              const originalBackground = markElement.style.backgroundColor;
              const originalOutline = markElement.style.outline;
              
              markElement.style.backgroundColor = 'rgba(255, 255, 0, 0.6)'; // 黄色闪烁
              markElement.style.outline = '2px solid #ffeb3b';
              markElement.style.transition = 'all 0.3s ease';
              
              // 1.5秒后恢复原始样式
              setTimeout(() => {
                markElement.style.backgroundColor = originalBackground;
                markElement.style.outline = originalOutline;
              }, 1500);
            }
          }
          break;
        }
      }
    }
  });
}

/**
 * 全局编辑器跳转管理器
 * 支持通过文件ID找到对应的编辑器实例并执行跳转
 */
class LexicalJumpManager {
  private static editorInstances = new Map<string, LexicalEditor>();
  
  static registerEditor(fileId: string, editor: LexicalEditor) {
    this.editorInstances.set(fileId, editor);
  }
  
  static unregisterEditor(fileId: string) {
    this.editorInstances.delete(fileId);
  }
  
  static jumpToTextInFile(fileId: string, targetText: string, highlightColor?: string): boolean {
    const editor = this.editorInstances.get(fileId);
    if (!editor) {
      // 如果编辑器未注册，通过事件系统尝试激活
      EditorEvents.emit(EDITOR_EVENTS.RELOAD_REQUEST, fileId);
      return false;
    }
    
    return jumpToTextInLexical(editor, targetText, highlightColor);
  }
  
  static jumpToPositionInFile(fileId: string, position: TextPosition): boolean {
    const editor = this.editorInstances.get(fileId);
    if (!editor) {
      EditorEvents.emit(EDITOR_EVENTS.RELOAD_REQUEST, fileId);
      return false;
    }
    
    return jumpToTextPositionInLexical(editor, position);
  }
  
  /**
   * 跳转到指定的 Mark 高亮
   * @param fileId 文件ID
   * @param nodeId 画布节点ID
   */
  static jumpToMarkByNodeId(fileId: string, nodeId: string): boolean {
    const editor = this.editorInstances.get(fileId);
    if (!editor) {
      EditorEvents.emit(EDITOR_EVENTS.RELOAD_REQUEST, fileId);
      return false;
    }
    
    return jumpToMarkHighlight(editor, nodeId);
  }
}

export { LexicalJumpManager };