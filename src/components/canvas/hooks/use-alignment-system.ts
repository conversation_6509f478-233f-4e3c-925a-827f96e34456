import { useCallback, useState, useRef } from 'react';
import { Node, useReactFlow } from '@xyflow/react';
import { SpatialIndex, NodeBounds } from '../lib/spatial-index';
import { NODE_TYPE_MAPS } from '../lib/node-types';

/**
 * 对齐线类型
 */
export interface AlignmentGuide {
  id: string;
  position: number;
  orientation: 'horizontal' | 'vertical';
  type: 'left' | 'right' | 'center' | 'top' | 'bottom' | 'middle' | 'adjacent';
  strength: number; // 对齐强度，用于优先级排序
}

/**
 * 吸附结果
 */
export interface SnapResult {
  position: { x: number; y: number };
  guides: AlignmentGuide[];
}

/**
 * 对齐系统配置
 */
export interface AlignmentConfig {
  enabled: boolean;
  snapDistance: number;
  showGuides: boolean;
  nodeTypes: string[];
  maxDetectionDistance: number;
}

const DEFAULT_CONFIG: AlignmentConfig = {
  enabled: true,
  snapDistance: 20,
  showGuides: true,
  nodeTypes: [NODE_TYPE_MAPS.markdown],
  maxDetectionDistance: 500
};

/**
 * 高性能对齐系统 Hook
 * 使用空间索引优化性能，正确处理 parentId 节点的坐标系统
 */
export const useAlignmentSystem = (config: Partial<AlignmentConfig> = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const { getNodes, getInternalNode } = useReactFlow();
  
  const [guides, setGuides] = useState<AlignmentGuide[]>([]);
  const spatialIndexRef = useRef<SpatialIndex>(new SpatialIndex());
  const nodeBoundsCache = useRef<Map<string, NodeBounds>>(new Map());
  const lastUpdateTime = useRef<number>(0);

  /**
   * 获取节点的绝对边界信息
   * 使用 getInternalNode 确保获取正确的绝对坐标
   */
  const getNodeBounds = useCallback((node: Node): NodeBounds | null => {
    const internalNode = getInternalNode(node.id);
    if (!internalNode) return null;

    // 使用绝对坐标，正确处理 parentId 节点
    const position = internalNode.internals.positionAbsolute;
    const size = node.measured || { width: 0, height: 0 };
    const width = size.width ?? 0;
    const height = size.height ?? 0;

    return {
      nodeId: node.id,
      left: position.x,
      right: position.x + width,
      top: position.y,
      bottom: position.y + height,
      centerX: position.x + width / 2,
      centerY: position.y + height / 2,
      width: width,
      height: height
    };
  }, [getInternalNode]);

  /**
   * 更新空间索引
   * 使用增量更新策略提高性能
   */
  const updateSpatialIndex = useCallback(() => {
    const now = Date.now();
    // 限制更新频率，避免过度计算
    if (now - lastUpdateTime.current < 16) { // ~60fps
      return;
    }
    lastUpdateTime.current = now;

    const nodes = getNodes();
    const validNodes = nodes.filter(node => 
      finalConfig.nodeTypes.includes(node.type || '')
    );

    // 清除旧的缓存和索引
    nodeBoundsCache.current.clear();
    spatialIndexRef.current.clear();

    // 重建索引
    validNodes.forEach(node => {
      const bounds = getNodeBounds(node);
      if (bounds) {
        nodeBoundsCache.current.set(node.id, bounds);
        spatialIndexRef.current.addNode(bounds);
      }
    });
  }, [getNodes, getNodeBounds, finalConfig.nodeTypes]);

  /**
   * 计算对齐指导线 - 简化版本，专注于简单的x/y坐标对齐
   */
  const calculateAlignmentGuides = useCallback((
    draggedNode: Node,
    candidateNodes: NodeBounds[]
  ): AlignmentGuide[] => {
    const draggedBounds = getNodeBounds(draggedNode);
    if (!draggedBounds) return [];

    const guides: AlignmentGuide[] = [];
    const seenPositions = new Set<string>();

    candidateNodes.forEach(targetBounds => {
      // 垂直对齐线 - 检查x轴坐标对齐（节点左边缘对齐）
      const xDistance = Math.abs(draggedBounds.left - targetBounds.left);
      if (xDistance <= finalConfig.snapDistance) {
        const key = `vertical-${targetBounds.left}`;
        if (!seenPositions.has(key)) {
          seenPositions.add(key);
          guides.push({
            id: `vertical-x-${targetBounds.nodeId}`,
            position: targetBounds.left,
            orientation: 'vertical',
            type: 'left',
            strength: 1.0
          });
          console.log('垂直对齐线:', targetBounds.left, 'xDistance:', xDistance);
        }
      }

      // 水平对齐线 - 检查y轴坐标对齐（节点上边缘对齐）
      const yDistance = Math.abs(draggedBounds.top - targetBounds.top);
      if (yDistance <= finalConfig.snapDistance) {
        const key = `horizontal-${targetBounds.top}`;
        if (!seenPositions.has(key)) {
          seenPositions.add(key);
          guides.push({
            id: `horizontal-y-${targetBounds.nodeId}`,
            position: targetBounds.top,
            orientation: 'horizontal',
            type: 'top',
            strength: 1.0
          });
          console.log('水平对齐线:', targetBounds.top, 'yDistance:', yDistance);
        }
      }
    });

    return guides;
  }, [getNodeBounds, finalConfig.snapDistance]);

  /**
   * 计算吸附位置
   */
  const calculateSnapPosition = useCallback((
    draggedNode: Node,
    currentPosition: { x: number; y: number }
  ): SnapResult => {
    if (!finalConfig.enabled) {
      return { position: currentPosition, guides: [] };
    }

    // 更新空间索引
    updateSpatialIndex();

    const draggedBounds = getNodeBounds(draggedNode);
    if (!draggedBounds) {
      return { position: currentPosition, guides: [] };
    }

    // 使用空间索引查找附近的节点
    const candidateNodes = spatialIndexRef.current.findNodesInRange(
      draggedBounds.centerX,
      draggedBounds.centerY,
      finalConfig.maxDetectionDistance,
      draggedNode.id
    );

    // 计算对齐指导线
    const alignmentGuides = calculateAlignmentGuides(draggedNode, candidateNodes);

    // 计算吸附后的位置 - 简化版本
    let snappedX = currentPosition.x;
    let snappedY = currentPosition.y;

    // 应用垂直吸附 - 直接吸附到x坐标
    const verticalGuide = alignmentGuides.find(g => g.orientation === 'vertical');
    if (verticalGuide) {
      snappedX = verticalGuide.position;
    }

    // 应用水平吸附 - 直接吸附到y坐标
    const horizontalGuide = alignmentGuides.find(g => g.orientation === 'horizontal');
    if (horizontalGuide) {
      snappedY = horizontalGuide.position;
    }

    return {
      position: { x: snappedX, y: snappedY },
      guides: alignmentGuides
    };
  }, [
    finalConfig.enabled,
    finalConfig.maxDetectionDistance,
    updateSpatialIndex,
    getNodeBounds,
    calculateAlignmentGuides
  ]);

  /**
   * 清除对齐指导线
   */
  const clearGuides = useCallback(() => {
    setGuides([]);
  }, []);

  /**
   * 获取性能统计信息
   */
  const getPerformanceStats = useCallback(() => {
    return {
      ...spatialIndexRef.current.getStats(),
      cachedNodes: nodeBoundsCache.current.size
    };
  }, []);

  return {
    guides,
    calculateSnapPosition,
    clearGuides,
    setGuides,
    getPerformanceStats,
    isEnabled: finalConfig.enabled
  };
};