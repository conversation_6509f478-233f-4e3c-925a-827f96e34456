import { useCallback, useState } from "react";
import { Edge, Node, useReactFlow } from "@xyflow/react";

export interface ContextMenuType {
  type: "node" | "edge" | "pane" | "selection";
  id?: string;
  x?: number;
  y?: number;
  flowX?: number; // 相对于 Flow 容器的 x 坐标
  flowY?: number; // 相对于 Flow 容器的 y 坐标
  canvasX?: number; // Flow 画布坐标系的 x 坐标（考虑缩放和平移）
  canvasY?: number; // Flow 画布坐标系的 y 坐标（考虑缩放和平移）
  position: {
    top?: number;
    left?: number;
    bottom?: number;
    right?: number;
  };
}

export const useCanvasContext = () => {
  const [contextMenuState, setContextMenuState] =
    useState<ContextMenuType | null>(null);
    const {setNodes, screenToFlowPosition} = useReactFlow()

  // 自适应定位，当底部距离不够时，在上边显示
  const calculateMenuPosition = (
    event: React.MouseEvent,
    containerElement?: HTMLElement
  ) => {
    const MENU_HEIGHT = 580; // 菜单高度
    const MENU_WIDTH = 200; // 菜单宽度
    const calcMenuPosition = (x: number, y: number) => {
      const winH = window.innerHeight;
      const winW = window.innerWidth;
      let top = y;
      let left = x;
      if (y + MENU_HEIGHT > winH) {
        top = Math.max(0, winH - MENU_HEIGHT - 10);
      }
      if (x + MENU_WIDTH > winW) {
        left = Math.max(0, winW - MENU_WIDTH - 10);
      }
      return { top, left };
    };
    const { top, left } = calcMenuPosition(event.clientX, event.clientY);

    return {
      top: top,
      left: left,
    };
  };

  // 通用的坐标计算函数
  const calculateAllCoordinates = useCallback((event: React.MouseEvent) => {
    // 获取相对于浏览器视口的坐标
    const clientX = event.clientX;
    const clientY = event.clientY;

    // 获取 ReactFlow 容器的边界信息
    const flowElement = event.currentTarget.closest(".react-flow") as HTMLElement;
    const flowRect = flowElement.getBoundingClientRect();
    
    // 计算相对于 Flow 容器的坐标
    const flowX = clientX - flowRect.left;
    const flowY = clientY - flowRect.top;

    // 将屏幕坐标转换为 Flow 画布坐标（考虑缩放和平移）
    const canvasPosition = screenToFlowPosition({
      x: clientX,
      y: clientY,
    });

    return {
      clientX,
      clientY,
      flowX,
      flowY,
      canvasX: canvasPosition.x,
      canvasY: canvasPosition.y,
    };
  }, [screenToFlowPosition]);

  const onNodeContextMenu = useCallback(
    (event: React.MouseEvent, node: Node) => {
      event.preventDefault();
      
      const position = calculateMenuPosition(
        event,
        event.currentTarget.closest(".react-flow") as HTMLElement
      );

      // 计算所有坐标
      const coordinates = calculateAllCoordinates(event);

      // 节点标记为选中
      setNodes(prev => prev.map(n => ({...n, selected: n.id === node.id})))
      
      setContextMenuState({
        id: node.id,
        type: "node",
        position,
        ...coordinates,
      });

      console.log('右键节点坐标信息:', coordinates);
    },
    [calculateAllCoordinates, setNodes]
  );

  const onEdgeContextMenu = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      event.preventDefault();

      const position = calculateMenuPosition(
        event,
        event.currentTarget.closest(".react-flow") as HTMLElement
      );

      // 计算所有坐标
      const coordinates = calculateAllCoordinates(event);

      setContextMenuState({
        id: edge.id,
        type: "edge",
        position,
        ...coordinates,
      });

      console.log('右键边坐标信息:', coordinates);
    },
    [calculateAllCoordinates]
  );

  const onPaneContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();

    const position = calculateMenuPosition(
      event,
      event.currentTarget as HTMLElement
    );

    // 计算所有坐标
    const coordinates = calculateAllCoordinates(event);

    setContextMenuState({
      id: "",
      type: "pane",
      position,
      ...coordinates,
    });

    console.log('右键面板坐标信息:', coordinates);
  }, [calculateAllCoordinates]);

  const onSelectionContextMenu = useCallback(
    (event: React.MouseEvent, selection: Node<Record<string, unknown>>[]) => {
      event.preventDefault();
      event?.stopPropagation();

      const position = calculateMenuPosition(
        event,
        event.currentTarget.closest(".react-flow") as HTMLElement
      );

      // 计算所有坐标
      const coordinates = calculateAllCoordinates(event);

      setContextMenuState({
        id: "",
        type: "selection",
        position,
        ...coordinates,
      });

      console.log('右键选择坐标信息:', coordinates);
    },
    [calculateAllCoordinates]
  );

  const onCloseMenu = () => {
    setContextMenuState(null);
  };

  return {
    contextMenuState,
    setContextMenuState,
    onNodeContextMenu,
    onEdgeContextMenu,
    onPaneContextMenu,
    onSelectionContextMenu,
    onCloseMenu,
  };
};