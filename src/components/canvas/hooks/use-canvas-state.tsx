import { useCallback, useState, useEffect } from "react";
import { 
  type Node, 
  type Edge, 
  type ReactFlowProps,
  OnNodesChange,
  OnEdgesChange,
  applyNodeChanges,
  applyEdgeChanges
} from "@xyflow/react";
import { useSyncFlow } from "@/components/flow/hooks/fetch-data";
import { usePdfStore, getNodeHighlight } from "@/store/pdf-store";

interface UseCanvasStateProps {
  initialNodes?: Node[];
  initialEdges?: Edge[];
  onNodesChange?: OnNodesChange;
  onEdgesChange?: OnEdgesChange;
}

export const useCanvasState = (props: UseCanvasStateProps = {}) => {
  const {
    initialNodes = [],
    initialEdges = [],
    onNodesChange,
    onEdgesChange,
  } = props;

  // 基础状态管理
  const [nodes, setNodes] = useState<Node[]>(initialNodes);
  const [edges, setEdges] = useState<Edge[]>(initialEdges);
  const [copiedNodes, setCopiedNodes] = useState<Node[]>([]);
  
  const { save } = useSyncFlow();

  // 同步外部传入的节点和边数据
  // 使用 JSON.stringify 来比较内容而不是引用
  // useEffect(() => {
  //   if (initialNodes) {
  //     setNodes(initialNodes);
  //   }
  // }, [JSON.stringify(initialNodes)]);

  // useEffect(() => {
  //   if (initialEdges) {
  //     setEdges(initialEdges);
  //   }
  // }, [JSON.stringify(initialEdges)]);

  // 节点变化处理
  const handleNodesChange = useCallback<OnNodesChange>(
    async (changes) => {
      // 检查是否有节点被删除
      const removedNodeIds = changes
        .filter(change => change.type === 'remove')
        .map(change => change.id);

      if (removedNodeIds.length > 0) {
        console.log('检测到节点删除（键盘删除）:', removedNodeIds);
        
        try {
          // 删除对应的高亮 - 从内存中删除
          const highlights = getNodeHighlight(removedNodeIds);
          if (highlights.length > 0) {
            const { batchRemoveHighlights } = usePdfStore.getState();
            batchRemoveHighlights(highlights);
            
            // 隐藏相关的高亮工具栏
            const pdfState = usePdfStore.getState();
            const activeAid = pdfState.activeAid;
            if (activeAid) {
              const pdfHighlighterUtils = pdfState.pdfs.get(activeAid)?.pdfHighlighterUtils;
              if (pdfHighlighterUtils) {
                // 检查当前显示的tip是否对应被删除的节点
                const currentTip = pdfHighlighterUtils.getTip();
                if (currentTip) {
                  // 由于tip中的高亮信息可能匹配被删除的节点，隐藏所有tip
                  pdfHighlighterUtils.setTip(null);
                }
              }
            }
            
            // 同时从本地数据库中删除高亮
            const { getDatabase } = await import('@/local');
            const db = await getDatabase();
            if (db && db.marks) {
              await db.marks.find({
                selector: {
                  node_id: { $in: removedNodeIds }
                }
              }).remove();
              console.log('已从数据库中删除节点对应的高亮（键盘删除）:', removedNodeIds);
            }
          }
        } catch (error) {
          console.error('删除节点高亮失败（键盘删除）:', error);
        }
      }

      setNodes((current) => {
        const updated = applyNodeChanges(changes, current);
        save();
        onNodesChange?.(changes);
        return updated;
      });
    },
    [save, onNodesChange]
  );

  // 边变化处理
  const handleEdgesChange = useCallback<OnEdgesChange>(
    (changes) => {
      setEdges((current) => {
        const updated = applyEdgeChanges(changes, current);
        save();
        onEdgesChange?.(changes);
        return updated;
      });
    },
    [save, onEdgesChange]
  );

  // 提供给 ReactFlow 的 props
  const flowProps: Partial<ReactFlowProps> = {
    nodes,
    edges,
    onNodesChange: handleNodesChange,
    onEdgesChange: handleEdgesChange,
  };

  return {
    // 状态
    nodes,
    edges,
    copiedNodes,
    
    // 状态更新方法
    setNodes,
    setEdges,
    setCopiedNodes,
    
    // ReactFlow props
    flowProps,
    
    // 工具方法
    save,
  };
};