import { useEffect, useCallback } from "react";

interface UseCanvasKeyboardProps {
  handleSelectAll: () => void;
  handleCopy: () => void;
  handlePaste: () => void;
  handleDuplicateAll: () => void;
}

export const useCanvasKeyboard = (props: UseCanvasKeyboardProps) => {
  const { handleSelectAll, handleCopy, handlePaste, handleDuplicateAll } = props;

  // 键盘事件处理
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // 检查是否在输入框中，如果是则不处理快捷键
      const target = event.target as HTMLElement;
      if (
        target.tagName === "INPUT" ||
        target.tagName === "TEXTAREA" ||
        target.contentEditable === "true"
      ) {
        return;
      }

      const isCtrlOrCmd = event.ctrlKey || event.metaKey;

      if (isCtrlOrCmd) {
        switch (event.key.toLowerCase()) {
          case "a":
            event.preventDefault();
            handleSelectAll();
            break;
          case "c":
            event.preventDefault();
            handleCopy();
            break;
          case "v":
            event.preventDefault();
            handlePaste();
            break;
          case "d":
            event.preventDefault();
            handleDuplicateAll();
            break;
          default:
            break;
        }
      }
    },
    [handleSelectAll, handleCopy, handlePaste, handleDuplicateAll]
  );

  // 注册键盘事件监听器
  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    // 暴露键盘事件处理函数，供外部使用
    handleKeyDown,
  };
};