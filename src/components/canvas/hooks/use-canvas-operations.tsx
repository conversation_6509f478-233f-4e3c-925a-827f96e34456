import { useCallback } from "react";
import {
  useReactFlow,
  type Node,
  type Edge,
  OnConnect,
  OnConnectEnd,
  XYPosition,
} from "@xyflow/react";
import { nanoid } from "nanoid";
import { toast } from "sonner";
import { usePanelOpenStore } from "@/store/panel-open-store";
import { createNode } from "../lib/node-factory";
import { useNodeChat } from "@/components/flow/hooks/node-chat";
import { useNodeNote } from "@/components/flow/hooks/node-note";
import { deleteLink } from "@/api/canvas";
import { getWsId } from "@/tools/params";
import { NODE_TYPE_MAPS, NodeType } from "../components/node";
import { usePdfStore, getNodeHighlight } from "@/store/pdf-store";

// 公用的自动字段添加函数
export const addAutoInputFields = (
  sourceNodeId: string,
  targetNodeId: string,
  nodes: Node[],
  updateNodeData: (nodeId: string, data: any) => void
) => {
  const targetNode = nodes.find(n => n.id === targetNodeId);
  const sourceNode = nodes.find(n => n.id === sourceNodeId);
  
  if (targetNode && sourceNode && (targetNode.type === NODE_TYPE_MAPS.llm || targetNode.type === NODE_TYPE_MAPS.llmEndNode)) {
    
    // 处理 End Node - 使用 inputVariables 字段
    if (targetNode.type === NODE_TYPE_MAPS.llmEndNode) {
      const currentInputVariables: any[] = Array.isArray(targetNode.data?.inputVariables) ? targetNode.data.inputVariables : [];
      
      if (sourceNode.type === NODE_TYPE_MAPS.llm) {
        // 连接 LLM 节点到 End 节点：添加引用类型变量
        const outputFields = sourceNode.data?.outputFields || [];
        if (Array.isArray(outputFields)) {
          const newVariables = [...currentInputVariables];
          let hasNewVariables = false;
          
          outputFields.forEach((outputField: any) => {
            const referenceValue = `${sourceNodeId}.${outputField.name}`;
            const exists = currentInputVariables.some(variable => variable.paramValue === referenceValue);
            
            if (!exists) {
              newVariables.push({
                id: `auto_${sourceNodeId}_${outputField.name}`,
                paramName: `${sourceNode.data?.title || '上游节点'}_${outputField.aliasName || outputField.name}`,
                paramValueType: "reference" as const,
                paramValue: referenceValue,
              });
              hasNewVariables = true;
            }
          });
          
          if (hasNewVariables) {
            updateNodeData(targetNodeId, {
              ...targetNode.data,
              inputVariables: newVariables,
            });
          }
        }
      }
    }
    // 处理 LLM Node - 使用 inputFields 字段
    else if (targetNode.type === NODE_TYPE_MAPS.llm) {
      const currentInputFields: any[] = Array.isArray(targetNode.data?.inputFields) ? targetNode.data.inputFields : [];
      
      if (sourceNode.type === NODE_TYPE_MAPS.llm || sourceNode.type === NODE_TYPE_MAPS.llmEndNode) {
        // 连接 LLM 节点：添加引用类型字段
        const outputFields = sourceNode.data?.outputFields || [];
        if (Array.isArray(outputFields)) {
          const newFields = [...currentInputFields];
          let hasNewFields = false;
          
          outputFields.forEach((outputField: any) => {
            const referenceValue = `${sourceNodeId}.${outputField.name}`;
            const exists = currentInputFields.some(field => field.paramValue === referenceValue);
            
            if (!exists) {
              newFields.push({
                id: `auto_ref_${sourceNodeId}_${outputField.name}`,
                paramName: `${sourceNode.data?.title || '上游节点'}_${outputField.aliasName || outputField.name}`,
                paramValueType: "reference" as const,
                paramValue: referenceValue,
              });
              hasNewFields = true;
            }
          });
          
          if (hasNewFields) {
            updateNodeData(targetNodeId, {
              ...targetNode.data,
              inputFields: newFields,
            });
          }
        }
      } else if (sourceNode.type === NODE_TYPE_MAPS.markdown) {
        // 连接 Markdown 节点：添加数据集类型字段
        const datasetValue = sourceNodeId;
        const exists = currentInputFields.some(field => 
          field.paramValueType === "dataset" && field.paramValue === datasetValue
        );
        
        if (!exists) {
          const nodeTitle = sourceNode.data?.title || '上游节点';
          const nodeContent = sourceNode.data?.content || '';
          
          const newFields = [...currentInputFields, {
            id: `auto_dataset_${sourceNodeId}`,
            paramName: `${nodeTitle}_内容`,
            paramValueType: "dataset" as const,
            paramValue: datasetValue,
            sourceType: "nodes",
            nodeContent: nodeContent,
            nodeTitle: String(nodeTitle),
          }];
          
          updateNodeData(targetNodeId, {
            ...targetNode.data,
            inputFields: newFields,
          });
        }
      }
    }
  }
};

interface UseCanvasOperationsProps {
  nodes: Node[];
  edges: Edge[];
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  copiedNodes: Node[];
  setCopiedNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  save: () => void;
}

export const useCanvasOperations = (props: UseCanvasOperationsProps) => {
  const { setNodes, setEdges, copiedNodes, setCopiedNodes, save } = props;

  const {
    getNodes,
    getEdges,
    screenToFlowPosition,
    getNode,
    updateNode,
    updateNodeData,
    getInternalNode,
    deleteElements,
  } = useReactFlow();

  const { handleChat } = useNodeChat();
  const { notePanelOpen, toggleNotePanel } = usePanelOpenStore();
  const { handleCopyToNotebook } = useNodeNote();

  // 添加节点
  const addNode = useCallback(
    (type: NodeType, options?: Record<string, unknown>) => {
      const {
        data: nodeData,
        position = { x: 0, y: 0 },
        autoConnect = false,
        ...rest
      } = options ?? {};

      const defaultOptions = createNode(type, position as XYPosition);
      const newNode: Node = {
        ...defaultOptions,
        data: {
          ...(nodeData ?? defaultOptions.data ?? {}),
        },
        ...rest,
      };

      // 获取当前选中的节点
      const selectedNodes = getNodes().filter((node) => node.selected);

      // 创建节点时，关联选择的节点
      if (autoConnect && selectedNodes.length > 0) {
        const selectedNodeIds = selectedNodes.map((node) => node.id);

        // 添加新节点
        setNodes((nds: Node[]) => nds.concat(newNode));

        // 创建从每个选中节点到新节点的边
        const newEdges: Edge[] = selectedNodeIds.map((sourceNodeId) => ({
          id: `${sourceNodeId}-${newNode.id}`,
          source: sourceNodeId,
          target: newNode.id,
          type: "editableEdge",
        }));

        // 添加新的边
        setEdges((edges: Edge[]) => [...edges, ...newEdges]);
      } else {
        // 直接添加节点
        setNodes((nds: Node[]) => nds.concat(newNode));
      }

      save();
      return newNode.id;
    },
    [save, setNodes, setEdges, getNodes]
  );

  // 插入子主题 TODO 需合并逻辑 - 目前只支持单个子主题
  const addSubTopNode = useCallback(
    (type: NodeType, options?: Record<string, unknown>) => {
      const {
        data: nodeData,
        position = { x: 0, y: 0 },
        autoConnect = false,
        ...rest
      } = options ?? {};
      
      // 获取当前选中的节点
      const curSelectNodes = getNodes().filter((node) => node.selected);

      const singleNode: any [] = []
      curSelectNodes?.forEach((selectNode:any) => {
        console.log(selectNode, selectNode?.width,  150)
        const newX = (selectNode?.position?.x || 0) + (selectNode?.width || 200) + 150
        const newY = (selectNode?.position?.y || 0) - 50

        const position = {
          x: newX,
          y: newY
        }
        const defaultOptions = createNode(type, position as XYPosition);

        const newNode: Node = {
          ...defaultOptions,
          data: {
            ...(nodeData ?? defaultOptions.data ?? {}),
          },
          ...rest,
        };

        setNodes((nds: Node[]) => nds.concat(newNode));
        singleNode.push(newNode)
        const selectNodeId = selectNode?.id
        const sourceNodeId = selectNodeId
        if (selectNodeId) {
          const newEdge = {
            id: `${sourceNodeId}-${newNode.id}`,
            source: sourceNodeId,
            target: newNode.id,
            type: "editableEdge",
          }
          setEdges((edges: Edge[]) => [...edges, newEdge]);
        }
      })

      save();
      return (singleNode)?.[0]?.id
      // return newNode.id;
    },
    [save, setNodes, setEdges, getNodes]
  );

  // 添加关联节点
  const addConnectedNode = useCallback(
    (type: NodeType, options?: Record<string, unknown>) => {
      return addNode(type, { ...options, autoConnect: true });
    },
    [addNode]
  );

  // 添加独立节点
  const addIndependentNode = useCallback(
    (type: NodeType, options?: Record<string, unknown>) => {
      return addNode(type, { ...options, autoConnect: false });
    },
    [addNode]
  );

  // 复制节点
  const duplicateNode = useCallback(
    (id: string) => {
      const node = getNode(id);

      if (!node || !node.type) {
        return;
      }

      const { id: oldId, ...rest } = node;

      const newId = addNode(node.type as NodeType, {
        ...rest,
        position: {
          x: node.position.x + 200,
          y: node.position.y + 200,
        },
        selected: true,
      });

      setTimeout(() => {
        updateNode(id, { selected: false });
        updateNode(newId, { selected: true });
      }, 0);
    },
    [addNode, getNode, updateNode]
  );

  // 连接处理
  const handleConnect = useCallback<OnConnect>(
    (connection) => {
      
      const newEdge: Edge = {
        id: nanoid(),
        type: "editableEdge",
        ...connection,
      };
      setEdges((eds: Edge[]) => eds.concat(newEdge));
      
      // 如果连接的目标是 LLM 节点，自动添加输入字段
      if (connection.target && connection.source) {
        const nodes = getNodes();
        addAutoInputFields(connection.source, connection.target, nodes, updateNodeData);
      }
      
      save();
    },
    [save, setEdges, getNodes, updateNodeData]
  );

  // 连接结束处理
  const handleConnectEnd = useCallback<OnConnectEnd>(
    (event, connectionState) => {
      if (!connectionState.isValid) {
        const { clientX, clientY } =
          "changedTouches" in event ? event.changedTouches[0] : event;

        const sourceId = connectionState.fromNode?.id;
        const isSourceHandle = connectionState.fromHandle?.type === "source";

        if (!sourceId) {
          return;
        }

        const newNodeId = addNode(NODE_TYPE_MAPS.markdown, {
          position: screenToFlowPosition({ x: clientX, y: clientY }),
        });

        setEdges((eds: Edge[]) =>
          eds.concat({
            id: nanoid(),
            source: isSourceHandle ? sourceId : newNodeId,
            target: isSourceHandle ? newNodeId : sourceId,
            type: "editableEdge",
          })
        );
      }
    },
    [addNode, screenToFlowPosition, setEdges]
  );

  // 全选
  const handleSelectAll = useCallback(() => {
    setNodes((nodes: Node[]) =>
      nodes.map((node: Node) => ({ ...node, selected: true }))
    );
  }, [setNodes]);

  // 复制
  const handleCopy = useCallback(() => {
    const selectedNodes = getNodes().filter((node) => node.selected);
    if (selectedNodes.length > 0) {
      setCopiedNodes(selectedNodes);
    }
  }, [getNodes, setCopiedNodes]);

  // 粘贴
  const handlePaste = useCallback(() => {
    if (copiedNodes.length === 0) {
      return;
    }

    const newNodes = copiedNodes.map((node) => ({
      ...node,
      id: nanoid(),
      position: {
        x: node.position.x + 200,
        y: node.position.y + 200,
      },
      selected: true,
    }));

    // 取消选择所有现有节点
    setNodes((nodes: Node[]) =>
      nodes.map((node: Node) => ({
        ...node,
        selected: false,
      }))
    );

    // 添加新节点
    setNodes((nodes: Node[]) => [...nodes, ...newNodes]);
    save();
  }, [copiedNodes, setNodes, save]);

  // 批量复制选中节点
  const handleDuplicateAll = useCallback(() => {
    const selected = getNodes().filter((node) => node.selected);

    for (const node of selected) {
      duplicateNode(node.id);
    }
  }, [getNodes, duplicateNode]);

  // 删除边
  const deleteEdgeById = useCallback(
    async (edgeId: string) => {
      try {
        await deleteLink({ wid: getWsId(), eid: edgeId });
        deleteElements({ edges: [{ id: edgeId }] });
      } catch (error) {
        console.error("Failed to delete edge:", error);
        throw error;
      }
    },
    [deleteElements]
  );

  // 批量删除边
  const deleteEdgesByIds = useCallback(
    async (edgeIds: string[]) => {
      try {
        await Promise.all(
          edgeIds.map((id) => deleteLink({ wid: getWsId(), eid: id }))
        );

        deleteElements({
          edges: edgeIds.map((id) => ({ id })),
        });
      } catch (error) {
        console.error("Failed to delete edges:", error);
        throw error;
      }
    },
    [deleteElements]
  );

  // 添加自由节点（上下文菜单）
  const handleAddFreeNode = useCallback(
    (menuX: number, menuY: number) => {
      const position = screenToFlowPosition({ x: menuX, y: menuY });
      const newNode = createNode(NODE_TYPE_MAPS.groupNode, {
        x: position.x,
        y: position.y,
      });
      setNodes((prevNodes) => [...prevNodes, newNode]);
      save();
    },
    [screenToFlowPosition, setNodes, save]
  );

  // 插入子主题
  const handleAddSubTopic = useCallback(() => {
    addConnectedNode(NODE_TYPE_MAPS.markdown);
  }, [addConnectedNode]);


  const handleAddSubTopNode = useCallback(
    () => {
      return addSubTopNode(NODE_TYPE_MAPS.markdown);
    },
    [addNode]
  );

  // 生成概括
  const handleGenerateSummary = useCallback(() => {
    const selectedNodes = getNodes().filter((node) => node.selected);

    if (selectedNodes.length === 0) {
      toast.error("请先选择要生成概括的节点");
      return;
    }

    // 计算选中节点的中心位置
    const positions = selectedNodes.map((node) => {
      const internalNode = getInternalNode(node.id);
      return {
        x:
          (internalNode?.internals.positionAbsolute?.x ?? node.position.x) +
          (internalNode?.measured?.width ?? 200) / 2,
        y:
          (internalNode?.internals.positionAbsolute?.y ?? node.position.y) +
          (internalNode?.measured?.height ?? 100) / 2,
      };
    });

    const centerX =
      positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY =
      positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;

    const summaryPosition = {
      x: centerX - 100,
      y: centerY - 150,
    };

    const summaryNodeId = addConnectedNode(NODE_TYPE_MAPS.markdown, {
      position: summaryPosition,
      data: {
        title: "概括",
        content: `基于 ${selectedNodes.length} 个节点的概括`,
      },
    });

    toast.success(`已创建概括节点并连接到 ${selectedNodes.length} 个选中节点`);
    return summaryNodeId;
  }, [getNodes, getInternalNode, addConnectedNode]);

  // 发送到聊天
  const handleToChat = useCallback(() => {
    const selectedNodes = getNodes()
      .filter((node) => node.selected)
      .filter((node) => !!(node.data?.content as string)?.trim());

    if (selectedNodes.length === 0) {
      toast.error("当前节点为空");
      return;
    }
    selectedNodes.forEach((node) => handleChat?.(node.id));
  }, [getNodes, handleChat]);

  // 发送到笔记
  const handleToNotePad = useCallback(() => {
    if (!notePanelOpen) {
      toggleNotePanel(true);
    }
    const selectedNodes = getNodes().filter((node) => node.selected);
    if (selectedNodes.length === 0) {
      toast.error("当前节点为空");
      return;
    }
    const lastNode = selectedNodes.at(-1);
    const { title, content } =
      (lastNode?.data as { title: string; content: string }) || {};
    if (!title && !content) {
      toast.error("当前节点为空");
      return;
    }
    if (lastNode?.id) {
      handleCopyToNotebook([lastNode?.id]);
    }
  }, [getNodes, notePanelOpen, toggleNotePanel, handleCopyToNotebook]);

  // 清空节点内容
  const handleClearNodeContent = useCallback(() => {
    const selectedNodes = getNodes().filter((node) => node.selected);
    if (selectedNodes.length === 0) {
      toast.error("请先选择要清空的节点");
      return;
    }

    selectedNodes.forEach((node) => {
      updateNodeData(node.id, {
        title: "",
        content: "",
      });
    });

    toast.success(`已清空 ${selectedNodes.length} 个节点的内容`);
    save();
  }, [getNodes, updateNodeData, save]);

  // 删除节点
  const handleDelete = useCallback(async () => {
    const selectedNodes = getNodes().filter((node) => node.selected);
    if (selectedNodes.length === 0) {
      toast.error("请先选择要删除的节点");
      return;
    }

    const selectedNodeIds = selectedNodes.map((node) => node.id);
    
    try {
      // 删除节点
      setNodes((prevNodes) =>
        prevNodes.filter((node) => !selectedNodeIds.includes(node.id))
      );

      // 删除对应的高亮 - 从内存中删除
      const highlights = getNodeHighlight(selectedNodeIds);
      if (highlights.length > 0) {
        const { batchRemoveHighlights } = usePdfStore.getState();
        batchRemoveHighlights(highlights);
        
        // 同时从本地数据库中删除高亮
        const { getDatabase } = await import('@/local');
        const db = await getDatabase();
        if (db && db.marks) {
          await db.marks.find({
            selector: {
              node_id: { $in: selectedNodeIds }
            }
          }).remove();
          console.log('已从数据库中删除节点对应的高亮:', selectedNodeIds);
        }
      }

      toast.success(`已删除 ${selectedNodes.length} 个节点`);
      save();
    } catch (error) {
      console.error('删除节点和高亮失败:', error);
      toast.error('删除节点失败');
    }
  }, [getNodes, setNodes, save]);

  // 创建组
  const handleCreateGroup = useCallback(() => {
    const selectedNodes = getNodes().filter((node) => node.selected);
    const internalNodes = selectedNodes.map((node) => getInternalNode(node.id));

    const minX = Math.min(
      ...internalNodes.map((node) => node?.internals.positionAbsolute?.x ?? 0)
    );
    const minY = Math.min(
      ...internalNodes.map((node) => node?.internals.positionAbsolute?.y ?? 0)
    );
    const maxX = Math.max(
      ...internalNodes.map(
        (node) =>
          (node?.internals.positionAbsolute?.x ?? 0) +
          (node?.measured?.width ?? 0)
      )
    );
    const maxY = Math.max(
      ...internalNodes.map(
        (node) =>
          (node?.internals.positionAbsolute?.y ?? 0) +
          (node?.measured?.height ?? 0)
      )
    );

    const parentId = `group-${Date.now()}`;
    const padding = 50;
    const parentPosition = {
      x: minX - padding,
      y: minY - padding,
    };

    const groupNode = {
      id: parentId,
      type: NODE_TYPE_MAPS.groupNode,
      width: maxX - minX + padding * 2,
      height: maxY - minY + padding * 2,
      position: parentPosition,
      data: {
        label: "Group",
      },
    };

    const allNodes = getNodes();
    const selectedNodeIds = new Set(selectedNodes.map((node) => node.id));

    const nodesToUpdate = selectedNodes.map((node) => {
      const internalNode = getInternalNode(node.id);
      const abs = internalNode?.internals.positionAbsolute;
      return {
        ...node,
        parentId,
        selected: false,
        position: {
          x: (abs?.x ?? 0) - parentPosition.x,
          y: (abs?.y ?? 0) - parentPosition.y,
        },
      };
    });

    const newNodes = [
      groupNode,
      ...allNodes.filter((node) => !selectedNodeIds.has(node.id)),
      ...nodesToUpdate,
    ];

    setNodes(newNodes);
    save();
  }, [getNodes, setNodes, getInternalNode, save]);

  return {
    // 节点操作
    addNode,
    addConnectedNode,
    addIndependentNode,
    duplicateNode,
    handleSelectAll,
    handleCopy,
    handlePaste,
    handleDuplicateAll,

    // 边操作
    handleConnect,
    handleConnectEnd,
    deleteEdgeById,
    deleteEdgesByIds,

    // 上下文菜单操作
    handleAddFreeNode,
    handleAddSubTopic,
    handleAddSubTopNode,
    handleGenerateSummary,
    handleToChat,
    handleToNotePad,
    handleClearNodeContent,
    handleDelete,
    handleCreateGroup,
  };
};
