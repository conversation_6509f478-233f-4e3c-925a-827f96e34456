import { use<PERSON><PERSON>back, useState, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRef, useEffect } from "react";
import { 
  useReactFlow, 
  type Node, 
  type Edge,
  OnConnectStart,
  IsValidConnection,
  getOutgoers
} from "@xyflow/react";
import { toast } from "sonner";
import { getRectRelation, type RelationResult } from "../lib/rect";
import { NODE_TYPE_MAPS, NodeType } from "../components/node";
import { 
  filterOutTemporaryNodes, 
  filterOutTemporaryEdges 
} from "../lib/temporary-node-utils";
import { addAutoInputFields } from "./use-canvas-operations";
// import { useAlignmentSystem } from "./use-alignment-system";

const MIN_DISTANCE = 50;

interface UseCanvasInteractionsProps {
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  save: () => void;
  addNode: (type: NodeType, options?: Record<string, unknown>) => string;
}

// 验证连接源和目标的合法性
const isValidSourceTarget = (source: Node, target: Node) => {
  // 基础节点类型验证
  if (source.type === "video" || source.type === "drop") {
    return false;
  }

  if (target.type === "audio" && source.type !== "text") {
    return false;
  }

  if (target.type === "file") {
    return false;
  }

  // 防止结束节点连接到结束节点
  if (source.type === NODE_TYPE_MAPS.llmEndNode && target.type === NODE_TYPE_MAPS.llmEndNode) {
    toast.error("连接失败", {
      description: "结束节点不能连接到另一个结束节点",
      duration: 3000,
    });
    return false;
  }
  
  // 防止结束节点作为源节点（结束节点应该是工作流的终点）
  if (source.type === NODE_TYPE_MAPS.llmEndNode) {
    toast.error("连接失败", {
      description: "结束节点不能连接到其他节点，它应该是工作流的终点",
      duration: 3000,
    });
    return false;
  }

  // LLM 节点的连接规则
  if (source.type === NODE_TYPE_MAPS.llm) {
    // LLM 节点只能连接到其他 LLM 节点或结束节点
    if (target.type === NODE_TYPE_MAPS.llm || target.type === NODE_TYPE_MAPS.llmEndNode) {
      return true;
    }
    // LLM 节点不能连接到普通节点
    toast.error("连接失败", {
      description: "LLM 节点只能连接到其他 LLM 节点或结束节点",
      duration: 3000,
    });
    return false;
  }

  // 允许普通节点连接到 LLM 节点（普通节点作为父节点）
  if (target.type === NODE_TYPE_MAPS.llm) {
    return true;
  }

  // 限制普通节点连接到结束节点（结束节点只能接收LLM节点的输出）
  if (target.type === NODE_TYPE_MAPS.llmEndNode) {
    toast.error("连接失败", {
      description: "结束节点只能接收 LLM 节点的输出",
      duration: 3000,
    });
    return false;
  }

  // 允许普通节点之间的连接（符合基础验证的）
  return true;
};

export const useCanvasInteractions = (props: UseCanvasInteractionsProps) => {
  const { setNodes, setEdges, save, addNode } = props;
  
  const { 
    getInternalNode, 
    screenToFlowPosition,
    getNodes,
    getEdges,
    getIntersectingNodes,
    updateNodeData
  } = useReactFlow();

  // 拖拽状态管理
  const [dropParentId, setDropParentId] = useState<string | undefined>(undefined);
  const [closestNodeForConnection, setClosestNodeForConnection] = useState<string | undefined>(undefined);
  
  // 防抖处理
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 组件销毁时清理防抖计时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 对齐系统
  // const alignmentSystem = useAlignmentSystem({
  //   enabled: true,
  //   snapDistance: 8,
  //   nodeTypes: [NODE_TYPE_MAPS.markdown]
  // });

  // 获取距离拖拽节点中心最近的节点
  const getClosestNodeForConnection = useCallback((draggedNode: Node) => {
    // 只允许非分组节点参与连线
    if (draggedNode.type === NODE_TYPE_MAPS.groupNode) {
      return null;
    }

    const nodes = getNodes();
    const internalNode = getInternalNode(draggedNode.id);
    if (!internalNode) return null;

    const currentPos = internalNode.internals.positionAbsolute;
    const currentWidth = draggedNode.measured?.width || 0;
    const currentHeight = draggedNode.measured?.height || 0;
    const currentCenterX = currentPos.x + currentWidth / 2;
    const currentCenterY = currentPos.y + currentHeight / 2;

    const closestNode = nodes.reduce(
      (res, n) => {
        // 跳过自己和分组节点
        if (n.id === draggedNode.id || n.type === NODE_TYPE_MAPS.groupNode) {
          return res;
        }

        const targetInternal = getInternalNode(n.id);
        if (!targetInternal) return res;

        const targetPos = targetInternal.internals.positionAbsolute;
        const targetWidth = n.measured?.width || 0;
        const targetHeight = n.measured?.height || 0;
        
        // 计算目标节点中心点
        const targetCenterX = targetPos.x + targetWidth / 2;
        const targetCenterY = targetPos.y + targetHeight / 2;
        
        // 计算中心点之间的距离
        const dx = targetCenterX - currentCenterX;
        const dy = targetCenterY - currentCenterY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 只有距离小于阈值才考虑连接
        if (distance < MIN_DISTANCE && distance < res.distance) {
          res.distance = distance;
          res.node = n;
        }

        return res;
      },
      {
        distance: Number.MAX_VALUE,
        node: null as Node | null,
      }
    );

    return closestNode.node;
  }, [getNodes, getInternalNode]);

  // 获取最近的边，用于自动连线
  const getClosestEdge = useCallback((node: Node) => {
    const closestNode = getClosestNodeForConnection(node);
    if (!closestNode) return null;

    const internalNode = getInternalNode(node.id);
    const closestInternal = getInternalNode(closestNode.id);
    if (!internalNode || !closestInternal) return null;

    const currentPos = internalNode.internals.positionAbsolute;
    const closestPos = closestInternal.internals.positionAbsolute;

    const closeNodeIsSource = closestPos.x < currentPos.x;

    return {
      id: closeNodeIsSource
        ? `${closestNode.id}-${node.id}`
        : `${node.id}-${closestNode.id}`,
      source: closeNodeIsSource ? closestNode.id : node.id,
      target: closeNodeIsSource ? node.id : closestNode.id,
    };
  }, [getClosestNodeForConnection, getInternalNode]);

  // 节点拖拽处理
  const onNodeDrag = useCallback(
    (_event: React.MouseEvent, draggedNode: Node) => {
      const internalNode = getInternalNode(draggedNode.id);
      const abs = internalNode?.internals.positionAbsolute;

      if (!abs) return;

      // 分组节点移动时不触发连线和重叠检测逻辑
      if (draggedNode.type === NODE_TYPE_MAPS.groupNode) {
        // 只处理对齐系统
        // const snapResult = alignmentSystem.calculateSnapPosition(draggedNode, {
        //   x: abs.x,
        //   y: abs.y
        // });
        
        // alignmentSystem.setGuides(snapResult.guides);

        // if (snapResult.position.x !== abs.x || snapResult.position.y !== abs.y) {
        //   setNodes((currentNodes) =>
        //     currentNodes.map((node) =>
        //       node.id === draggedNode.id
        //         ? {
        //             ...node,
        //             position: snapResult.position
        //           }
        //         : node
        //     )
        //   );
        // }
        return;
      }

      // 获取所有相交的节点进行重叠检测
      const intersectingNodes = getIntersectingNodes(draggedNode, true)
        .filter(n => n.id !== draggedNode.id);

      const intersectingGroups = intersectingNodes.filter(n => n.type === NODE_TYPE_MAPS.groupNode);
      const intersectingRegularNodes = intersectingNodes.filter(n => n.type !== NODE_TYPE_MAPS.groupNode);

      // 处理分组包含关系
      let containingGroup: Node | undefined;
      const nodeRelations = new Map<string, string>();

      intersectingGroups.forEach(group => {
        const groupInternal = getInternalNode(group.id);
        const groupAbs = groupInternal?.internals.positionAbsolute;
        
        if (!groupAbs) return;

        const relation = getRectRelation(
          {
            x: abs.x,
            y: abs.y,
            width: draggedNode.measured?.width ?? 0,
            height: draggedNode.measured?.height ?? 0,
          },
          {
            x: groupAbs.x,
            y: groupAbs.y,
            width: group.measured?.width ?? 0,
            height: group.measured?.height ?? 0,
          }
        );

        nodeRelations.set(group.id, relation);
        
        if (relation === "contain" && !containingGroup) {
          containingGroup = group;
        }
      });

      // 计算与普通节点的重叠关系和距离
      const intersectingRegularNodesWithDistance = intersectingRegularNodes.map(node => {
        const nodeInternal = getInternalNode(node.id);
        const nodeAbs = nodeInternal?.internals.positionAbsolute;
        
        if (!nodeAbs) return null;

        const relation = getRectRelation(
          {
            x: abs.x,
            y: abs.y,
            width: draggedNode.measured?.width ?? 0,
            height: draggedNode.measured?.height ?? 0,
          },
          {
            x: nodeAbs.x,
            y: nodeAbs.y,
            width: node.measured?.width ?? 0,
            height: node.measured?.height ?? 0,
          }
        );

        // 计算中心点距离
        const draggedCenterX = abs.x + (draggedNode.measured?.width ?? 0) / 2;
        const draggedCenterY = abs.y + (draggedNode.measured?.height ?? 0) / 2;
        const nodeCenterX = nodeAbs.x + (node.measured?.width ?? 0) / 2;
        const nodeCenterY = nodeAbs.y + (node.measured?.height ?? 0) / 2;
        
        const distance = Math.sqrt(
          Math.pow(draggedCenterX - nodeCenterX, 2) + 
          Math.pow(draggedCenterY - nodeCenterY, 2)
        );

        nodeRelations.set(node.id, relation);
        
        return {
          node,
          distance,
          relation
        };
      }).filter((item): item is { node: Node; distance: number; relation: RelationResult } => Boolean(item));

      // 按距离排序，找到最近的重叠节点
      intersectingRegularNodesWithDistance.sort((a, b) => (a?.distance || 0) - (b?.distance || 0));
      const closestOverlappingNode = intersectingRegularNodesWithDistance.length > 0 
        ? intersectingRegularNodesWithDistance[0]?.node || null
        : null;

      // 找到距离最近的节点用于连线提示（包括未重叠的节点）
      const closestNode = getClosestNodeForConnection(draggedNode);
      
      // 防抖更新节点样式
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      
      debounceTimerRef.current = setTimeout(() => {
        // 更新节点状态和样式
        setNodes((currentNodes) => {
          return currentNodes.map((node) => {
            if (node.id === draggedNode.id) return node;
            
            // 处理重叠节点的样式
            if (nodeRelations.has(node.id)) {
              const relation = nodeRelations.get(node.id);
              const isGroupNode = node.type === NODE_TYPE_MAPS.groupNode;
              
              if (isGroupNode) {
                // 分组节点使用原有样式
                return {
                  ...node,
                  className: relation === "contain" ? "highlight_contain"
                            : relation === "overlap" ? "highlight_overlap"
                            : ""
                };
              } else {
                // 普通节点重叠时：检查连接有效性决定颜色
                if (closestOverlappingNode && node.id === closestOverlappingNode.id) {
                  // 检查连接是否有效
                  const isConnectionValid = isValidSourceTarget(node, draggedNode);
                  return {
                    ...node,
                    className: isConnectionValid ? "highlight_connection_ready" : "highlight_invalid_connection"
                  };
                } else {
                  // 其他重叠节点 - 红色（不会连接）
                  return {
                    ...node,
                    className: "highlight_overlap"
                  };
                }
              }
            }
            
            // 处理连线匹配提示（距离最近但不重叠的节点）
            if (closestNode && node.id === closestNode.id && !nodeRelations.has(node.id)) {
              // 检查连接是否有效
              const isConnectionValid = isValidSourceTarget(node, draggedNode);
              return {
                ...node,
                className: isConnectionValid ? "highlight_connection_match" : "highlight_invalid_connection"
              };
            }
            
            // 清除其他节点的样式
            if (node.className) {
              return { ...node, className: "" };
            }
            
            return node;
          });
        });
      }, 50); // 50ms 防抖延迟

      // 立即设置状态（不防抖，因为这些对性能影响较小）
      setDropParentId(containingGroup?.id);
      setClosestNodeForConnection(closestNode?.id);

      // 处理对齐系统（只对 markdown 节点）
      // if (draggedNode.type === NODE_TYPE_MAPS.markdown) {
      //   const snapResult = alignmentSystem.calculateSnapPosition(draggedNode, {
      //     x: abs.x,
      //     y: abs.y
      //   });
      //   
      //   alignmentSystem.setGuides(snapResult.guides);

      //   if (snapResult.position.x !== abs.x || snapResult.position.y !== abs.y) {
      //     setNodes((currentNodes) =>
      //       currentNodes.map((node) =>
      //         node.id === draggedNode.id
      //           ? {
      //               ...node,
      //               position: snapResult.position
      //             }
      //           : node
      //       )
      //     );
      //   }
      // }
    },
    [setNodes, getInternalNode, getIntersectingNodes, getClosestNodeForConnection]
  );

  // 更新父节点ID的辅助函数
  const updateParentId = useCallback(
    (nodeId: string, parentId: string) => {
      const internalNode = getInternalNode(nodeId);
      const abs = internalNode?.internals.positionAbsolute;
      const parentNode = getInternalNode(parentId);
      const parentAbs = parentNode?.internals.positionAbsolute;
      return {
        x: (abs?.x ?? 0) - (parentAbs?.x ?? 0),
        y: (abs?.y ?? 0) - (parentAbs?.y ?? 0),
      };
    },
    [getInternalNode]
  );

  // 验证连接合法性
  const isValidConnection = useCallback<IsValidConnection>(
    (connection) => {
      const nodes = getNodes();
      const edges = getEdges();
      const target = nodes.find((node) => node.id === connection.target);

      if (connection.source) {
        const source = nodes.find((node) => node.id === connection.source);

        if (!source || !target) {
          return false;
        }

        const valid = isValidSourceTarget(source, target);

        if (!valid) {
          return false;
        }
      }

      // 防止循环连接
      const hasCycle = (node: Node, visited = new Set<string>()) => {
        if (visited.has(node.id)) {
          return false;
        }

        visited.add(node.id);

        for (const outgoer of getOutgoers(node, nodes, edges)) {
          if (outgoer.id === connection.source || hasCycle(outgoer, visited)) {
            return true;
          }
        }
      };

      if (!target || target.id === connection.source) {
        return false;
      }

      return !hasCycle(target);
    },
    [getNodes, getEdges]
  );

  // 节点拖拽结束处理
  const onNodeDragStop = useCallback(
    (_event: React.MouseEvent, draggedNode: Node) => {
      // 清理防抖计时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      
      // 清除对齐指导线
      // alignmentSystem.clearGuides();

      // 分组节点拖拽结束时不执行连线逻辑
      if (draggedNode.type === NODE_TYPE_MAPS.groupNode) {
        save();
        return;
      }

      // 处理分组节点关系
      setNodes((currentNodes) =>
        currentNodes.map((node) => {
          // 处理被拖拽的节点
          if (node.id === draggedNode.id) {
            if (dropParentId) {
              // 设置为子节点
              return {
                ...node,
                position: updateParentId(node.id, dropParentId),
                parentId: dropParentId,
              };
            } else {
              // 取消父子关系，使用绝对位置
              const internalNode = getInternalNode(node.id);
              const abs = internalNode?.internals.positionAbsolute;

              return {
                ...node,
                position: {
                  x: abs?.x ?? 0,
                  y: abs?.y ?? 0,
                },
                parentId: undefined,
              };
            }
          }
          
          // 清除所有节点的高亮样式
          if (node.className) {
            return {
              ...node,
              className: "",
            };
          }
          
          return node;
        })
      );

      // 检查是否有重叠的普通节点需要连线
      const overlappingRegularNodes = getIntersectingNodes(draggedNode, true)
        .filter(n => n.id !== draggedNode.id && n.type !== NODE_TYPE_MAPS.groupNode);

      if (overlappingRegularNodes.length > 0) {
        // 计算距离，只与最近的重叠节点创建连线
        const internalNode = getInternalNode(draggedNode.id);
        const abs = internalNode?.internals.positionAbsolute;
        
        if (abs) {
          const draggedCenterX = abs.x + (draggedNode.measured?.width ?? 0) / 2;
          const draggedCenterY = abs.y + (draggedNode.measured?.height ?? 0) / 2;
          
          const closestOverlappingNodeAtDragEnd = overlappingRegularNodes
            .map(node => {
              const nodeInternal = getInternalNode(node.id);
              const nodeAbs = nodeInternal?.internals.positionAbsolute;
              
              if (!nodeAbs) return null;
              
              const nodeCenterX = nodeAbs.x + (node.measured?.width ?? 0) / 2;
              const nodeCenterY = nodeAbs.y + (node.measured?.height ?? 0) / 2;
              
              const distance = Math.sqrt(
                Math.pow(draggedCenterX - nodeCenterX, 2) + 
                Math.pow(draggedCenterY - nodeCenterY, 2)
              );
              
              return { node, distance };
            })
            .filter((item): item is { node: Node; distance: number } => Boolean(item))
            .sort((a, b) => a.distance - b.distance)[0]?.node;
          
          // 只与最近的重叠节点创建连线
          if (closestOverlappingNodeAtDragEnd) {
            const targetNode = closestOverlappingNodeAtDragEnd;
            
            // 直接创建连线，无需依赖getClosestEdge的复杂逻辑
            const internalDraggedNode = getInternalNode(draggedNode.id);
            const internalTargetNode = getInternalNode(targetNode.id);
            
            if (internalDraggedNode && internalTargetNode) {
              // 拖拽的节点永远是连线的终点（target）
              const sourceId = targetNode.id; // 重叠的节点作为起点
              const targetId = draggedNode.id; // 拖拽的节点作为终点
              const edgeId = `${sourceId}-${targetId}`;
              
              setEdges((es) => {
                const nextEdges = es.filter((e) => e.className !== 'temp');

                // 检查是否已存在相同连线
                const existingEdge = nextEdges.find(
                  (ne) =>
                    (ne.source === sourceId && ne.target === targetId) ||
                    (ne.source === targetId && ne.target === sourceId)
                );

                if (!existingEdge) {
                  // 检查连接是否有效
                  const connectionValid = isValidConnection({
                    source: sourceId,
                    target: targetId,
                    sourceHandle: null,
                    targetHandle: null,
                  });

                  if (connectionValid) {
                    const newEdge: Edge = {
                      id: edgeId,
                      source: sourceId,
                      target: targetId,
                      type: 'editableEdge'
                    };
                    nextEdges.push(newEdge);
                    
                    // 拖拽连线时也添加自动字段
                    const currentNodes = getNodes();
                    addAutoInputFields(sourceId, targetId, currentNodes, updateNodeData);
                  }
                }

                return nextEdges;
              });
            }
          }
        }
      } else if (closestNodeForConnection) {
        // 如果没有重叠节点，则与距离最近的节点连线
        const closestNode = getNodes().find(n => n.id === closestNodeForConnection);
        if (closestNode) {
          // 直接创建连线，无需依赖getClosestEdge的复杂逻辑
          const internalDraggedNode = getInternalNode(draggedNode.id);
          const internalClosestNode = getInternalNode(closestNode.id);
          
          if (internalDraggedNode && internalClosestNode) {
            // 拖拽的节点永远是连线的终点（target）
            const sourceId = closestNode.id; // 最近的节点作为起点
            const targetId = draggedNode.id; // 拖拽的节点作为终点
            const edgeId = `${sourceId}-${targetId}`;
            
            setEdges((es) => {
              const nextEdges = es.filter((e) => e.className !== 'temp');

              // 检查是否已存在相同连线
              const existingEdge = nextEdges.find(
                (ne) =>
                  (ne.source === sourceId && ne.target === targetId) ||
                  (ne.source === targetId && ne.target === sourceId)
              );

              if (!existingEdge) {
                // 检查连接是否有效
                const connectionValid = isValidConnection({
                  source: sourceId,
                  target: targetId,
                  sourceHandle: null,
                  targetHandle: null,
                });

                if (connectionValid) {
                  const newEdge: Edge = {
                    id: edgeId,
                    source: sourceId,
                    target: targetId,
                    type: 'editableEdge'
                  };
                  nextEdges.push(newEdge);
                  
                  // 拖拽连线时也添加自动字段  
                  const currentNodes = getNodes();
                  addAutoInputFields(sourceId, targetId, currentNodes, updateNodeData);
                }
              }

              return nextEdges;
            });
          }
        }
      }

      // 重置状态
      setDropParentId(undefined);
      setClosestNodeForConnection(undefined);
      save();
    },
    [setNodes, setEdges, getInternalNode, updateParentId, dropParentId, closestNodeForConnection, getClosestEdge, isValidConnection, save, getNodes]
  );

  // 连接开始处理
  const handleConnectStart = useCallback<OnConnectStart>(() => {
    // 删除拖拽时的临时节点
    setNodes((nds: Node[]) => filterOutTemporaryNodes(nds));
    setEdges((eds: Edge[]) => filterOutTemporaryEdges(eds));
    save();
  }, [setNodes, setEdges, save]);

  // 双击创建markdown节点
  const createMarkdownNode = useCallback<MouseEventHandler<HTMLDivElement>>(
    (event) => {
      if (!(event.target instanceof HTMLElement)) {
        return;
      }

      // 检查是否点击在空白画板上
      if (!event.target.classList.contains("react-flow__pane")) {
        return;
      }

      const { x, y } = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });
      
      addNode(NODE_TYPE_MAPS.markdown, {
        position: { x, y },
      });
    },
    [addNode, screenToFlowPosition]
  );

  // 拖拽Drop处理
  const handleDrop = useCallback<MouseEventHandler<HTMLDivElement>>((event) => {
    if (!(event.target instanceof HTMLElement)) {
      return;
    }

    // 检查是否点击在空白画板上
    if (!event.target.classList.contains("react-flow__pane")) {
      return;
    }

    const { x, y } = screenToFlowPosition({
      x: event.clientX,
      y: event.clientY,
    });
    addNode(NODE_TYPE_MAPS.markdown, {
      position: { x, y },
    });
  }, [addNode, screenToFlowPosition]);

  // 右键菜单处理
  const handleContextMenu = useCallback((event: MouseEvent) => {
    if (
      !(event.target instanceof HTMLElement) ||
      !event.target.classList.contains("react-flow__pane")
    ) {
      event.preventDefault();
    }
  }, []);

  // 连接线样式
  const connectionLineStyle = {
    strokeWidth: 2,
    stroke: '#999',
  };

  return {
    // 拖拽相关
    onNodeDrag,
    onNodeDragStop,
    
    // 连接相关
    handleConnectStart,
    isValidConnection,
    connectionLineStyle,
    
    // 交互事件
    createMarkdownNode,
    handleDrop,
    handleContextMenu,
    
    // 对齐系统
    // alignmentSystem,
  };
};