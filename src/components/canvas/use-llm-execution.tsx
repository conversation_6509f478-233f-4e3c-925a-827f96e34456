import { useState, useCallback, useEffect } from "react";
import {
  useLLMOutput,
  LLMOutputConfig,
  LLMOutputResult,
} from "./components/use-llm-output";
import { useReactFlow, type Edge, type Node } from "@xyflow/react";
import { NODE_TYPE_MAPS } from "./components/node";
import { forEach } from "lodash-es";

interface InputField {
  name: string;
  nodeTitle: string;
  nodeId: string | undefined;
  sourceType: "nodes" | "pdfs" | undefined | string;
}

interface NodeData {
  inputFields?: InputField[];
  userPrompt?: string;
  model?: string;
  systemPrompt?: string;
  content?: string;
  lastExecutionTime?: string | null;
  executionStatus?: "completed" | "error" | null;
  error?: string | null;
  [key: string]: any;
}

export const useLLMExecution = () => {
  const { generateLLMOutput } = useLLMOutput();
  const { updateNodeData, getNode } = useReactFlow();

  // 执行 LLM 输出
  const executeLLM = useCallback(
    async (nodeId: string) => {
      const currentNode = getNode(nodeId);
      const nodeData = currentNode?.data as NodeData;
      const prompts: any[] = nodeData?.prompts;
      try {
        if (prompts.length === 0) {
          throw new Error("没有找到有效的输入数据");
        }

        // 准备配置
        const config: LLMOutputConfig = {
          model: nodeData?.model,
          systemPrompt:
            nodeData?.systemPrompt ||
            "你是一个有用的AI助手，请根据用户提供的内容进行回答。",
          temperature: 0.7,
          maxTokens: 1000,
          stream: true, // 启用流式输出
          provider: "deepseek",
        };

        const result = await generateLLMOutput(prompts, config, (content) => {
          console.log("content", content);
          updateNodeData(nodeId, {
            ...currentNode?.data,
            content: content,
            executionState: "running",
          });
        });
        console.log("result", result);
        updateNodeData(nodeId, {
          ...currentNode?.data,
          content: result.content,
          lastExecutionTime: new Date().toISOString(),
          executionState: "success",
        });

        // 执行结束，要不要更新？
      } catch (err) {
        console.error("LLM 执行失败:", err);
        updateNodeData(nodeId, {
          ...currentNode?.data,
          content: "",
          lastExecutionTime: new Date().toISOString(),
          executionState: "error",
        });
      }
    },
    [generateLLMOutput, getNode]
  );

  const runDataFlow = useCallback(
    async (startNodeId: string, edges: Edge[], nodes: Node[]) => {
      // 用于记录已访问的节点，避免循环依赖
      const visited = new Set();
      // 深度优先遍历函数，用于执行工作流
      async function traverse(nodeId: string) {
        // 如果节点已访问过，直接返回（防止循环）
        if (visited.has(nodeId)) return;
        // 标记当前节点为已访问
        visited.add(nodeId);
        // 先执行当前agent节点，如果执行成功，则流转到下一个链接的节点
        const node = getNode(nodeId);
        if (node?.type === NODE_TYPE_MAPS.llm) {
          console.log(`${nodeId} 是 agent 节点，开始执行`);
          // 先mock 数据吧
          const currentNode = getNode(nodeId);
          updateNodeData(nodeId, {
            ...currentNode?.data,
            executionState: "running",
          });
          try {
            await executeLLM(nodeId);
            // updateNodeData(nodeId, {
            //   ...currentNode?.data,
            //   executionState: "success",
            // });
          } catch (error) {
            console.error("LLM 执行失败:", error);
            updateNodeData(nodeId, {
              ...currentNode?.data,
              executionState: "error",
            });
          }
        }

        const outEdges = edges.filter((e) => e.source === nodeId);
        // 遍历所有出边
        for (const edge of outEdges) {
          const nextNodeId = edge.target;

          console.log(`数据从 ${nodeId} -> ${nextNodeId}`);
          // 递归处理下一个节点
          await traverse(nextNodeId);
        }
      }
      // 从起始节点开始执行工作流
      await traverse(startNodeId);
    },
    [executeLLM, updateNodeData, getNode]
  );

  return {
    executeLLM,
    runDataFlow,
  };
};
