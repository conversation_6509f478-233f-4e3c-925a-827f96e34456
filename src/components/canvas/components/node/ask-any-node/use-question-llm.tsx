import { useLLMOutput } from "../../use-llm-output";
import { useReactFlow } from "@xyflow/react";

const useQuestionLLM = () => {
  const { getNode, updateNodeData } = useReactFlow();
  const { generateLLMOutput } = useLLMOutput();
  const updateQuestionLLM = (id: string, content: string) => {
    const node = getNode(id);
    console.log("node", node, content);
    const prompts = [
      content,
      `
       根据 ${node?.data?.title} 问题，生成完美的文章
      `,
    ];
    generateLLMOutput(
      prompts,
      {
        model: "gpt-4o-mini",
        temperature: 0.5,
        stream: true, // 启用流式输出
      },
      (res: any) => {
        updateNodeData(id, {
          content: res,
          ...node?.data,
        });
      }
    );
  };
  return { updateQuestionLLM };
};

export default useQuestionLLM;
