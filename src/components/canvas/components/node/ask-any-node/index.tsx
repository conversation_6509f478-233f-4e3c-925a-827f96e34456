import { useState, useCallback, useEffect, useRef } from "react";
import { useReactFlow, <PERSON><PERSON>, Position } from "@xyflow/react";
import { nanoid } from "nanoid";
import { createNode } from "@/components/canvas/lib/node-factory";
import { NODE_TYPE_MAPS } from "@/components/canvas/lib/node-types";
import {
  filterOutTemporaryNodes,
  filterOutTemporaryEdges,
  createTemporaryEdgeId,
  removeAllTemporaryElements,
} from "@/components/canvas/lib/temporary-node-utils";
import useQuestion from "./use-question";
import useQuestionLLM from "./use-question-llm";
import { Content } from "./content";

type Prop = {
  data: {
    isSource?: boolean;
    content: string;
  };
  selected: boolean;
  id: string;
};

export const AskAnyNode = ({ data, id, selected }: Prop) => {
  const [isHovering, setIsHovering] = useState(false);
  const [hasTemporaryNodes, setHasTemporaryNodes] = useState(false);
  const [customQuestions, setCustomQuestions] = useState<
    { question: string; expert: string }[]
  >([]);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const {
    addNodes,
    addEdges,
    getNode,
    getNodes,
    setNodes,
    setEdges,
    getEdges,
  } = useReactFlow();
  const { questions } = useQuestion(data.content);
  const { updateQuestionLLM } = useQuestionLLM();
  // 统一的位置计算函数 - React Flow 最佳实践
  const calculateAllPositions = useCallback(
    (
      basePosition: { x: number; y: number },
      nodeWidth: number,
      spacing: number,
      rightOffset: number,
      questionsCount: number,
      customCount: number
    ) => {
      return {
        inputNode: {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y - 80, // 位于源节点上方
        },
        questionNodes: Array.from({ length: questionsCount }, (_, i) => ({
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + i * spacing,
        })),
        customNodes: Array.from({ length: customCount }, (_, i) => ({
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + (questionsCount + i) * spacing,
        })),
        confirmButton: {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + (questionsCount + customCount) * spacing,
        },
      };
    },
    []
  );

  // 检查节点是否已有出边（正式连接）
  const hasOutgoingEdges = useCallback(() => {
    const edges = getEdges();
    const nonTemporaryEdges = filterOutTemporaryEdges(edges);
    return nonTemporaryEdges.some((edge) => edge.source === id);
  }, [id, getEdges]);

  // 处理确定按钮点击
  const handleConfirm = useCallback(() => {
    const currentNode = getNode(id);
    if (!currentNode) return;

    const basePosition = currentNode.position;
    const nodeWidth = currentNode.width || 200;
    const spacing = 600;
    const rightOffset = 300;

    // 创建正式的问题节点（包括预设问题和自定义问题）
    const allQuestions = [...questions, ...customQuestions];
    const permanentNodes = allQuestions.map(({ question }: any, index) => {
      return createNode(
        NODE_TYPE_MAPS.llmOutput,
        {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + index * spacing,
        },
        {
          title: question,
          isTemporary: false, // 确保创建的是正式节点
        }
      );
    });

    // 创建正式连线
    const permanentEdges = permanentNodes.map((node) => ({
      id: nanoid(),
      source: id,
      target: node.id,
      type: "default",
      style: {
        stroke: "#6b7280", // 灰色的正式连线
      },
    }));

    // 原子操作：删除所有临时节点/边，添加正式节点/边
    setNodes((nodes) => [...filterOutTemporaryNodes(nodes), ...permanentNodes]);

    setEdges((edges) => [...filterOutTemporaryEdges(edges), ...permanentEdges]);
    // 执行 updateQuestionLLM
    setTimeout(() => {
      permanentNodes.forEach((edge) => {
        updateQuestionLLM(edge.id, data?.content);
      });
    }, 10);
    // 清空状态
    setCustomQuestions([]);
    setHasTemporaryNodes(false);
  }, [id, questions, customQuestions, getNode, setNodes, setEdges]);

  // 处理用户输入的自定义问题
  const handleAddCustomQuestion = useCallback(
    (questionText: string) => {
      const currentNode = getNode(id);
      if (!currentNode) return;

      const basePosition = currentNode.position;
      const nodeWidth = currentNode.width || 200;
      const spacing = 70;
      const rightOffset = 300;

      // 计算新问题节点的位置
      const newNodePosition = {
        x: basePosition.x + nodeWidth + rightOffset,
        y:
          basePosition.y +
          (questions.length + customQuestions.length) * spacing,
      };

      // 创建新的临时问题节点
      const newQuestionNode = createNode(
        NODE_TYPE_MAPS.questionNode,
        newNodePosition,
        {
          title: questionText,
          isTemporary: true,
        }
      );

      // 创建新的临时连线
      const newEdge = {
        id: createTemporaryEdgeId("custom"),
        source: id,
        target: newQuestionNode.id,
        type: "default",
        style: {
          strokeDasharray: "5,5",
          opacity: 0.6,
          stroke: "#3b82f6",
        },
        data: { isTemporary: true },
      };

      // 添加节点和连线
      addNodes([newQuestionNode]);
      addEdges([newEdge]);

      // 更新状态
      setCustomQuestions((prev) => [
        ...prev,
        { question: questionText, expert: "self" },
      ]);
      setHasTemporaryNodes(true);
    },
    [id, questions.length, customQuestions.length, getNode, addNodes, addEdges]
  );

  // 计算安全区域是否包含鼠标位置
  const isMouseInSafeArea = useCallback(
    (mouseX: number, mouseY: number) => {
      if (!nodeRef.current || !hasTemporaryNodes) return false;

      // 获取源节点的屏幕位置
      const nodeRect = nodeRef.current.getBoundingClientRect();

      // 计算总的节点数量（包括输入框、预设问题和自定义问题）
      const totalNodes = 1 + questions.length + customQuestions.length; // +1 for input node

      // 定义安全区域的边界（扩展的矩形区域）
      const safeAreaLeft = nodeRect.left - 20;
      const safeAreaRight = nodeRect.right + 400; // 扩展到包含临时节点区域
      const safeAreaTop = nodeRect.top - 100; // 扩展到包含输入框节点
      const safeAreaBottom = nodeRect.bottom + totalNodes * 100 + 150; // 扩展到包含所有节点和确定按钮

      // 检查鼠标是否在安全区域内
      return (
        mouseX >= safeAreaLeft &&
        mouseX <= safeAreaRight &&
        mouseY >= safeAreaTop &&
        mouseY <= safeAreaBottom
      );
    },
    [hasTemporaryNodes, questions.length, customQuestions.length]
  );

  // 全局鼠标监听，实现更优雅的鼠标事件处理
  useEffect(() => {
    if (!hasTemporaryNodes) return;

    const handleGlobalMouseMove = (event: MouseEvent) => {
      // 清除之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // 检查鼠标是否在安全区域外
      if (!isMouseInSafeArea(event.clientX, event.clientY)) {
        // 延迟删除临时节点
        timeoutRef.current = setTimeout(() => {
          // 直接删除所有临时节点和边
          const currentNodes = getNodes();
          const currentEdges = getEdges();
          const { nodes: permanentNodes, edges: permanentEdges } =
            removeAllTemporaryElements(currentNodes, currentEdges);

          setNodes(permanentNodes);
          setEdges(permanentEdges);

          // 清空状态
          setCustomQuestions([]);
          setHasTemporaryNodes(false);
          setIsHovering(false);
        }, 200);
      }
    };

    document.addEventListener("mousemove", handleGlobalMouseMove);

    return () => {
      document.removeEventListener("mousemove", handleGlobalMouseMove);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [
    hasTemporaryNodes,
    isMouseInSafeArea,
    getNodes,
    getEdges,
    setNodes,
    setEdges,
  ]);

  const handleMouseEnter = useCallback(() => {
    const currentNode = getNode(id);

    // 检查是否可以创建临时节点：
    // 1. 节点存在
    // 2. 当前没有临时节点
    // 3. 当前节点没有正式的出边
    if (!currentNode || hasTemporaryNodes || hasOutgoingEdges()) {
      return;
    }

    setIsHovering(true);
    const basePosition = currentNode.position;
    const nodeWidth = currentNode.width || 200;
    const spacing = 70;
    const rightOffset = 300;

    // 使用统一位置计算函数
    const positions = calculateAllPositions(
      basePosition,
      nodeWidth,
      spacing,
      rightOffset,
      questions.length,
      customQuestions.length
    );

    // 1. 创建输入框节点
    const inputNode = createNode(
      NODE_TYPE_MAPS.inputNode,
      positions.inputNode,
      {
        title: "输入问题...",
        isTemporary: true,
      }
    );

    // 为输入框节点添加发送回调
    (inputNode.data as any).onSend = handleAddCustomQuestion;

    // 2. 创建临时问题节点
    const tempNodes = questions.map(({ question }: any, index) => {
      return createNode(
        NODE_TYPE_MAPS.questionNode,
        positions.questionNodes[index],
        {
          title: question,
          isTemporary: true,
        }
      );
    });

    // 3. 创建确定按钮节点
    const confirmButton = createNode(
      NODE_TYPE_MAPS.confirmButtonNode,
      positions.confirmButton,
      {
        title: "✓ 确定",
        isTemporary: true,
        onConfirm: handleConfirm,
      }
    );

    // 创建临时连线
    const inputEdge = {
      id: createTemporaryEdgeId("input"),
      source: id,
      target: inputNode.id,
      type: "default",
      style: {
        strokeDasharray: "5,5",
        opacity: 0.6,
        stroke: "#3b82f6",
      },
      data: { isTemporary: true },
    };

    const tempEdges = tempNodes.map((node) => ({
      id: createTemporaryEdgeId("question"),
      source: id,
      target: node.id,
      type: "default",
      style: {
        strokeDasharray: "5,5",
        opacity: 0.6,
        stroke: "#3b82f6",
      },
      data: { isTemporary: true },
    }));

    // 添加所有节点和连线
    addNodes([inputNode, ...tempNodes, confirmButton]);
    addEdges([inputEdge, ...tempEdges]);

    // 更新状态
    setHasTemporaryNodes(true);
  }, [
    id,
    questions,
    hasTemporaryNodes,
    addNodes,
    addEdges,
    getNode,
    handleAddCustomQuestion,
    handleConfirm,
    hasOutgoingEdges,
    calculateAllPositions,
    customQuestions.length,
  ]);

  const handleMouseLeave = useCallback(() => {
    // 只更新悬停状态，删除逻辑由全局鼠标监听处理
    setIsHovering(false);
  }, []);

  return (
    <>
      <div
        ref={nodeRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`transition-all duration-200 w-[400px] ${
          isHovering
            ? "border-blue-400 shadow-md scale-105"
            : "border-gray-200 hover:border-blue-300"
        }`}
      >
        <Content
          id={id}
          data={data}
          selected={selected}
          type={NODE_TYPE_MAPS.askAnyNode}
        />
      </div>
      <Handle type="source" position={Position.Right} />
      <Handle type="target" position={Position.Left} />
    </>
  );
};
