import NodeTitle from "./title";
import { useState } from "react";
import { Edit } from "./edit";
import { Preview } from "./preview";

// 自定义滚动条样式
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }
`;

// 注入样式
if (typeof document !== "undefined") {
  const style = document.createElement("style");
  style.textContent = scrollbarStyles;
  document.head.appendChild(style);
}

type MarkdownNodeProps = {
  type: string;
  id: string;
  data?: {
    [key: string]: any;
  };
  selected: boolean;
};

export const Content = ({
  type,
  id,
  data,
  selected,
}: MarkdownNodeProps) => {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <>
      <div className={`bg-white w-full rounded-lg flex flex-col relative`}>
        <div className="flex-shrink-0">
          <NodeTitle
            id={id}
            title={data?.title}
            color={data?.color || "#dbeafe"}
          />
        </div>
        <div className="flex-1 min-h-[100px] max-h-[500px] overflow-y-auto w-full custom-scrollbar">
          {isEditing ? (
            <Edit
              id={id}
              content={data?.content}
              selected={selected}
              onBlur={() => setIsEditing(false)}
            />
          ) : (
            <Preview
              content={data?.content}
              onEdit={() => setIsEditing(true)}
            />
          )}
        </div>
      </div>
    </>
  );
};
