import { useEffect, useState } from "react";
import { useLLMOutput } from "../../use-llm-output";

const useQuestion = (content: string) => {
  const [questions, setQuestions] = useState<string[]>([]);
  const { generateLLMOutput } = useLLMOutput();
  useEffect(() => {
    if (!content) return;
    const systemPrompt = `
    针对内容请5位相关的专家，提出具有真知灼见的问题。
    使用可以通过JSON.parse解析的json的格式返回，示例：
  [{
    question:string, // 问题  
    expert": "string"    // 对应的专家，例如负责回答该问题的领域专家

  }]
    `;
    generateLLMOutput([content], {
      model: "gpt-4o-mini",
      temperature: 0.5,
      systemPrompt: systemPrompt,
    }).then((res: any) => {
      console.log(res);
      const content = res.content;

      // 提取 JSON 部分（去除 ```json ... ```）
      const match = content.match(/```json\s*([\s\S]*?)\s*```/);

      if (!match) {
        console.error("未找到 JSON 代码块");
        return;
      }

      const jsonString = match[1];

      try {
        const data = JSON.parse(jsonString);
        console.log("解析后的 JS 对象：", data);
        setQuestions(data);
        return data;
      } catch (e) {
        console.error("JSON 解析失败:", e);
      }
    });
  }, [content]);
  return { questions };
};

export default useQuestion;
