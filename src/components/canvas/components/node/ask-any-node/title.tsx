import { nodeData } from "@/store/flow-store.ts";
import { useReactFlow } from "@xyflow/react";
import { Input, InputRef } from "antd";
import { useEffect, useRef, useState } from "react";

type NodeTitleProps = {
  id: string;
  title: string;
  color: string;
};

const NodeTitle = ({ title, id, color }: NodeTitleProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const { updateNodeData } = useReactFlow();
  const inputRef = useRef<InputRef>(null);

  const handleDoubleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleFinishEditing = () => {
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === 'Escape') {
      e.preventDefault();
      handleFinishEditing();
      inputRef.current?.blur();
    }
  };

  // 组件卸载时确保退出编辑状态
  useEffect(() => {
    return () => {
      if (isEditing) {
        setIsEditing(false);
      }
    };
  }, [isEditing]);

  if (isEditing) {
    return (
      <Input
        ref={inputRef}
        className="h-[30px] px-4"
        value={title}
        placeholder="请输入标题"
        autoFocus
        onBlur={handleFinishEditing}
        onKeyDown={handleKeyDown}
        onChange={(e) => {
          updateNodeData(id, {
            title: e.target.value,
          });
        }}
      />
    );
  }
  return (
    <div
      className="h-[30px] px-4 flex items-center overflow-hidden"
      style={{ backgroundColor: color }}
      onDoubleClick={handleDoubleClick}
    >
      <span className="truncate">{title}</span>
    </div>
  );
};

export default NodeTitle;
