import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { type XYPosition, useReactFlow } from "@xyflow/react";
import { nanoid } from "nanoid";
import { useEffect, useRef } from "react";
import { NodeLayout } from "./layout";
import { TextIcon, BrainCircuit } from "lucide-react";
import { createNode } from "@/components/canvas/lib/node-factory";
import { NODE_TYPE_MAPS, NodeType } from "@/components/canvas/lib/node-types";



export const nodeButtons = [
  {
    id: NODE_TYPE_MAPS.markdown,
    label: "Text",
    icon: TextIcon,
  },
  {
    id: NODE_TYPE_MAPS.llm,
    label: "LLM",
    icon: BrainCircuit,
  },
  {
    id: NODE_TYPE_MAPS.llmOutput,
    label: "LLM Output",
    icon: BrainCircuit,
  },
  {
    id: NODE_TYPE_MAPS.groupNode,
    label: "Group",
    icon: BrainCircuit,
  },
  // {
  //   id: NODE_TYPE_MAPS.questionNode,
  //   label: "Question",
  //   icon: BrainCircuit,
  // },
  {
    id: NODE_TYPE_MAPS.askAnyNode,
    label: "Ask Any",
    icon: BrainCircuit,
  },
];

type DropNodeProps = {
  data: {
    isSource?: boolean;
    position: XYPosition;
  };
  id: string;
};

export const DropNode = ({ data, id }: DropNodeProps) => {
  const { addNodes, deleteElements, getNode, addEdges, getNodeConnections } =
    useReactFlow();
  const ref = useRef<HTMLDivElement>(null);

  const handleSelect = (type: NodeType, options?: Record<string, unknown>) => {
    // Get the position of the current node
    const currentNode = getNode(id);
    const position = currentNode?.position || { x: 0, y: 0 };
    const sourceNodes = getNodeConnections({
      nodeId: id,
    });

    // Delete the drop node
    deleteElements({
      nodes: [{ id }],
    });

    const newNodeId = nanoid();
    const { data: nodeData, ...rest } = options ?? {};

    const newNode = createNode(type, position);
    addNodes([newNode]);

    for (const sourceNode of sourceNodes) {
      addEdges({
        id: nanoid(),
        source: data.isSource ? newNodeId : sourceNode.source,
        target: data.isSource ? sourceNode.source : newNodeId,
        type: "animated",
      });
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        // Delete the drop node when Escape is pressed
        deleteElements({
          nodes: [{ id }],
        });
      }
    };

    const handleClick = (event: MouseEvent) => {
      // Get the DOM element for this node
      const nodeElement = ref.current;

      // Check if the click was outside the node
      if (nodeElement && !nodeElement.contains(event.target as Node)) {
        deleteElements({
          nodes: [{ id }],
        });
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    setTimeout(() => {
      window.addEventListener("click", handleClick);
    }, 50);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("click", handleClick);
    };
  }, [deleteElements, id]);

  return (
    <div ref={ref}>
      <NodeLayout id={id} data={data} type="drop" title="Add a new node">
        <Command className="rounded-lg">
          <CommandInput placeholder="search..." />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup heading="添加节点">
              {nodeButtons.map((button) => (
                <CommandItem
                  key={button.id}
                  onSelect={() => handleSelect(button.id)}
                >
                  <button.icon size={16} />
                  {button.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </NodeLayout>
    </div>
  );
};
