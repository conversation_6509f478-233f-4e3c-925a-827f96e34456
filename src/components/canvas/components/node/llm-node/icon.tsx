// 机器人
export const LLMNodeIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="23"
      height="23"
      viewBox="0 0 23 23"
      fill="none"
    >
      <path
        d="M15.3337 7.66699C15.3337 9.78409 13.6174 11.5003 11.5003 11.5003C9.38322 11.5003 7.66699 9.78409 7.66699 7.66699"
        stroke="#6E6BEE"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.5003 3.83301C9.38322 3.83301 7.66699 5.54924 7.66699 7.66634H15.3337C15.3337 5.54924 13.6174 3.83301 11.5003 3.83301Z"
        fill="#6E6BEE"
        stroke="#6E6BEE"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.75 7.66699H17.25"
        stroke="#6E6BEE"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.5 1.91699V3.83366"
        stroke="#6E6BEE"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.5003 12.9375C7.00153 12.9375 3.35449 16.37 3.35449 20.6042H19.6462C19.6462 16.37 15.9991 12.9375 11.5003 12.9375Z"
        fill="#6E6BEE"
        stroke="#6E6BEE"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8 16V17.9167"
        stroke="white"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.375 16.292V18.2087"
        stroke="white"
        stroke-width="0.9375"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

// play

export const PlayIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
    >
      <g clip-path="url(#clip0_2288_1035)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M10.7534 6.31704C11.0861 6.50961 11.0861 6.98986 10.7534 7.18246L2.75139 11.8152C2.41804 12.0082 2.00086 11.7677 2.00086 11.3825V2.11696C2.00086 1.73179 2.41804 1.49126 2.75139 1.68425L10.7534 6.31704Z"
          fill="#333333"
          stroke="#333333"
          stroke-width="0.9375"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2288_1035">
          <rect
            width="12"
            height="12"
            fill="white"
            transform="matrix(0 1 -1 0 12.501 0.75)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

// add icon
export const AddIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        d="M10.7974 4.79744C10.9595 4.79744 11.1151 4.82942 11.2644 4.89339C11.4136 4.95736 11.5416 5.04264 11.6482 5.14925C11.7548 5.25586 11.8401 5.3838 11.9041 5.53305C11.968 5.6823 12 5.83795 12 6C12 6.17058 11.968 6.32836 11.9041 6.47335C11.8401 6.61834 11.7548 6.74414 11.6482 6.85075C11.5416 6.95736 11.4136 7.04264 11.2644 7.10661C11.1151 7.17058 10.9595 7.20256 10.7974 7.20256H7.20256V10.7974C7.20256 10.968 7.17058 11.1258 7.10661 11.2708C7.04264 11.4158 6.95736 11.5416 6.85075 11.6482C6.74414 11.7548 6.61621 11.8401 6.46695 11.9041C6.3177 11.968 6.16205 12 6 12C5.82942 12 5.67164 11.968 5.52665 11.9041C5.38166 11.8401 5.25586 11.7548 5.14925 11.6482C5.04264 11.5416 4.95736 11.4158 4.89339 11.2708C4.82942 11.1258 4.79744 10.968 4.79744 10.7974V7.20256H1.20256C1.03198 7.20256 0.8742 7.17058 0.729211 7.10661C0.584222 7.04264 0.458422 6.95736 0.351812 6.85075C0.245203 6.74414 0.159915 6.61834 0.0959488 6.47335C0.0319829 6.32836 0 6.17058 0 6C0 5.83795 0.0319829 5.6823 0.0959488 5.53305C0.159915 5.3838 0.245203 5.25586 0.351812 5.14925C0.458422 5.04264 0.584222 4.95736 0.729211 4.89339C0.8742 4.82942 1.03198 4.79744 1.20256 4.79744H4.79744V1.20256C4.79744 1.04051 4.82942 0.884862 4.89339 0.735608C4.95736 0.586354 5.04264 0.458422 5.14925 0.351812C5.25586 0.245203 5.38166 0.159915 5.52665 0.0959488C5.67164 0.0319829 5.82942 0 6 0C6.33262 0 6.61621 0.117271 6.85075 0.351812C7.08529 0.586354 7.20256 0.869936 7.20256 1.20256V4.79744H10.7974Z"
        fill="#6E6BEE"
      />
    </svg>
  );
};

// 垃圾桶
export const TrashIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M2.66699 3.66699H13.3337"
        stroke="#6E6BEE"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6 1.66699H10"
        stroke="#6E6BEE"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 5.66699H12V13.3337C12 13.886 11.5523 14.3337 11 14.3337H5C4.4477 14.3337 4 13.886 4 13.3337V5.66699Z"
        stroke="#6E6BEE"
        stroke-linejoin="round"
      />
    </svg>
  );
};

// 资源 ICON
type ResourceType = "pdf" | "markdown" | "auto";
export const ResourceIcon = ({ type }: { type: ResourceType }) => {
  switch (type) {
    case "pdf":
      return (
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <rect
              x="1"
              y="1"
              width="10"
              height="10"
              rx="2"
              fill="#F5B0B5"
              stroke="#E08B90"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M5.53475 2.43477C4.88546 2.5156 4.63796 3.32649 4.99332 4.28965L4.99296 4.28964C5.06964 4.4974 5.16665 4.71505 5.27994 4.93643C4.95457 5.65897 4.52021 6.59408 4.16783 7.33622C3.86183 7.46005 3.54267 7.59892 3.21114 7.75032C2.48935 8.08002 2.2247 8.77174 2.59613 9.26911C2.96649 9.76422 3.654 9.64166 4.09329 8.96498C4.15629 8.86803 4.36356 8.45119 4.62962 7.89611C5.01286 7.7479 5.37063 7.62727 5.69847 7.54019C6.13092 7.42693 6.56538 7.32246 7.0016 7.22685C7.50928 7.68859 8.02792 7.99185 8.46513 7.99185C9.15549 7.99185 9.61442 7.63471 9.56799 7.09073C9.52727 6.61179 9.08334 6.25277 8.54655 6.25277L8.54656 6.25277C8.35832 6.25277 7.82787 6.34277 7.19481 6.47504C6.76666 6.02949 6.3389 5.45653 6.01378 4.89701C6.21752 4.43319 6.36029 4.08806 6.3844 3.98401C6.59191 3.07912 6.17904 2.35469 5.53475 2.43477ZM5.68382 5.63519C5.80364 5.37075 5.91576 5.12014 6.01378 4.89701C5.86088 4.63389 5.73069 4.37373 5.63383 4.1312L5.63383 4.13118L5.63383 4.13119L5.63383 4.1312C5.54901 4.333 5.42536 4.61348 5.27994 4.93643C5.39827 5.16768 5.53437 5.40301 5.68362 5.6354C5.48372 6.07689 5.26206 6.5563 5.04896 7.01132L5.04875 7.01139L5.04875 7.01178L5.04896 7.01132C5.21889 6.95571 5.38382 6.90649 5.54304 6.8644C5.82875 6.78846 6.1334 6.71327 6.4359 6.6426C6.16272 6.32609 5.91138 5.98943 5.68382 5.63519ZM5.68382 5.63519L5.68368 5.6355L5.68362 5.6354L5.68375 5.63509L5.68382 5.63519ZM7.19481 6.47504C6.95227 6.52572 6.69466 6.5826 6.43547 6.64299C6.61928 6.85505 6.80966 7.05227 7.0016 7.22685C7.28543 7.16464 7.57001 7.10617 7.85527 7.05147C7.64491 6.90814 7.41981 6.70918 7.19481 6.47504ZM4.62962 7.89611C4.33776 8.00897 4.03112 8.13783 3.71167 8.28004C3.8237 8.05551 3.98469 7.72193 4.16783 7.33622C4.47497 7.21192 4.76885 7.10275 5.04866 7.01123C4.90164 7.3249 4.75871 7.62678 4.62962 7.89611ZM3.10197 8.8337C3.03197 8.7401 3.10197 8.55701 3.46483 8.3916L3.71162 8.27995C3.63697 8.42995 3.58411 8.53145 3.5609 8.56679C3.34768 8.89498 3.17161 8.92619 3.10197 8.8337ZM8.46525 7.29524C8.67489 7.29524 8.81704 7.25577 8.89025 7.199C8.91882 7.17645 8.92347 7.1663 8.9224 7.15389C8.9149 7.06479 8.77168 6.94938 8.54739 6.94938C8.44811 6.94938 8.19382 6.9881 7.85596 7.05164C8.08239 7.20577 8.29168 7.29524 8.46561 7.29524H8.46525ZM5.73103 3.21425C5.7991 3.3338 5.81799 3.53831 5.75384 3.81763C5.74422 3.86048 5.70145 3.97101 5.63373 4.13116C5.62055 4.09845 5.60807 4.06575 5.59631 4.03342C5.47086 3.69282 5.4484 3.43004 5.49937 3.26312C5.52753 3.17064 5.56887 3.13192 5.6095 3.12665C5.64763 3.12214 5.6929 3.14695 5.73103 3.21425Z"
              fill="#F9FAFC"
            />
          </svg>
        </span>
      );
    case "markdown":
      return (
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <rect
              x="1"
              y="1"
              width="10"
              height="10"
              rx="2"
              fill="#CAABEB"
              stroke="#A86FDA"
            />
            <path
              d="M4.33301 7.66699H7.66634"
              stroke="white"
              stroke-width="0.75"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M4.33301 9.33301H5.99967"
              stroke="white"
              stroke-width="0.75"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M7.66634 3.5H4.33301V6H7.66634V3.5Z"
              fill="#CAABEB"
              stroke="white"
              stroke-width="0.75"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      );
    case "auto":
      return (
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <rect
              x="1"
              y="1"
              width="10"
              height="10"
              rx="2"
              fill="#F0F0FA"
              stroke="#6E6BEE"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M4.49051 6.68159C4.59748 6.68159 4.68419 6.58452 4.68419 6.46477V6.25286H6.37043V6.55909C6.37043 6.89718 6.58949 7.17156 6.85956 7.17156H8.57205C8.84233 7.17156 9.06118 6.89718 9.06118 6.55909V5.33415C9.06118 4.99607 8.84211 4.72168 8.57205 4.72168H6.85977C6.58949 4.72168 6.37064 4.99607 6.37064 5.33415V5.64039H4.68419V5.42847C4.68419 5.30867 4.59753 5.21166 4.49051 5.21166H3.56478C3.45776 5.21166 3.37109 5.30867 3.37109 5.42847V6.46477C3.37109 6.58452 3.45781 6.68159 3.56478 6.68159H4.49051ZM6.85967 5.11393H8.57195L8.5888 5.11589C8.65074 5.12912 8.71092 5.21438 8.71092 5.33442V6.55936L8.70939 6.59121C8.69889 6.70464 8.63367 6.77985 8.57195 6.77985H6.85967L6.84282 6.77789C6.78067 6.76466 6.7207 6.67941 6.7207 6.55936V5.33442L6.72202 5.30257C6.73274 5.18914 6.79774 5.11393 6.85967 5.11393Z"
              fill="#6E6BEE"
            />
          </svg>
        </span>
      );
    default:
      return <div>未知</div>;
  }
};
