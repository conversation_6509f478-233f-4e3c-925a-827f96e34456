import { useForm, useFieldArray } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  CircleHelp,
  TrashIcon,
  X,
  ChevronRight,
  ChevronDown,
} from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  TooltipTrigger,
  Tooltip,
  TooltipProvider,
  TooltipContent,
} from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { Info } from "lucide-react";
import { AddIcon, LLMNodeIcon, PlayIcon, ResourceIcon } from "./icon";
import doubaoUrl from "./assets/doubao.png";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import React from "react";
import { find } from "lodash-es";

// 定义参数值枚举
const NameTypeEnum = z.enum(["manual", "auto"]);

/**
 * LLM节点配置表单的Zod验证schema
 * 定义了表单的所有字段及其验证规则
 */
const formSchema = z.object({
  /** 节点标题，必填字段 */
  title: z.string().min(1, "节点标题不能为空"),
  /** 输入参数字段数组，至少需要一个参数 */
  inputFields: z.array(
    z.object({
      name: z.string().min(1, "参数名不能为空"),
      type: NameTypeEnum, // 不需要渲染
      nodeId: z.string().optional(),
    })
  ),
  /** 选择的LLM模型，必填字段 */
  model: z.string().min(1, "模型不能为空"),
  /** 系统提示词，必填字段 */
  systemPrompt: z.string().min(1, "系统提示不能为空"),
  /** 用户提示词，必填字段 */
  userPrompt: z.string().min(1, "用户提示不能为空"),
  /** 输出字段数组，至少需要一个输出字段 */
  outputFields: z.array(
    z.object({
      name: z.string().min(1, "输出字段名不能为空"),
      aliasName: z.string().optional(),
    })
  ),
});

type FormValues = z.infer<typeof formSchema>;

/**
 * LLM节点配置表单组件
 * 功能：
 * - 配置LLM节点的标题、输入参数、模型、提示词和输出字段
 * - 支持动态添加和删除输入参数和输出字段
 * - 实时验证表单数据
 * - 提供友好的用户界面和错误提示
 */
export function LLMNodeConfigForm({
  initialData,
  sourceData,
  saveConfig,
}: {
  initialData?: FormValues;
  sourceData?: any;
  saveConfig: (data: any) => void;
}) {
  console.log("initialData1", { initialData, sourceData });
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      title: initialData?.title || "",
      inputFields: initialData?.inputFields || [
        { name: "", type: "manual" as const, nodeId: "" },
      ],
      model: initialData?.model || "doubao-1.5-pro",
      systemPrompt: initialData?.systemPrompt || "",
      userPrompt: initialData?.userPrompt || "",
      outputFields: initialData?.outputFields || [{ name: "", aliasName: "" }],
    },
  });

  // 管理输入字段数组
  const {
    fields: inputFields,
    append: appendInput,
    remove: removeInput,
  } = useFieldArray({
    control: form.control,
    name: "inputFields",
  });

  // 管理输出字段数组
  const {
    fields: outputFields,
    append: appendOutput,
    remove: removeOutput,
  } = useFieldArray({
    control: form.control,
    name: "outputFields",
  });

  /**
   * 表单提交处理函数
   * @param {FormValues} data - 表单数据，包含所有配置信息
   */
  const onSubmit = (data: FormValues) => {
    console.log("提交数据：", data);
    // 确保所有字段都被包含，包括 disabled 的字段
    const formData = {
      ...data,
      inputFields: data.inputFields.map((field) => {
        const currentNodes = find(sourceData, { id: field.nodeId });
        return {
          ...field,
          name: field.name,
          type: field.type,
          nodeId: field.nodeId || "",
          nodeContent: currentNodes?.nodeContent || "",
          sourceType: currentNodes?.sourceType || "",
        };
      }),
    };
    console.log("处理后的表单数据：", { formData, sourceData });
    // 这里可以调用实际的保存函数
    saveConfig(formData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* 节点标题 */}
        <div className="flex flex-row gap-2 items-center justify-center bg-[#F0F0FA] h-11 px-4">
          <LLMNodeIcon />
          <div className="flex-1">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormControl className="w-full">
                    <Input
                      className="w-full border-none shadow-none"
                      placeholder="请输入节点标题"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex flex-row gap-2 items-center">
            <div className="p-1 cursor-pointer hover:text-primary">
              <PlayIcon />
            </div>
            <div className="p-1 cursor-pointer hover:text-primary">
              <X className="w-4 h-4" />
            </div>
          </div>
        </div>

        <div className="p-[10px]">
          {/* 输入参数配置 */}
          <CollapseCard title="输入">
            <ConfigCard>
              <ContentCard>
                <div className="flex flex-row gap-3">
                  <div className="w-[91px]">参数名</div>
                  <div className="flex flex-row gap-2 w-[50px]"></div>
                  <div>参数值</div>
                </div>
                <div className="flex flex-col gap-2 w-full mt-3">
                  {inputFields.map((field, index) => (
                    <div
                      key={field.id}
                      className="flex flex-row w-full items-center"
                    >
                      <FormField
                        control={form.control}
                        name={`inputFields.${index}.name`}
                        render={({ field }) => (
                          <FormItem className="w-[91px]  mr-4">
                            <FormControl>
                              <Input
                                readOnly={inputFields[index].type === "auto"}
                                className="bg-white"
                                placeholder="参数名"
                                {...field}
                              />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                      {field.type === "auto" && (
                        <div className="flex flex-row gap-1 items-center justify-between w-[60px] h-8">
                          <span className="text-sm">引用</span>
                          <DataTypeTooltip
                            title="引用"
                            content="所有连线过来的内容"
                          />
                        </div>
                      )}

                      {field.type === "manual" && (
                        <div className="flex flex-row gap-1 items-center justify-between w-[60px] h-8">
                          <span className="text-sm text-nowrap">数据集</span>
                          <DataTypeTooltip
                            title="数据集"
                            content="当前project里的所有资源"
                          />
                        </div>
                      )}

                      {/* 参数值下拉 */}
                      <FormField
                        control={form.control}
                        name={`inputFields.${index}.nodeId`}
                        render={({ field }) => (
                          <FormItem className="flex-1 ml-2">
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={inputFields[index].type === "auto"}
                              >
                                <SelectTrigger className="bg-white w-[150px]">
                                  <SelectValue placeholder="请选择" />
                                </SelectTrigger>
                                <SelectContent>
                                  {sourceData.map((item: any) => (
                                    <SelectItem key={item.id} value={item.id}>
                                      <span className="flex flex-row items-center gap-2">
                                        <ResourceIcon type={item.sourceType} />
                                        {item.name || "未知"}
                                      </span>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="px-2"
                        onClick={() => removeInput(index)}
                      >
                        <TrashIcon />
                      </Button>
                    </div>
                  ))}
                </div>
              </ContentCard>
              <Button
                className="w-full h-[30px] bg-[#F0F0FA] border-none"
                type="button"
                variant="outline"
                size="sm"
                onClick={() =>
                  appendInput({
                    name: "",
                    type: "manual",
                    nodeId: undefined,
                  })
                }
              >
                <AddIcon />
                &nbsp;&nbsp;添加输入字段
              </Button>
            </ConfigCard>
          </CollapseCard>
          {/* 模型选择 */}
          <CollapseCard title="Prompt工程">
            <ConfigCard>
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center gap-2">
                    <FormLabel className="text-nowrap mt-2">选择模型</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled
                      >
                        <SelectTrigger className="mt-0">
                          <SelectValue
                            className="mt-0"
                            placeholder="请选择模型"
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="doubao-1.5-pro">
                            <div className="flex flex-row items-center gap-2">
                              <img
                                src={doubaoUrl}
                                alt="doubao"
                                className="w-4 h-4"
                              />
                              豆包1.5pro
                            </div>
                          </SelectItem>
                          <SelectItem value="gpt-3.5-turbo">
                            GPT-3.5 Turbo
                          </SelectItem>
                          <SelectItem value="claude-3">Claude-3</SelectItem>
                          <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    {/* <FormMessage /> */}
                  </FormItem>
                )}
              />

              {/* 系统提示词 */}
              <FormField
                control={form.control}
                name="systemPrompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="pl-2">System Prompt</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入系统提示词..."
                        className="min-h-[66px] border-none bg-[#F0F0FA]"
                        {...field}
                      />
                    </FormControl>
                    {/* <FormMessage /> */}
                  </FormItem>
                )}
              />

              {/* 用户提示词 */}
              <FormField
                control={form.control}
                name="userPrompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="pl-2">User Prompt</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入用户提示词..."
                        className="min-h-[66px] border-none bg-[#F0F0FA]"
                        {...field}
                      />
                    </FormControl>
                    {/* <FormMessage /> */}
                  </FormItem>
                )}
              />
            </ConfigCard>
          </CollapseCard>
          {/* 输出字段配置 */}
          <CollapseCard title="输出">
            <ConfigCard>
              <ContentCard>
                <div className="flex flex-row">
                  <div className="w-[91px] mr-4">参数名</div>
                  <div>别名</div>
                </div>
                <div className="flex flex-col gap-2 w-full mt-3">
                  {outputFields.map((field, index) => (
                    <div
                      key={field.id}
                      className="flex flex-row w-full items-center"
                    >
                      <FormField
                        control={form.control}
                        name={`outputFields.${index}.name`}
                        render={({ field }) => (
                          <FormItem className="w-[91px] mr-4">
                            <FormControl>
                              <Input
                                className="bg-white text-center"
                                placeholder="输出字段名"
                                {...field}
                              />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`outputFields.${index}.aliasName`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input
                                className="bg-white text-center"
                                placeholder="别名（可选）"
                                {...field}
                              />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="px-2"
                        onClick={() => removeOutput(index)}
                      >
                        <TrashIcon />
                      </Button>
                    </div>
                  ))}
                </div>
              </ContentCard>
              <Button
                className="w-full h-[30px] bg-[#F0F0FA] border-none"
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendOutput({ name: "", aliasName: "" })}
              >
                <AddIcon />
                &nbsp;&nbsp;添加输出字段
              </Button>
            </ConfigCard>
          </CollapseCard>
          {/* 提交按钮 */}
          <Button type="submit" className="w-full mt-6">
            保存配置
          </Button>
        </div>
      </form>
    </Form>
  );
}

/**
 * 数据类型提示组件
 * @param {string} title - 提示标题
 * @param {string} content - 提示内容
 */
function DataTypeTooltip({
  title,
  content,
}: {
  title: string;
  content: string;
}) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <div className="w-4 h-8 flex items-center justify-center cursor-pointer hover:text-primary">
            <CircleHelp />
          </div>
        </TooltipTrigger>
        <TooltipContent
          style={{
            zIndex: 1002,
          }}
        >
          <p className="text-xs">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

const ConfigCard = ({ children }: { children: React.ReactNode }) => {
  return (
    <div
      style={{
        boxShadow: "0 0 4px 0 rgba(61, 86, 186, 0.20)",
      }}
      className="space-y-2 bg-white rounded-[16px] p-3"
    >
      {children}
    </div>
  );
};

const ContentCard = ({ children }: { children: React.ReactNode }) => {
  return <div className="bg-[#F0F0FA] rounded-[10px] p-3">{children}</div>;
};

// 折叠卡片
const CollapseCard = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  const [isOpen, setIsOpen] = React.useState(true);

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button className="w-fit flex py-1 pl-1 pr-8 items-center font-normal justify-between my-3 ml-3">
          {isOpen ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
          <span>{title}</span>
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent>{children}</CollapsibleContent>
    </Collapsible>
  );
};
