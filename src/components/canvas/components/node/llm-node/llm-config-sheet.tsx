import { z } from "zod";
import { Sheet, Sheet<PERSON>ontent, SheetTitle, SheetDescription } from "@/components/ui/sheet";

import { AINodeData } from "@/components/canvas/lib/node-factory";
import { cn } from "@/lib/utils";
import { LLMNodeConfigForm } from "./config-form";
import { useEffect, useRef, useState, useCallback } from "react";
import { workspaceService } from "@/local/services/workspace-service";
import { getWsId } from "@/tools/params";
import { canvasService } from "@/local";
import type { Node, Edge } from "@xyflow/react";
import { useReactFlow } from "@xyflow/react";

export type LLMConfigData = AINodeData;

// Zod Schema 定义
const inputFieldSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "参数名不能为空"),
  nodeTitle: z.string().optional(),
  sourceType: z.string().optional(),
  nodeId: z.string().optional(),
  nodeContent: z.string().optional(),
  type: z.enum(["manual", "auto"]),
});

const outputFieldSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "输出名不能为空"),
  aliasName: z.string().optional(),
});

const llmConfigSchema = z.object({
  title: z.string().min(1, "节点标题不能为空"),
  userPrompt: z.string(),
  systemPrompt: z.string(),
  inputFields: z.array(inputFieldSchema).min(1, "至少需要一个输入字段"),
  outputFields: z.array(outputFieldSchema).min(1, "至少需要一个输出字段"),
});

// 常量定义
const COMPONENT_STYLES = {
  sheet: "!w-[450px] min-w-[450px] sm:w-[450px] flex flex-col",
  input: "h-8 text-xs",
  button: "text-red-500 hover:text-red-700 h-8 w-8 p-0",
  select: "h-8 text-xs",
  textarea: "min-h-[60px] text-xs resize-none",
  selectTrigger: {
    source: "h-8 text-xs w-24",
    data: "h-8 text-xs w-32",
  },
} as const;

// 获取所有指向目标节点的上游节点
const getUpstreamNodes = (targetNodeId: string, nodes: Node[], edges: Edge[]) => {
  const upstreamNodes = new Set<string>();
  const visited = new Set<string>();
  
  // 递归查找所有上游节点
  const findUpstream = (nodeId: string) => {
    if (visited.has(nodeId)) return;
    visited.add(nodeId);
    
    // 找到所有指向当前节点的边
    const incomingEdges = edges.filter(edge => edge.target === nodeId);
    
    incomingEdges.forEach(edge => {
      const sourceNodeId = edge.source;
      if (sourceNodeId !== targetNodeId) { // 避免包含目标节点自己
        upstreamNodes.add(sourceNodeId);
        // 递归查找更上游的节点
        findUpstream(sourceNodeId);
      }
    });
  };
  
  // 从目标节点开始查找
  findUpstream(targetNodeId);
  
  // 返回找到的所有上游节点
  return nodes.filter(node => upstreamNodes.has(node.id));
};

type LLMConfigSheetProps = {
  open: boolean;
  onClose: () => void;
  onSave: (data: LLMConfigData) => void;
  initialData?: Partial<LLMConfigData>;
  zIndex?: number;
};

export const LLMConfigSheet = ({
  open,
  onClose,
  onSave,
  initialData,
  zIndex = 50,
}: LLMConfigSheetProps) => {
  const { getNodes, getEdges } = useReactFlow();
  const [sourceData, setSourceData] = useState<any>(null);
  const [availableNodeVariables, setAvailableNodeVariables] = useState<any[]>([]);

  const data = {
    ...initialData,
    title: initialData?.title || "",
    inputFields: initialData?.inputFields || [],
    model: "doubao-1.5-pro",
  };
  // 获取数据

  // 获取连线到当前LLM节点的前置节点输出变量
  const getAvailableNodeVariables = () => {
    if (!initialData?.id) return [];

    const edges = getEdges();
    const nodes = getNodes();

    // 找到所有连接到当前节点的边
    const incomingEdges = edges.filter(edge => edge.target === initialData.id);
    
    // 找到这些边的源节点
    const sourceNodeIds = incomingEdges.map(edge => edge.source);
    
    // 获取这些源节点中的 LLM 节点
    const llmNodes = nodes.filter(node => 
      sourceNodeIds.includes(node.id) && node.type === 'llm'
    );

    // 提取这些 LLM 节点的输出字段
    const variables: { value: string; label: string; nodeTitle: string; disabled?: boolean }[] = [];
    
    llmNodes.forEach(node => {
      const nodeTitle = node.data?.title || '未命名节点';
      const outputFields = node.data?.outputFields || [];
      
      if (Array.isArray(outputFields)) {
        outputFields.forEach((field: any) => {
          if (field && typeof field === 'object' && field.name) {
            variables.push({
              value: `${node.id}.${field.name}`, // 使用节点ID.字段名作为唯一标识
              label: `${field.aliasName || field.name}`, // 显示别名或字段名
              nodeTitle: typeof nodeTitle === 'string' ? nodeTitle : '未命名节点',
              disabled: false // 初始状态都是可用的
            });
          }
        });
      }
    });

    return variables;
  };

  const getData = useCallback(async () => {
    // 获取前置节点变量
    const nodeVariables = getAvailableNodeVariables();
    setAvailableNodeVariables(nodeVariables);
    
    const wid = getWsId();
    const res = await workspaceService.getFilesContentByWid(wid) as any[] || []
    
    // 获取画布数据
    const canvases = await canvasService.getByWorkspace(wid);
    let nodeData: Node[] = [];
    
    if (canvases && canvases.length > 0) {
      // 解析第一个画布的内容
      const canvasContent = JSON.parse(canvases[0].content || '{}');
      nodeData = canvasContent.nodes || [];
    }
    
    // 开发环境下的调试信息 - 减少重复日志
    if (import.meta.env.DEV && !sourceData) {
      console.log("LLM配置数据获取:", { 
        files: res.length, 
        nodes: nodeData.length, 
        variables: nodeVariables.length 
      });
    }
    
    setSourceData([
      // 文件（根据 mime_type 或文件名区分类型）
      ...res.map((item: any) => {
        // 根据文件信息判断具体类型
        let sourceType = "pdf"; // 默认为 PDF
        
        // 检查 mime_type 字段和文件扩展名
        if (item.mime_type === 'notebook') {
          sourceType = "lexical";
        } else if (item.mime_type === 'application/pdf' || 
                  (item.filename && item.filename.toLowerCase().endsWith('.pdf'))) {
          sourceType = "pdf";
        } else if (item.filename) {
          // 根据文件扩展名判断其他类型
          const filename = item.filename.toLowerCase();
          if (filename.endsWith('.md') || filename.endsWith('.txt')) {
            sourceType = "lexical"; // 将文本文件也归类为 lexical
          } else if (filename.endsWith('.json') || filename.endsWith('.lexical')) {
            sourceType = "lexical";
          }
        }
        
        return {
          id: item.aid,
          name: item.title || item.filename,
          sourceType: sourceType,
          nodeId: item.aid,
          nodeContent: item.content,
          type: "manual",
        }
      }),
      // 所有画布节点（用于数据集模式）
      ...nodeData.map((item: any) => {
        return {
          id: item.id,
          name: item?.data?.title,
          sourceType: "markdown",
          nodeId: item.id,
          nodeContent: item?.data?.content,
          type: "manual",
        }
      })
    ]);
  }, [getAvailableNodeVariables, sourceData]);
  
  // 添加loading状态防止重复请求
  const [isLoading, setIsLoading] = useState(false);
  
  // 优化后的数据获取逻辑
  const fetchData = useCallback(async () => {
    if (isLoading) return; // 防止重复调用
    
    setIsLoading(true);
    try {
      await getData();
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, getData]);

  // 只在需要时获取数据
  useEffect(() => {
    if (open && !sourceData && !isLoading) {
      fetchData();
    }
  }, [open, sourceData, isLoading, fetchData])

  return (
    <div onClick={(e) => e?.stopPropagation()}>
      <Sheet open={open} onOpenChange={(open) => !open && onClose()} >
        <SheetContent
          side="right"
          className={cn(
            COMPONENT_STYLES.sheet,
            "!rounded-2xl shadow-md p-[0] overflow-y-auto"
          )}
          style={{
            zIndex,
          }}
          showCloseButton={false}
        >
          <SheetTitle className="sr-only">LLM节点配置</SheetTitle>
          <SheetDescription className="sr-only">
            配置LLM节点的输入输出参数、提示词和模型设置
          </SheetDescription>
          <LLMNodeConfigForm
            saveConfig={data => {
              onSave(data)
              // onClose()
            }}
            initialData={data as any}
            sourceData={sourceData}
            availableNodeVariables={availableNodeVariables}
            nodeId={initialData?.id}
            onClose={() => {
              // 在关闭前保存数据（处理点击空白处关闭的情况）
              // 注意：这里不能直接获取表单数据，因为数据在 config-form 组件中
              // 所以我们依赖 config-form 的 useEffect 清理函数来处理
              onClose();
            }}
          />
        </SheetContent>
      </Sheet>
    </div>
  );
};
