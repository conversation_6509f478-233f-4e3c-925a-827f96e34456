import { useState, useEffect, useRef } from "react";
import {
  <PERSON>le,
  Position,
  useUpdateNodeInternals,
  useReactFlow,
} from "@xyflow/react";
import { Badge } from "@/components/ui/badge";
import { NodeToolBar } from "../../node-tool-bar";
import { LLMConfigSheet } from "./llm-config-sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Play, Zap, ChevronUp, Eye, Copy, Check } from "lucide-react";

import { BorderBeam } from "@/components/magicui/border-beam";
import { AINodeData, InputFields } from "@/components/canvas/lib/node-factory";
import { useLLMExecution } from "../../../use-llm-execution";

type LLMNodeProps = {
  id: string;
  data?: {
    inputFields?: {
      id: string;
      paramName: string;
      paramValueType: "reference" | "dataset";
      paramValue: string;
      sourceType?: string;
      nodeContent?: string;
      nodeTitle?: string;
    }[];
    outputFields?: {
      aliasName: string;
      name: string;
    }[];
    title?: string;
    model?: string;
    userPrompt?: string;
    systemPrompt?: string;
    [key: string]: any;
  };
  selected: boolean;
};

export const LLMNode = ({ id, data, selected }: LLMNodeProps) => {
  const [showConfig, setShowConfig] = useState(false);
  const [showOutput, setShowOutput] = useState(false);
  const [copied, setCopied] = useState(false);
  const shouldPropagateDataRef = useRef(true);
  const outputContentRef = useRef<HTMLDivElement>(null);
  const updateNodeInternals = useUpdateNodeInternals();
  const { updateNodeData, getNodes, getEdges } = useReactFlow();

  const outputFields = data?.outputFields || [];
  const inputFields = data?.inputFields || [];
  const title = data?.title || "AI 处理节点";
  const model = data?.model || "GPT-4";

  // TODO: 是否正在执行
  const isExecuting = data?.executionState === "running";
  const streamingContent = data?.content;
  const executionState = data?.executionState;
  // 只在节点有明确执行状态时才显示内容，避免显示传播来的临时内容
  const hasOutput = streamingContent && streamingContent.trim().length > 0 && 
    (executionState === "running" || executionState === "success" || executionState === "error");

  // 当有输出内容时自动展开输出区域
  useEffect(() => {
    if (hasOutput && !showOutput) {
      setShowOutput(true);
    }
  }, [hasOutput]);

  // 流式内容自动滚动到底部
  useEffect(() => {
    if (outputContentRef.current && streamingContent && isExecuting) {
      outputContentRef.current.scrollTop = outputContentRef.current.scrollHeight;
    }
  }, [streamingContent, isExecuting]);

  // 移除复杂的自动字段管理 - 现在在连线时处理

  // 监听执行状态变化，完成后重置传播状态
  useEffect(() => {
    if (executionState === 'success' || executionState === 'error') {
      // 执行完成后，重置为默认的传播状态（true）
      // 这样下次执行时会根据执行模式重新设置
      setTimeout(() => {
        shouldPropagateDataRef.current = true;
      }, 100); // 稍微延迟，确保当前执行周期完成
    }
  }, [executionState]);

  const { runDataFlow } = useLLMExecution();

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isExecuting) {
      return;
    }
    setShowConfig(true);
  };


  // 处理单步执行
  const handleSingleExecute = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡到节点点击事件

    // 单步执行模式：立即禁止数据传播
    shouldPropagateDataRef.current = false;
    
    const nodes = getNodes();
    const edges = getEdges();
    // 只执行当前节点，不传递到后续节点
    runDataFlow(id, edges, nodes, { mode: 'single' });
  };

  // 处理执行此步及以后
  const handleExecuteFromHere = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡到节点点击事件

    // 执行此步及以后模式：允许数据传播
    shouldPropagateDataRef.current = true;
    
    const nodes = getNodes();
    const edges = getEdges();
    // 执行当前节点及后续所有连接的节点
    runDataFlow(id, edges, nodes, { mode: 'fromHere' });
  };

  const handleConfigSave = (configData: AINodeData) => {
    let prompts: string[] = [];
    const { inputFields } = configData;
    
    // 处理新的输入字段结构
    inputFields?.forEach((field: any) => {
      if (field?.paramName && field?.paramValue) {
        prompts.push(`${field.paramName}: ${field.paramValue}`);
      }
    });

    // 如果有用户自定义提示词，也加入
    if (configData.userPrompt) {
      prompts.push(configData.userPrompt);
    }

    // 更新节点数据
    updateNodeData(id, {
      ...data,
      ...configData,
      prompts,
    });
    // 更新节点数据后，触发重新测量
    setTimeout(() => {
      updateNodeInternals(id);
    }, 0);
  };
  return (
    <>
      <div
        className="rounded-[6px] min-w-[220px] border-[#D8D8D8] border relative flex flex-col"
        style={{ minHeight: 120 }}
        onClick={handleNodeClick}
      >
        {/* 节点头部 */}
        <div className="h-8 px-3 bg-white flex flex-row items-center gap-2 ">
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="23"
              height="23"
              viewBox="0 0 23 23"
              fill="none"
            >
              <path
                d="M15.3332 7.66699C15.3332 9.78409 13.6169 11.5003 11.4998 11.5003C9.38274 11.5003 7.6665 9.78409 7.6665 7.66699"
                stroke="#333333"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M11.4998 3.83301C9.38274 3.83301 7.6665 5.54924 7.6665 7.66634H15.3332C15.3332 5.54924 13.6169 3.83301 11.4998 3.83301Z"
                fill="#333333"
                stroke="#333333"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M5.75 7.66699H17.25"
                stroke="#333333"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M11.5 1.91699V3.83366"
                stroke="#333333"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M11.4998 12.9375C7.00104 12.9375 3.354 16.37 3.354 20.6042H19.6457C19.6457 16.37 15.9986 12.9375 11.4998 12.9375Z"
                fill="#333333"
                stroke="#333333"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8.625 16.292V18.2087"
                stroke="white"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M14.375 16.292V18.2087"
                stroke="white"
                strokeWidth="0.9375"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </span>
          <div className="flex-1">
            <h3 className="text-sm">{title}</h3>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                className="flex items-center gap-1 p-1 rounded hover:bg-white hover:bg-opacity-20 transition-colors"
                disabled={isExecuting}
                title="执行选项"
                onClick={(e) => e.stopPropagation()}
              >
                <Play className="w-3 h-3" fill="#333333" stroke="#333333" />
                <ChevronDown className="w-2 h-2" stroke="#333333" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              align="end" 
              className="w-40"
              onClick={(e) => e.stopPropagation()}
            >
              <DropdownMenuItem 
                onClick={handleSingleExecute}
                disabled={isExecuting}
                className="flex items-center gap-2 cursor-pointer"
              >
                <Play className="w-3 h-3" />
                <span>单步执行</span>
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={handleExecuteFromHere}
                disabled={isExecuting}
                className="flex items-center gap-2 cursor-pointer"
              >
                <Zap className="w-3 h-3" />
                <span>执行此步及以后</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 节点内容 */}
        <div className="p-2 space-y-4 text-xs text-[#565656] bg-background h-full flex-1">
          {/* 输入部分 */}
          <div className=" flex flex-row flex-nowrap items-center">
            <div className="text-xs">输入</div>
            <div className="flex flex-wrap gap-1.5 ml-4">
              {inputFields.map((field, index) => (
                <Badge
                  key={field.id || index}
                  className="text-xs bg-[#F0F0FA] rounded-sm text-black  transition-colors max-w-full truncate shadow-none px-1.5"
                  title={field.paramName}
                >
                  {field.paramName}
                </Badge>
              ))}
            </div>
          </div>

          {/* 输出部分 */}
          <div className="flex flex-row flex-nowrap items-center">
            <div className="text-xs">输出</div>
            <div className="flex flex-wrap gap-1.5 ml-4">
              {outputFields.map((field, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs bg-[#F0F0FA] rounded-sm text-black  transition-colors max-w-full truncate shadow-none px-1.5"
                  title={field.aliasName || "结果"}
                >
                  {field.aliasName || "结果"}
                </Badge>
              ))}
            </div>
          </div>

          {/* 输出内容显示区域 */}
          {(hasOutput || isExecuting) && (
            <div className="border-t border-gray-200 pt-3 mt-1">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Eye className="w-3 h-3 text-gray-500" />
                  <span className="text-xs font-medium text-gray-700">执行结果</span>
                  {executionState === "success" && (
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  )}
                  {executionState === "error" && (
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  )}
                  {isExecuting && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  {hasOutput && (
                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          await navigator.clipboard.writeText(streamingContent);
                          setCopied(true);
                          setTimeout(() => setCopied(false), 2000);
                        } catch (err) {
                          console.error('Failed to copy:', err);
                        }
                      }}
                      className="p-1 rounded hover:bg-gray-100 transition-colors"
                      title="复制输出内容"
                    >
                      {copied ? (
                        <Check className="w-3 h-3 text-green-500" />
                      ) : (
                        <Copy className="w-3 h-3 text-gray-500" />
                      )}
                    </button>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowOutput(!showOutput);
                    }}
                    className="p-1 rounded hover:bg-gray-100 transition-colors"
                    title={showOutput ? "折叠输出" : "展开输出"}
                  >
                    {showOutput ? (
                      <ChevronUp className="w-3 h-3 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-3 h-3 text-gray-500" />
                    )}
                  </button>
                </div>
              </div>
              {showOutput && (
                <div 
                  ref={outputContentRef}
                  className="bg-gray-50 rounded-md p-2 max-h-32 overflow-y-auto"
                >
                  {hasOutput || isExecuting ? (
                    <div className="space-y-2">
                      {/* 流式内容显示 */}
                      {streamingContent && (
                        <div className="text-xs text-gray-700 whitespace-pre-wrap leading-relaxed">
                          {streamingContent}
                        </div>
                      )}
                      {/* 执行状态提示 */}
                      {isExecuting && (
                        <div className="flex items-center gap-2 text-blue-600 border-t border-gray-200 pt-2">
                          <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                          <span className="text-xs">正在生成中...</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-xs text-gray-400 italic">暂无输出内容</div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 连接点 */}
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 bg-green-500 border-2 border-white shadow-md"
          style={{
            top: "16px",
            backgroundColor: "#fff",
            border: "1px solid #D8D8D8",
          }}
        />
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 bg-blue-500 border-2 border-white shadow-md"
          style={{
            top: "16px",
            backgroundColor: "#fff",
            border: "1px solid #D8D8D8",
          }}
        />

        {selected && <NodeToolBar id={id} tools={{ delete: true }} />}
        {isExecuting && (
          <BorderBeam
            duration={6}
            delay={3}
            size={2200}
            className="from-transparent via-blue-500 to-transparent"
          />
        )}
      </div>

      {/* 配置 Sheet */}
      {showConfig && (
        <LLMConfigSheet
          open={showConfig}
          onClose={() => setShowConfig(false)}
          onSave={handleConfigSave}
          initialData={{
            title,
            inputFields: inputFields as InputFields[],
            outputFields,
            userPrompt: data?.userPrompt || "",
            systemPrompt: data?.systemPrompt || "",
            model,
            id: id,
          }}
          zIndex={1000}
        />
      )}
    </>
  );
};
