import { useState, useEffect } from "react";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  Sheet<PERSON>eader,
  SheetDescription,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CheckCircle, Save, Plus, Trash2 } from "lucide-react";

export type OutputVariable = {
  id: string;
  paramName: string;
  paramValueType: "reference" | "dataset"; // 第一个下拉：引用或数据集
  paramValue: string; // 第二个下拉：具体的值
  outputTarget: "newNode" | "resourceFile";
};

export type LlmEndNodeConfigData = {
  title: string; // 固定为"结束节点"
  outputVariables: OutputVariable[];
};

type LlmEndNodeConfigSheetProps = {
  open: boolean;
  onClose: () => void;
  onSave: (data: LlmEndNodeConfigData) => void;
  initialData?: Partial<LlmEndNodeConfigData>;
  zIndex?: number;
};

const DEFAULT_VALUES: LlmEndNodeConfigData = {
  title: "结束节点",
  outputVariables: [],
};

// 二级联动选项数据
const REFERENCE_OPTIONS = [
  { value: "user_input", label: "用户输入" },
  { value: "system_prompt", label: "系统提示" },
  { value: "context", label: "上下文" },
  { value: "result", label: "结果" },
];

const DATASET_OPTIONS = [
  { value: "dataset_1", label: "数据集1" },
  { value: "dataset_2", label: "数据集2" },
  { value: "dataset_3", label: "数据集3" },
  { value: "custom_dataset", label: "自定义数据集" },
];

const COMPONENT_STYLES = {
  sheet: "w-[500px] sm:w-[500px] flex flex-col",
  input: "h-8 text-xs",
} as const;

const LlmEndNodeConfigSheet = ({
  open,
  onClose,
  onSave,
  initialData,
  zIndex = 50,
}: LlmEndNodeConfigSheetProps) => {
  const [formData, setFormData] = useState<LlmEndNodeConfigData>(DEFAULT_VALUES);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 初始化数据
  useEffect(() => {
    if (initialData) {
      setFormData({
        ...DEFAULT_VALUES,
        ...initialData,
        title: "结束节点", // 确保标题始终固定
      });
    }
  }, [initialData]);

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // 验证输出变量
    formData.outputVariables.forEach((variable) => {
      if (!variable.paramName.trim()) {
        newErrors[`paramName_${variable.id}`] = "参数名不能为空";
      }
      if (!variable.paramValue.trim()) {
        newErrors[`paramValue_${variable.id}`] = "参数值不能为空";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error("保存配置失败:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 添加新的输出变量
  const addOutputVariable = () => {
    const newVariable: OutputVariable = {
      id: Date.now().toString(),
      paramName: "",
      paramValueType: "reference",
      paramValue: "",
      outputTarget: "newNode",
    };
    setFormData(prev => ({
      ...prev,
      outputVariables: [...prev.outputVariables, newVariable],
    }));
  };

  // 删除输出变量
  const removeOutputVariable = (id: string) => {
    setFormData(prev => ({
      ...prev,
      outputVariables: prev.outputVariables.filter(v => v.id !== id),
    }));
  };

  // 更新输出变量字段
  const updateOutputVariable = (id: string, field: keyof OutputVariable, value: string) => {
    setFormData(prev => ({
      ...prev,
      outputVariables: prev.outputVariables.map(v =>
        v.id === id ? { ...v, [field]: value } : v
      ),
    }));
    // 清除该字段的错误
    const errorKey = `${field}_${id}`;
    if (errors[errorKey]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }
  };

  // 获取二级选项
  const getSecondaryOptions = (type: "reference" | "dataset") => {
    return type === "reference" ? REFERENCE_OPTIONS : DATASET_OPTIONS;
  };

  return (
    <Sheet open={open} onOpenChange={(open) => !open && onClose()}>
      <SheetContent
        side="right"
        className={COMPONENT_STYLES.sheet}
        style={{ zIndex }}
      >
        <SheetHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-red-500" />
            <span className="text-base font-semibold">结束节点</span>
          </div>
          <SheetDescription className="text-sm text-muted-foreground">
            配置输出变量和输出目标
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto mt-4 space-y-4">
          <Separator />
          
          {/* 输出变量模块 */}
          <div>
            <h3 className="text-sm font-medium mb-3">输出变量</h3>
            
            {/* 表头 */}
            <div className="grid grid-cols-[1fr,auto,1fr,auto] gap-2 mb-2 text-xs font-medium text-gray-600">
              <div>参数名</div>
              <div>类型</div>
              <div>参数值</div>
              <div className="text-center">操作</div>
            </div>

            {/* 输出变量列表 */}
            <div className="space-y-2">
              {formData.outputVariables.map((variable) => (
                <div key={variable.id} className="grid grid-cols-[1fr,auto,1fr,auto] gap-2 items-start">
                  <div>
                    <Input
                      value={variable.paramName}
                      onChange={(e) => updateOutputVariable(variable.id, "paramName", e.target.value)}
                      placeholder="请输入参数名"
                      className={COMPONENT_STYLES.input}
                    />
                    <div className="h-4 mt-1">
                      {errors[`paramName_${variable.id}`] && (
                        <p className="text-xs text-red-500">
                          {errors[`paramName_${variable.id}`]}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <Select
                      value={variable.paramValueType}
                      onValueChange={(value: "reference" | "dataset") => {
                        updateOutputVariable(variable.id, "paramValueType", value);
                        // 清空参数值，因为类型改变了
                        updateOutputVariable(variable.id, "paramValue", "");
                      }}
                    >
                      <SelectTrigger className="h-8 text-xs w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="reference">引用</SelectItem>
                        <SelectItem value="dataset">数据集</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="h-4 mt-1">
                      {/* 为类型选择器预留错误空间，保持对齐 */}
                    </div>
                  </div>
                  
                  <div>
                    <Select
                      value={variable.paramValue}
                      onValueChange={(value) => updateOutputVariable(variable.id, "paramValue", value)}
                    >
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="选择参数值" />
                      </SelectTrigger>
                      <SelectContent>
                        {getSecondaryOptions(variable.paramValueType).map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div className="h-4 mt-1">
                      {errors[`paramValue_${variable.id}`] && (
                        <p className="text-xs text-red-500">
                          {errors[`paramValue_${variable.id}`]}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex justify-center">
                    <Button
                      onClick={() => removeOutputVariable(variable.id)}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                    <div className="h-4 mt-1">
                      {/* 为删除按钮预留空间，保持对齐 */}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 添加新字段按钮 */}
            <Button
              onClick={addOutputVariable}
              variant="outline"
              size="sm"
              className="w-full mt-3 h-8 text-xs flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              添加新字段
            </Button>
          </div>

          <Separator />

          {/* 指定输出模块 */}
          <div>
            <h3 className="text-sm font-medium mb-3">指定输出</h3>
            <div className="space-y-3">
              {formData.outputVariables.map((variable) => (
                <div key={variable.id} className="flex items-center gap-2">
                  <span className="text-xs text-gray-600 min-w-0 flex-1 truncate">
                    {variable.paramName || "未命名参数"}
                  </span>
                  <Select
                    value={variable.outputTarget}
                    onValueChange={(value: "newNode" | "resourceFile") =>
                      updateOutputVariable(variable.id, "outputTarget", value)
                    }
                  >
                    <SelectTrigger className="h-8 text-xs w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newNode">新节点</SelectItem>
                      <SelectItem value="resourceFile">资源库文件</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              ))}
              
              {formData.outputVariables.length === 0 && (
                <p className="text-xs text-gray-500 text-center py-4">
                  请先添加输出变量
                </p>
              )}
            </div>
          </div>

          <Separator />

          {/* 预览 */}
          <div>
            <h3 className="text-sm font-medium mb-3">预览</h3>
            <div className="bg-gray-50 rounded p-3 text-xs">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-4 h-4 text-red-500" />
                <span className="font-medium">结束节点</span>
              </div>
              <div className="text-gray-600 space-y-1">
                <div>输出变量数量: {formData.outputVariables.length}</div>
                {formData.outputVariables.length > 0 && (
                  <div className="mt-2 space-y-1">
                    {formData.outputVariables.map((variable) => {
                      const secondaryOptions = getSecondaryOptions(variable.paramValueType);
                      const selectedOption = secondaryOptions.find(opt => opt.value === variable.paramValue);
                      return (
                        <div key={variable.id} className="text-xs">
                          <span className="font-medium">{variable.paramName || "未命名"}</span>
                          <span className="mx-2">→</span>
                          <span className="text-blue-600">
                            {variable.outputTarget === "newNode" ? "新节点" : "资源库文件"}
                          </span>
                          <div className="text-gray-500 mt-1 ml-2">
                            {variable.paramValueType === "reference" ? "引用" : "数据集"}: {selectedOption?.label || "未选择"}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-2 mt-4 pt-4 border-t">
          <Button
            onClick={handleSave}
            size="sm"
            className="flex-1"
            disabled={isSubmitting}
          >
            <Save className="w-4 h-4 mr-2" />
            {isSubmitting ? "保存中..." : "保存配置"}
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default LlmEndNodeConfigSheet;
