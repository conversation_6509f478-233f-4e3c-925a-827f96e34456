import { useState } from "react";
import { Handle, Position, useReactFlow } from "@xyflow/react";
import { NodeToolBar } from "../../node-tool-bar";
import { CheckCircle, Settings } from "lucide-react";
import LlmEndNodeConfigSheet from "./llm-config-sheet";

type LlmEndNodeProps = {
  type: string;
  id: string;
  data?: {
    outputVariables?: Array<{
      id: string;
      paramName: string;
      paramValueType: "reference" | "dataset";
      paramValue: string;
      outputTarget: "newNode" | "resourceFile";
    }>;
    content?: string;
    [key: string]: any;
  };
  selected: boolean;
};

// 二级联动选项数据（与配置表单保持一致）
const REFERENCE_OPTIONS = [
  { value: "user_input", label: "用户输入" },
  { value: "system_prompt", label: "系统提示" },
  { value: "context", label: "上下文" },
  { value: "result", label: "结果" },
];

const DATASET_OPTIONS = [
  { value: "dataset_1", label: "数据集1" },
  { value: "dataset_2", label: "数据集2" },
  { value: "dataset_3", label: "数据集3" },
  { value: "custom_dataset", label: "自定义数据集" },
];

const LlmEndNode = ({ type, id, data, selected }: LlmEndNodeProps) => {
  const [open, setOpen] = useState(false);
  const { updateNodeData } = useReactFlow();
  
  const title = "结束节点"; // 固定标题
  const color = "#ef4444"; // 固定颜色
  const content = data?.content || "";
  const outputVariables = data?.outputVariables || [];

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setOpen(true);
  };

  const handleConfigSave = (configData: any) => {
    updateNodeData(id, {
      ...data,
      ...configData,
      title: "结束节点", // 确保标题始终固定
    });
  };

  // 获取选项标签
  const getOptionLabel = (type: "reference" | "dataset", value: string) => {
    const options = type === "reference" ? REFERENCE_OPTIONS : DATASET_OPTIONS;
    return options.find(opt => opt.value === value)?.label || value;
  };

  return (
    <>
      <div
        className="bg-gradient-to-br w-72 from-red-50 to-pink-50 rounded-lg hover:shadow-lg transition-all duration-200 cursor-pointer h-full border-2 border-dashed"
        style={{ 
          minHeight: 180,
          borderColor: color 
        }}
        onClick={handleNodeClick}
      >
        {/* 节点头部 */}
        <div 
          className="bg-gradient-to-r from-red-500 to-pink-600 text-white p-3 rounded-t-lg flex items-center gap-2"
          style={{ background: `linear-gradient(to right, ${color}, ${color}dd)` }}
        >
          <CheckCircle className="w-4 h-4" />
          <div className="flex-1">
            <h3 className="font-semibold text-sm">{title}</h3>
            <p className="text-xs opacity-90">LLM 流程结束</p>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setOpen(true);
            }}
            className="p-1 rounded hover:bg-white hover:bg-opacity-20 transition-colors"
            title="配置结束节点"
          >
            <Settings className="w-4 h-4 opacity-80" />
          </button>
        </div>

        {/* 节点内容 */}
        <div className="p-4 space-y-4">
          {/* 状态指示 */}
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            结束状态
          </div>
          
          {/* 输出变量显示 */}
          {outputVariables.length > 0 && (
            <div className="bg-white rounded border p-3 text-sm">
              <div className="font-medium text-gray-700 mb-2">输出变量:</div>
              <div className="space-y-1">
                {outputVariables.map((variable) => (
                  <div key={variable.id} className="text-xs text-gray-600">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{variable.paramName}</span>
                      <span className="mx-2">→</span>
                      <span className="text-blue-600">
                        {variable.outputTarget === "newNode" ? "新节点" : "资源库文件"}
                      </span>
                    </div>
                    <div className="text-gray-500 ml-2 mt-1">
                      {variable.paramValueType === "reference" ? "引用" : "数据集"}: {getOptionLabel(variable.paramValueType, variable.paramValue)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* 内容显示区域 */}
          {content && (
            <div className="bg-white rounded border p-3 text-sm text-gray-600 max-h-32 overflow-y-auto">
              <div className="font-medium text-gray-700 mb-2">最终输出:</div>
              <div className="text-xs leading-relaxed">
                {content.length > 200 
                  ? `${content.substring(0, 200)}...` 
                  : content
                }
              </div>
            </div>
          )}
          
          {/* 空状态 */}
          {!content && outputVariables.length === 0 && (
            <div className="bg-gray-50 rounded border p-3 text-sm text-gray-500 text-center">
              <CheckCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <div>等待 LLM 流程完成</div>
            </div>
          )}
        </div>

        {/* 连接点 - 只有输入，没有输出 */}
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 bg-red-500 border-2 border-white shadow-md"
        />
        
        {selected && <NodeToolBar id={id} tools={{ delete: true }} />}
      </div>

      <LlmEndNodeConfigSheet
        open={open}
        onClose={() => setOpen(false)}
        onSave={handleConfigSave}
        initialData={data}
      />
    </>
  );
};

export { LlmEndNode };
