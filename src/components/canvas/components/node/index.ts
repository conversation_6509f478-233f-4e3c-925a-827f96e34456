import { LLMOutputNode } from "./llm-output-node";
import { GroupNode } from "./group-node";
import { MarkdownNode } from "./markdown-node";
import { LLMNode } from "./llm-node/index";
import { DropNode } from "./drop-node";
import { QuestionNode } from "./question-node";
import { AskAnyNode } from "./ask-any-node";
import { InputNode } from "./input-node";
import { NODE_TYPE_MAPS } from "../../lib/node-types";
import { ConfirmButtonNode } from "./confirm-button-node";
import { LlmEndNode } from "./llm-end-node";

export const nodeTypes = {
  llmOutput: LLMOutputNode,
  groupNode: GroupNode,
  markdown: MarkdownNode,
  llm: LLMNode,
  drop: DropNode,
  questionNode: QuestionNode,
  askAnyNode: AskAnyNode,
  inputNode: InputNode,
  confirmButtonNode: ConfirmButtonNode,
  llmEndNode: LlmEndNode,
};

export type NodeType = keyof typeof nodeTypes;

// 节点类型映射常量
export { NODE_TYPE_MAPS };
