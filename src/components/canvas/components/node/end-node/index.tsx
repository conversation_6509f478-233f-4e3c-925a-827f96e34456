import { Handle, Position } from "@xyflow/react";
import { NodeToolBar } from "../../node-tool-bar";
import { CheckCircle } from "lucide-react";

type EndNodeProps = {
  type: string;
  id: string;
  data?: {
    title?: string;
    color?: string;
    [key: string]: any;
  };
  selected: boolean;
};

export const EndNode = ({ type, id, data, selected }: EndNodeProps) => {
  const title = data?.title || "结束节点";
  const color = data?.color || "#ef4444";

  return (
    <>
      <div 
        className="bg-white rounded-lg flex flex-col relative border-2 border-dashed"
        style={{ borderColor: color }}
      >
        <div className="flex items-center justify-center p-4">
          <CheckCircle 
            size={24} 
            className="mr-2" 
            style={{ color: color }}
          />
          <span className="font-medium text-gray-700">{title}</span>
        </div>
        
        {/* 只有输入连接点，没有输出连接点 */}
        <Handle type="target" position={Position.Left} />
        
        {selected && (
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
            <NodeToolBar id={id} />
          </div>
        )}
      </div>
    </>
  );
}; 