import React, {
  forwardRef,
  type HTMLAttributes,
  type ReactNode,
  useState,
  useCallback,
  useRef,
  useEffect,
} from "react";
import {
  Panel,
  type NodeProps,
  type PanelPosition,
  useReactFlow,
  NodeResizer,
  Handle,
  Position,
} from "@xyflow/react";

import { cn } from "@/lib/utils";
import { BorderBeam } from "@/components/magicui/border-beam";
import { NodeToolBar } from "../../node-tool-bar";

/* GROUP NODE Label ------------------------------------------------------- */

export type GroupNodeLabelProps = HTMLAttributes<HTMLDivElement> & {
  nodeId?: string;
};

export const GroupNodeLabel = forwardRef<HTMLDivElement, GroupNodeLabelProps>(
  ({ children, className, nodeId, ...props }, ref) => {
    const [isEditing, setIsEditing] = useState(false);
    const [editValue, setEditValue] = useState((children as string) || "");
    const { setNodes } = useReactFlow();
    const inputRef = useRef<HTMLInputElement>(null);

    // 双击进入编辑模式
    const handleDoubleClick = useCallback(
      (event: React.MouseEvent) => {
        event.stopPropagation(); // 阻止事件冒泡
        setIsEditing(true);
        setEditValue((children as string) || "");
      },
      [children]
    );

    // 保存编辑内容
    const handleSave = useCallback(() => {
      if (nodeId) {
        setNodes((nodes) =>
          nodes.map((node) =>
            node.id === nodeId
              ? { ...node, data: { ...node.data, label: editValue } }
              : node
          )
        );
      }
      setIsEditing(false);
    }, [nodeId, editValue, setNodes]);

    // 取消编辑
    const handleCancel = useCallback(() => {
      setEditValue((children as string) || "");
      setIsEditing(false);
    }, [children]);

    // 处理键盘事件
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        event.stopPropagation(); // 阻止事件冒泡到React Flow
        if (event.key === "Enter") {
          handleSave();
        } else if (event.key === "Escape") {
          handleCancel();
        }
      },
      [handleSave, handleCancel]
    );

    // 处理失去焦点
    const handleBlur = useCallback(() => {
      handleSave();
    }, [handleSave]);

    // 自动聚焦到输入框
    useEffect(() => {
      if (isEditing && inputRef.current) {
        inputRef.current.focus();
        inputRef.current.select();
      }
    }, [isEditing]);

    return (
      <div ref={ref} className="h-full w-full" {...props}>
        <div
          className={cn(
            "w-fit bg-secondary p-1 text-xs text-card-foreground rounded-sm overflow-hidden",
            className
          )}
          onDoubleClick={!isEditing ? handleDoubleClick : undefined}
        >
          {isEditing ? (
            <input
              ref={inputRef}
              type="text"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              className="bg-transparent border-none outline-none text-xs min-w-[3ch] w-full"
              style={{ width: `${Math.max(editValue.length, 3)}ch` }}
            />
          ) : (
            children
          )}
        </div>
      </div>
    );
  }
);

GroupNodeLabel.displayName = "GroupNodeLabel";

export type GroupNodeProps = Partial<NodeProps> & {
  label?: ReactNode;
  position?: PanelPosition;
};

/* GROUP NODE -------------------------------------------------------------- */

export const GroupNode = forwardRef<HTMLDivElement, GroupNodeProps>(
  ({ position, selected, ...props }, ref) => {
    const label = (props.data?.label as string) ?? "";
    const [isResizing, setIsResizing] = useState(false);

    return (
      <div className="h-full rounded-sm bg-[rgba(240, 240, 250, 0.31)] bg-opacity-100 p-0 relative border border-primary">
        <NodeResizer 
          isVisible={selected} 
          minWidth={100} 
          minHeight={30}
          onResizeStart={() => setIsResizing(true)}
          onResizeEnd={() => setIsResizing(false)}
        />
        <Panel className={cn("m-0 p-0")} position={position}>
          {label && (
            <GroupNodeLabel
              className={"rounded-br-sm -translate-y-[120%]"}
              nodeId={props.id}
            >
              {label}
            </GroupNodeLabel>
          )}
        </Panel>
        <BorderBeam
          duration={6}
          delay={3}
          size={400}
          className="from-transparent via-blue-500 to-transparent"
        />
        <Handle type="target" position={Position.Left} />
        <Handle type="source" position={Position.Right} />

        {selected && <NodeToolBar id={props.id!} tools={{ delete: true }} />}
      </div>
    );
  }
);

GroupNode.displayName = "GroupNode";
