import { useEffect, useState, memo, useMemo, useCallback, useRef, Suspense } from "react";
import { FlowEditor } from "@/components/lexicalEditor/App";
import { useReactFlow, useUpdateNodeInternals } from "@xyflow/react";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { $convertFromMarkdownString } from "@lexical/markdown";
import { PLAYGROUND_TRANSFORMERS } from "@/components/lexicalEditor/plugins/MarkdownTransformers";
import PlaygroundNodes from "@/components/lexicalEditor/nodes/PlaygroundNodes";
import PlaygroundEditorTheme from "@/components/lexicalEditor/themes/PlaygroundEditorTheme";
import ContentEditable from "@/components/lexicalEditor/ui/ContentEditable";
import "@/components/lexicalEditor/index.css";
import { cn } from "@/lib/utils";

type ReadOnlyProps = {
  content: string;
  className?: string;
};
// 只读查看器 - 优化性能
const ReadOnlyViewer = ({ content, className }: ReadOnlyProps) => {
  // 检查是否是截图内容
  const isScreenshotContent = useMemo(() => {
    return content && typeof content === 'string' && content.includes('pdf截图-');
  }, [content]);

  // 截图内容的调试信息
  // useEffect(() => {
  //   if (isScreenshotContent) {
  //     console.log('📸 截图内容 ReadOnlyViewer 渲染:', { content: content.substring(0, 100) });
  //   }
  // }, [isScreenshotContent, content]);
  // 检查内容格式
  const isValidLexicalJSON = useMemo(() => {
    try {
      const parsed = JSON.parse(content);
      const root = parsed?.root;
      return (
        parsed &&
        typeof parsed === "object" &&
        root &&
        root.type === "root" &&
        Array.isArray(root.children)
      );
    } catch {
      return false;
    }
  }, [content]);

  // 缓存配置对象，只在内容变化时重新创建
  const preprocessHtmlToMarkdown = useCallback((raw: string) => {
    if (!raw || typeof raw !== 'string') return '';
    const hasImg = /<img\b[^>]*>/i.test(raw);
    if (!hasImg) return raw;
    // 将 <img src="..." alt="..."> 转为 ![alt](src)
    return raw.replace(/<img\b([^>]*)>/gi, (_m, attrs) => {
      const srcMatch = /src=["']([^"']+)["']/i.exec(attrs);
      const altMatch = /alt=["']([^"']*)["']/i.exec(attrs);
      const src = srcMatch ? srcMatch[1] : '';
      const alt = altMatch ? altMatch[1] : '';
      return src ? `![${alt}](${src})` : '';
    });
  }, []);

  const initialConfig = useMemo(() => {
    return {
      editorState: (editor: any) => {
        if (isValidLexicalJSON) {
          try {
            return editor.parseEditorState(content);
          } catch (e) {
            console.warn('解析 Lexical JSON 失败，回退到 Markdown 解析', e);
          }
        }
        const text = preprocessHtmlToMarkdown(content || "");
        return $convertFromMarkdownString(text, PLAYGROUND_TRANSFORMERS);
      },
      editable: false,
      namespace: `NodeReadOnlyViewer-${content.substring(0, 20)}`,
      nodes: [...PlaygroundNodes],
      onError: (error: Error) => {
        console.error("ReadOnly viewer error:", error);
      },
      theme: PlaygroundEditorTheme,
    };
  }, [content, isValidLexicalJSON, preprocessHtmlToMarkdown]);

  return (
    <LexicalComposer
      key={content.substring(0, 100)}
      initialConfig={initialConfig}
    >
      <Suspense fallback={null}>
        <div
          className={cn(
            "editor-shell viewer-mode h-auto",
            isScreenshotContent && "screenshot-content"
          )}
        >
          <RichTextPlugin
            contentEditable={
              <ContentEditable
                className={cn("ContentEditable__root", className)}
                placeholder=""
              />
            }
            placeholder={null}
            ErrorBoundary={LexicalErrorBoundary}
          />

        </div>
      </Suspense>
    </LexicalComposer>
  );
};

type NodeContentProps = {
  id: string;
  content: string;
  selected: boolean;
  isBusy?: boolean; // 模型输出或者其他操作正在执行，不能编辑
  hasBeenResized?: boolean; // 是否使用过 NodeResizer
};

export const NodeContent = ({
  id,
  content,
  isBusy = false,
  selected,
  hasBeenResized = false,
}: NodeContentProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempContent, setTempContent] = useState(content);
  const updateNodeInternals = useUpdateNodeInternals();
  const { updateNodeData } = useReactFlow();
  const isBlurringRef = useRef(false); // 防止重复失焦

  // 缓存事件处理函数
  const handleDoubleClick = useMemo(
    () => (e: React.MouseEvent<HTMLDivElement>) => {
      if (isBusy) return;
      e.stopPropagation();
      // 重置失焦标志
      isBlurringRef.current = false;
      // 确保进入编辑模式时，tempContent 与当前 content 同步
      setTempContent(content);
      setIsEditing(true);
    },
    [content, tempContent]
  );

  // 处理编辑器内容变化（只更新临时内容，不退出编辑模式）
  const handleEditorChange = useCallback((value: string | undefined) => {
    const newContent = value || "";
    setTempContent(newContent);
  }, []);

  // 处理失焦事件 - 保存内容并退出编辑模式
  const handleBlur = useCallback(() => {
    // 防止重复失焦
    if (isBlurringRef.current || !isEditing) {
      return;
    }

    isBlurringRef.current = true;
    console.log("🔒 开始失焦处理");

    // 使用 setState 的函数形式获取最新的 tempContent
    setTempContent((currentTempContent) => {
      const hasChanged = content !== currentTempContent;
      console.log(`💾 失焦保存 - 内容${hasChanged ? "已" : "未"}改变`);

      // 只有内容真正改变时才更新
      if (hasChanged) {
        // 使用 requestAnimationFrame 优化性能
        requestAnimationFrame(() => {
          updateNodeData(id, {
            content: currentTempContent,
          });

          // 延迟更新节点内部状态，减少卡顿
          setTimeout(() => {
            updateNodeInternals(id);
          }, 16); // 一帧的时间
        });
      }
      return currentTempContent;
    });

    // 立即退出编辑模式，提升响应速度
    setIsEditing(false);

    // 重置失焦标志
    setTimeout(() => {
      isBlurringRef.current = false;
      console.log("🔓 失焦处理完成");
    }, 200);
  }, [id, content, updateNodeData, updateNodeInternals, isEditing]);

  // 监听全局点击事件和键盘事件
  useEffect(() => {
    if (!isEditing) return;

    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as Element;
      // 检查点击是否在当前节点外部
      const nodeElement = document.querySelector(`[data-id="${id}"]`);
      if (nodeElement && !nodeElement.contains(target)) {
        handleBlur();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      // ESC 键退出编辑模式
      if (e.key === "Escape") {
        handleBlur();
      }
    };

    // 延迟添加监听器，避免立即触发
    const timer = setTimeout(() => {
      document.addEventListener("click", handleGlobalClick, true);
      document.addEventListener("keydown", handleKeyDown);
    }, 100);

    return () => {
      clearTimeout(timer);
      document.removeEventListener("click", handleGlobalClick, true);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isEditing, id, handleBlur]);

  // 同步外部内容变化到临时内容
  useEffect(() => {
    setTempContent(content);
  }, [content]);

  useEffect(() => {
    updateNodeInternals(id);
  }, [content, id, updateNodeInternals]);
// hide-scrollbar 
  if (isEditing) {
    return (
      <div
        data-id={id}
        className={`vditor  w-full bg-[#fff] overflow-auto nodrag nowheel h-[100%] leading-normal text-secondary-foreground font-light`}
        onClick={(e) => e.stopPropagation()}
        onDoubleClick={(e) => e.stopPropagation()}
      >
        <FlowEditor
          editClass={""}
          key={`editor-${id}-${isEditing}`} // 强制重新创建编辑器实例
          onChange={handleEditorChange}
          value={tempContent}
          onBlur={handleBlur}
          toolBar={false}
        />
      </div>
    );
  }

  return (
    <div
      data-id={id}
      className={cn(
        `py-2 px-[10px] text-xs leading-normal text-secondary-foreground font-light select-none   ${
          hasBeenResized
            ? "h-full overflow-y-auto"
            : " min-h-[60px] !max-h-[550px] overflow-y-scroll overscroll-contain hide-scrollbar"
        }`,
        {
          // nodrag: selected,
          nowheel: selected,
        }
      )}
      onDoubleClick={handleDoubleClick}>
      <ReadOnlyViewer key={content} content={content} className='select-none'/>
    </div>
  );
};

NodeContent.displayName = "NodeContent";
