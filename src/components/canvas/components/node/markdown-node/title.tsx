import { nodeData } from "@/store/flow-store.ts";
import { useReactFlow } from "@xyflow/react";
import { Input, InputRef } from "antd";
import { ChevronDown, ChevronLeft } from "lucide-react";
import { useEffect, useRef, useState } from "react";

type NodeTitleProps = {
  id: string;
  title: string;
  color: string;
  isExpanded: boolean;
  setIsExpanded: (isExpanded: boolean) => void;
};

const NodeTitle = ({
  title,
  id,
  color,
  isExpanded,
  setIsExpanded,
}: NodeTitleProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const { updateNodeData } = useReactFlow();
  const inputRef = useRef<InputRef>(null);

  const handleDoubleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleFinishEditing = () => {
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === "Escape") {
      e.preventDefault();
      handleFinishEditing();
      inputRef.current?.blur();
    }
  };

  // 组件卸载时确保退出编辑状态
  useEffect(() => {
    return () => {
      if (isEditing) {
        setIsEditing(false);
      }
    };
  }, [isEditing]);

  return (
    <div className="h-8 w-full px-[8px] flex items-center justify-between text-sm font-normal text-foreground bg-white rounded-[6px_6px_0px_0]">
      {!isEditing ? (
        <div style={{
          width:'calc(100% - 50px)'
        }} className="flex-1 flex" onDoubleClick={handleDoubleClick}>
          <div className="pr-10 relative w-full">
            <div className="truncate">{title || "Node Title"}</div>

            <div
              className="h-[2px] w-full absolute -bottom-[7px] -left-[8px]"
              style={{
                background: color,
              }}
            />
          </div>
        </div>
      ) : (
        <Input
          ref={inputRef}
          className="h-[30px] px-1 rounded-[5px] text-sm"
          defaultValue={title}
          placeholder="请输入标题"
          autoFocus
          onBlur={handleFinishEditing}
          onKeyDown={handleKeyDown}
          onChange={(e) => {
            updateNodeData(id, {
              title: e.target.value,
            });
          }}
        />
      )}

      <div className="flex flex-row flex-shrink-0">
        <div
          className="text-[#9A9A9A] hover:text-primary relative -bottom-[4px] p-[2px] cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? <ChevronLeft size={12} /> : <ChevronDown size={12} />}
        </div>
        <div className="p-1 cursor-pointer hover:text-primary ">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <g clipPath="url(#clip0_2028_3262)">
              <path
                d="M6 11C7.3807 11 8.6307 10.4404 9.53553 9.53553C10.4404 8.6307 11 7.3807 11 6C11 4.6193 10.4404 3.3693 9.53553 2.46446C8.6307 1.55964 7.3807 1 6 1C4.6193 1 3.3693 1.55964 2.46446 2.46446C1.55964 3.3693 1 4.6193 1 6C1 7.3807 1.55964 8.6307 2.46446 9.53553C3.3693 10.4404 4.6193 11 6 11Z"
                stroke="#9A9A9A"
                strokeWidth="0.75"
                strokeLinejoin="round"
              />
              <path
                d="M6 7.15625V6.15625C6.82842 6.15625 7.5 5.48467 7.5 4.65625C7.5 3.82783 6.82842 3.15625 6 3.15625C5.17158 3.15625 4.5 3.82783 4.5 4.65625"
                stroke="#9A9A9A"
                strokeWidth="0.75"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6 9.40625C6.34518 9.40625 6.625 9.12643 6.625 8.78125C6.625 8.43608 6.34518 8.15625 6 8.15625C5.65483 8.15625 5.375 8.43608 5.375 8.78125C5.375 9.12643 5.65483 9.40625 6 9.40625Z"
                fill="#9A9A9A"
              />
            </g>
            <defs>
              <clipPath id="clip0_2028_3262">
                <rect width="12" height="12" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default NodeTitle;
