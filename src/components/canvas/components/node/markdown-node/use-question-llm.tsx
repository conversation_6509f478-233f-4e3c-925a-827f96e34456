import { LLM_STATUS } from "@/common/llm";
import { useLLMOutput } from "../../use-llm-output";
import { useReactFlow } from "@xyflow/react";

const useQuestionLLM = () => {
  const { getNode, updateNodeData } = useReactFlow();
  const { generateLLMOutput } = useLLMOutput();
  const updateQuestionLLM = async (id: string, content: string) => {
    const node = getNode(id);
    const prompts = [
      content,
      `
       根据 ${node?.data?.title} 问题，生成完美的文章
      `,
    ];
    try {
      await generateLLMOutput(
        prompts,
        {
          model: "gpt-4o-mini",
          temperature: 0.5,
          stream: true, // 启用流式输出
        },
        (res: any, isEnd = false) => {
          if (isEnd) {
            updateNodeData(id, {
              ...node?.data,
              content: res,
              // llm 输出状态，streaming 流式输出，done 完成
              llmStatus: LLM_STATUS.DONE,
            });
          } else {
            updateNodeData(id, {
              ...node?.data,
              content: res,
              // llm 输出状态，streaming 流式输出，done 完成
              llmStatus: LLM_STATUS.STREAMING,
            });
          }
        }
      );
    } catch (e) {
      updateNodeData(id, {
        ...node?.data,
        llmStatus: LLM_STATUS.ERROR,
      });
    }
  };
  return { updateQuestionLLM };
};

export default useQuestionLLM;
