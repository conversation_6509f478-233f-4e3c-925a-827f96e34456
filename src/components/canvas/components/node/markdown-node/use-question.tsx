import { useEffect, useState } from "react";
import { useLLMOutput } from "../../use-llm-output";
import { toast } from "sonner";
import { map } from "lodash-es";

const useQuestion = (content: string, isOpen: boolean) => {
  const [questions, setQuestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const { generateLLMOutput } = useLLMOutput();
  useEffect(() => {
    if (!content && isOpen) {
      toast.info("请在笔记内添加有效的文本内容～");
    }
    if (!content || !isOpen) {
      setQuestions([]);
      return;
    }
    const systemPrompt = `
    针对内容请5位相关的专家，提出具有真知灼见的问题。
    使用使用可以通过JSON.parse解析的json的格式返回，示例：
  [{
    question:string, // 问题  
    expert": "string"    // 对应的专家，例如负责回答该问题的领域专家

  }]
    `;
    setLoading(true);
    generateLLMOutput([content], {
      model: "gpt-4o-mini",
      temperature: 0.5,
      systemPrompt: systemPrompt,
    })
      .then((res: any) => {
        const content = res.content;

        // 提取 JSON 部分（去除 ```json ... ```）
        const match = content.match(/```json\s*([\s\S]*?)\s*```/);

        if (!match) {
          console.error("未找到 JSON 代码块");
          return;
        }

        const jsonString = match[1];

        try {
          const data = JSON.parse(jsonString);
          setQuestions(map(data, (q) => ({ ...q, isSelect: true })));
          return data;
        } catch (e) {
          console.error("JSON 解析失败:", e);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [content, isOpen]);
  return { questions, loading };
};

export default useQuestion;
