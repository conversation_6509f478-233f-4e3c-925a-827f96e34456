import { useState, useCallback, useEffect, useRef } from "react";
import { useReactFlow } from "@xyflow/react";
import { nanoid } from "nanoid";
import { createNode } from "@/components/canvas/lib/node-factory";
import { NODE_TYPE_MAPS } from "@/components/canvas/lib/node-types";
import {
  filterOutTemporaryNodes,
  filterOutTemporaryEdges,
  createTemporaryEdgeId,
  removeAllTemporaryElements,
} from "@/components/canvas/lib/temporary-node-utils";
import useQuestion from "./use-question";
import useQuestionLLM from "./use-question-llm";
type Prop = {
  data: {
    isSource?: boolean;
    content: string;
  };
  selected: boolean;
  id: string;
};

export const useAskAny = ({ data, id, selected }: Prop) => {
  const [hasTemporaryNodes, setHasTemporaryNodes] = useState(false);
  const [questions, setQuestions] = useState<
    { question: string; expert: string; isSelect: boolean }[]
  >([]);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const {
    addNodes,
    addEdges,
    getNode,
    getNodes,
    setNodes,
    setEdges,
    getEdges,
  } = useReactFlow();
  // TODO: 点击open，之后在进行LLM调用
  const [isOpen, setIsOpen] = useState(false);
  const { questions: generatedQuestions, loading } = useQuestion(
    data.content,
    isOpen
  );
  const { updateQuestionLLM } = useQuestionLLM();

  // 当生成的问题变化时，更新问题列表（保留已有的自定义问题）
  useEffect(() => {
    if (generatedQuestions.length > 0) {
      setQuestions((prev: any) => {
        // 过滤出自定义问题（expert为"self"的）
        const customQuestions = prev.filter((q: any) => q.expert === "self");
        // 合并生成的问题和自定义问题
        return [...generatedQuestions, ...customQuestions];
      });
    }
  }, [generatedQuestions]);
  // 统一的位置计算函数 - React Flow 最佳实践
  const calculateAllPositions = useCallback(
    (
      basePosition: { x: number; y: number },
      nodeWidth: number,
      spacing: number,
      rightOffset: number,
      totalQuestionsCount: number
    ) => {
      return {
        inputNode: {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y - 60, // 位于源节点上方
        },
        questionNodes: Array.from({ length: totalQuestionsCount }, (_, i) => ({
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + i * spacing,
        })),
        confirmButton: {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + totalQuestionsCount * spacing,
        },
      };
    },
    []
  );

  // 检查节点是否已有出边（正式连接）
  const hasOutgoingEdges = useCallback(() => {
    const edges = getEdges();
    const nonTemporaryEdges = filterOutTemporaryEdges(edges);
    return nonTemporaryEdges.some((edge) => edge.source === id);
  }, [id, getEdges]);

  // 处理确定按钮点击
  const handleConfirm = useCallback(() => {
    const currentNode = getNode(id);
    if (!currentNode) return;

    const basePosition = currentNode.position;
    const nodeWidth = currentNode.width || 200;
    const spacing = 600;
    const rightOffset = 200;

    // 使用函数式更新获取最新的 questions 状态
    setQuestions((currentQuestions) => {
      // 过滤出被选中的问题
      const selectedQuestions = currentQuestions.filter((q) => q.isSelect);

      const permanentNodes = selectedQuestions.map(
        ({ question }: any, index) => {
          return createNode(
            NODE_TYPE_MAPS.markdown,
            {
              x: basePosition.x + nodeWidth + rightOffset,
              y: basePosition.y + index * spacing,
            },
            {
              title: question,
              isTemporary: false, // 确保创建的是正式节点
            }
          );
        }
      );

      // 创建正式连线
      const permanentEdges = permanentNodes.map((node) => ({
        id: nanoid(),
        source: id,
        target: node.id,
        type: "default",
        style: {
          stroke: "#6b7280", // 灰色的正式连线
        },
      }));

      // 原子操作：删除所有临时节点/边，添加正式节点/边
      setNodes((nodes) => [
        ...filterOutTemporaryNodes(nodes),
        ...permanentNodes,
      ]);

      setEdges((edges) => [
        ...filterOutTemporaryEdges(edges),
        ...permanentEdges,
      ]);

      // 执行 updateQuestionLLM
      setTimeout(() => {
        permanentNodes.forEach((edge) => {
          updateQuestionLLM(edge.id, data?.content);
        });
      }, 10);

      // 返回当前的 questions 状态（不修改）
      return currentQuestions;
    });

    // 清空状态
    setHasTemporaryNodes(false);
  }, [
    id,
    getNode,
    setNodes,
    setEdges,
    setQuestions,
    updateQuestionLLM,
    data?.content,
  ]);

  // 处理节点选择
  const handleToggleSelectQuestion = useCallback((question: string) => {
    setQuestions((prevQuestions) => {
      return prevQuestions.map((q) => {
        if (q.question === question) {
          return { ...q, isSelect: !q.isSelect };
        }
        return q;
      });
    });
  }, []);

  // 处理用户输入的自定义问题
  const handleAddCustomQuestion = useCallback(
    (questionText: string) => {
      const currentNode = getNode(id);
      if (!currentNode) return;

      const basePosition = currentNode.position;
      const nodeWidth = currentNode.width || 200;
      const spacing = 36;
      const rightOffset = 200;

      // 使用 setQuestions 的函数形式获取最新的 questions 状态
      setQuestions((prevQuestions) => {
        // 计算新问题节点的位置
        const newNodePosition = {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + prevQuestions.length * spacing,
        };

        // 创建新的临时问题节点
        const newQuestionNode = createNode(
          NODE_TYPE_MAPS.questionNode,
          newNodePosition,
          {
            title: questionText,
            isTemporary: true,
            isSelect: true,
            onSelect: handleToggleSelectQuestion,
          }
        );

        // 添加节点
        addNodes([newQuestionNode]);

        // 重新计算确认按钮位置
        const newConfirmButtonPosition = {
          x: basePosition.x + nodeWidth + rightOffset,
          y: basePosition.y + (prevQuestions.length + 1) * spacing,
        };

        // 查找并更新确认按钮节点的位置
        setNodes((nodes) =>
          nodes.map((node) => {
            if (
              node.data?.isTemporary &&
              node.type === NODE_TYPE_MAPS.confirmButtonNode
            ) {
              return {
                ...node,
                position: newConfirmButtonPosition,
              };
            }
            return node;
          })
        );

        setHasTemporaryNodes(true);

        // 返回新的问题列表
        return [
          ...prevQuestions,
          { question: questionText, expert: "self", isSelect: true },
        ];
      });
    },
    [id, getNode, addNodes, setNodes, handleToggleSelectQuestion]
  );

  // 检查点击的元素是否是相关节点（当前节点或其临时节点）
  const isClickOnRelatedNode = useCallback(
    (clickedElement: Element) => {
      if (!hasTemporaryNodes) return false;

      // 检查是否点击当前节点
      if (nodeRef.current?.contains(clickedElement)) {
        return true;
      }

      // 检查是否点击临时节点 - 通过查找具有 data-id 属性的父元素
      let currentElement = clickedElement as Element | null;
      while (currentElement) {
        const dataId = currentElement.getAttribute("data-id");
        if (dataId) {
          // 获取当前所有节点，检查点击的是否是临时节点
          const allNodes = getNodes();
          const clickedNode = allNodes.find((node) => node.id === dataId);

          // 如果点击的是当前节点创建的临时节点，返回 true
          if (clickedNode?.data?.isTemporary) {
            return true;
          }
          break;
        }
        currentElement = currentElement.parentElement;
      }

      return false;
    },
    [hasTemporaryNodes, getNodes]
  );

  // 全局点击监听，检测点击非相关节点时关闭临时节点
  useEffect(() => {
    if (!hasTemporaryNodes) return;

    const handleGlobalClick = (event: MouseEvent) => {
      const clickedElement = event.target as Element;

      // 如果点击的不是相关节点（当前节点或其临时节点），则关闭临时节点
      if (!isClickOnRelatedNode(clickedElement)) {
        // 清除定时器
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // 删除所有临时节点和边
        const currentNodes = getNodes();
        const currentEdges = getEdges();
        const { nodes: permanentNodes, edges: permanentEdges } =
          removeAllTemporaryElements(currentNodes, currentEdges);

        setNodes(permanentNodes);
        setEdges(permanentEdges);

        // 清空状态
        setHasTemporaryNodes(false);
        setIsOpen(false);
        setQuestions([]);
      }
    };

    // 使用 capture 阶段监听，确保能够在其他事件处理器之前捕获点击
    document.addEventListener("click", handleGlobalClick, true);

    return () => {
      document.removeEventListener("click", handleGlobalClick, true);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [
    hasTemporaryNodes,
    isClickOnRelatedNode,
    getNodes,
    getEdges,
    setNodes,
    setEdges,
  ]);

  const executeQuestionLLM = useCallback(() => {
    const currentNode = getNode(id);

    // 检查是否可以创建临时节点：
    // 1. 节点存在
    // 2. 当前没有临时节点
    // 3. 当前节点没有正式的出边
    if (!currentNode || hasTemporaryNodes || hasOutgoingEdges()) {
      return;
    }

    const basePosition = currentNode.position;
    const nodeWidth = currentNode.width || 200;
    const spacing = 36;
    const rightOffset = 200;

    // 使用统一位置计算函数
    const positions = calculateAllPositions(
      basePosition,
      nodeWidth,
      spacing,
      rightOffset,
      questions.length
    );

    // 1. 创建输入框节点
    const inputNode = createNode(
      NODE_TYPE_MAPS.inputNode,
      positions.inputNode,
      {
        title: "输入问题...",
        isTemporary: true,
      }
    );

    // 为输入框节点添加发送回调
    (inputNode.data as any).onSend = handleAddCustomQuestion;

    // 2. 创建临时问题节点
    const tempNodes = questions.map(({ question, isSelect }: any, index) => {
      return createNode(
        NODE_TYPE_MAPS.questionNode,
        positions.questionNodes[index],
        {
          title: question,
          isTemporary: true,
          // 是否是选择执行的节点
          isSelect: isSelect !== undefined ? isSelect : true,
          onSelect: handleToggleSelectQuestion,
        }
      );
    });

    // 3. 创建确定按钮节点
    const confirmButton = createNode(
      NODE_TYPE_MAPS.confirmButtonNode,
      positions.confirmButton,
      {
        title: "✓ 确定",
        isTemporary: true,
        onConfirm: handleConfirm,
      }
    );

    // 只创建 input 节点的临时连线，使用实线
    const inputEdge = {
      id: createTemporaryEdgeId("input"),
      source: id,
      target: inputNode.id,
      type: "default",
      style: {
        stroke: "#3b82f6", // 使用实线，移除 strokeDasharray
      },
      data: { isTemporary: true },
    };

    // 添加所有节点和仅 input 节点的连线
    addNodes([inputNode, ...tempNodes, confirmButton]);
    addEdges([inputEdge]); // 只添加 input 节点的边

    // 更新状态
    setHasTemporaryNodes(true);
  }, [
    id,
    questions,
    hasTemporaryNodes,
    addNodes,
    addEdges,
    getNode,
    handleAddCustomQuestion,
    handleConfirm,
    hasOutgoingEdges,
    calculateAllPositions,
  ]);
  // 执行 question 的 LLM 调用
  const openAskAnyNode = useCallback(() => {
    if (!isOpen) {
      setIsOpen(true);
    }
  }, []);

  const closeAskAnyNode = useCallback(() => {
    // 只更新悬停状态，删除逻辑由全局鼠标监听处理
  }, []);

  useEffect(() => {
    if (isOpen && questions.length > 0) {
      executeQuestionLLM();
    }
  }, [isOpen, questions]);

  return {
    openAskAnyNode,
    loading,
    closeAskAnyNode,
    nodeRef,
  };
};
