import {
  <PERSON><PERSON>,
  <PERSON>de<PERSON><PERSON><PERSON>,
  <PERSON>sition,
  useNodeConnections,
  useUpdateNodeInternals,
  useReactFlow,
} from "@xyflow/react";
import NodeTitle from "./title";
import { NodeContent } from "./content";
import { NodeToolBar } from "../../node-tool-bar";
import { NodeTags } from "../node-tags";
import { useMemo, useState, useEffect, useCallback, useRef } from "react";
import { Button } from "@/components/ui/button";
import { useAskAny } from "./use-ask-any";
import { toast } from "sonner";
import { Send } from "lucide-react";
import { LLM_STATUS } from "@/common/llm";
import { NODE_DEFAULTS } from "@/components/flow/constants/node-defaults";
import { usePreventZoomOnly } from "@/hooks/usePreventBrowserZoom";
import ErrorBoundary from "@/components/error/ErrorBoundary";

type MarkdownNodeProps = {
  id: string;
  data?: {
    [key: string]: any;
  };
  selected: boolean;
};

export const MarkdownNode = ({
  id,
  data,
  selected,
  ...props
}: MarkdownNodeProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const updateNodeInternals = useUpdateNodeInternals();
  const { getNode, updateNode } = useReactFlow();
  const { openAskAnyNode, closeAskAnyNode, loading, nodeRef, hasTemporaryNodes } = useAskAny({
    data: {
      content: data?.content,
    },
    id,
    selected,
  });

  // 获取节点的出边连接
  const sourceConnections = useNodeConnections({
    id: id,
    handleType: "source",
  });

  // LLM 输出状态
  const llmStatus = useMemo(() => {
    return data?.llmStatus || LLM_STATUS.DONE;
  }, [data?.llmStatus]);

  // Hover 显示：统一一个状态 + 400ms 隐藏延迟；支持点击固定显示
  const HOVER_HIDE_DELAY = 400;
  const [isHovering, setIsHovering] = useState(false);
  const [isPinned, setIsPinned] = useState(false);
  const hoverDelayRef = useRef<NodeJS.Timeout | null>(null);

  // 是否允许展示发送按钮（业务条件）
  const canShowSendButton = useMemo(() => {
    return llmStatus === LLM_STATUS.DONE && sourceConnections.length === 0;
  }, [llmStatus, sourceConnections]);

  // 最终显示：hover 或已固定 且 满足业务条件
  const showSendButton = useMemo(() => {
    return canShowSendButton && (isHovering || isPinned);
  }, [canShowSendButton, isHovering, isPinned]);

  // 跟踪是否正在调整大小
  const [isResizing, setIsResizing] = useState(false);
  // 本地状态跟踪手动调整标记，用于立即响应
  const [localHasBeenResized, setLocalHasBeenResized] = useState(false);
  
  // 判断节点是否被手动调整过 - 结合数据标记和本地状态
  const hasBeenResized = useMemo(() => {
    const currentNode = getNode(id);
    const dataHasBeenResized = currentNode?.data?.hasBeenResized === true;
    // 优先使用本地状态，确保立即响应
    return localHasBeenResized || dataHasBeenResized;
  }, [getNode, id, data?.hasBeenResized, localHasBeenResized]);

  // 设置节点初始尺寸 - 仅在节点创建时执行一次
  // useEffect(() => {
  //   const currentNode = getNode(id);

  //   // 同步本地状态与数据状态
  //   const dataHasBeenResized = currentNode?.data?.hasBeenResized === true;
  //   setLocalHasBeenResized(dataHasBeenResized);

  //   // 检测是否是新创建的节点（没有设置过尺寸）
  //   if (currentNode && (!currentNode.width || !currentNode.height)) {
  //     // 先设置宽度，高度稍后通过测量设置
  //     updateNode(id, {
  //       width: NODE_DEFAULTS.WIDTH,
  //       height: NODE_DEFAULTS.MIN_HEIGHT, // 临时高度，将被实际测量值替换
  //     });
      
  //     // 延迟测量实际渲染高度
  //     setTimeout(() => {
  //       const nodeElement = nodeRef.current;
  //       if (nodeElement) {
  //         // 获取节点实际渲染的高度
  //         const actualHeight = nodeElement.offsetHeight || NODE_DEFAULTS.MIN_HEIGHT;
          
  //         // 更新为实际高度
  //         updateNode(id, {
  //           height: actualHeight,
  //           measured: {
  //             width: NODE_DEFAULTS.WIDTH,
  //             height: actualHeight,
  //           }
  //         });
  //       }
  //     }, 50); // 短暂延迟确保渲染完成
  //   }
  // }, [id, getNode, updateNode, nodeRef]);

  // 防抖更新节点尺寸 - 优化性能
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedUpdateNodeInternals = useCallback(() => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    updateTimeoutRef.current = setTimeout(() => {
      requestAnimationFrame(() => {
        updateNodeInternals(id);

        // 只有在未使用过 NodeResizer 时才自动调整高度
        if (!hasBeenResized) {
          const currentNode = getNode(id);
          const nodeElement = nodeRef.current;

          if (currentNode && nodeElement) {
            const contentHeight = nodeElement.scrollHeight;
            const currentHeight =
              currentNode.height || NODE_DEFAULTS.MIN_HEIGHT;

            // 如果内容高度超过当前节点高度，自动增长
            if (contentHeight > currentHeight) {
              updateNode(id, {
                height: Math.max(contentHeight, NODE_DEFAULTS.MIN_HEIGHT),
              });
            }
          }
        }
      });
    }, 16); // 一帧的时间，保证流畅性
  }, [id, updateNodeInternals, getNode, updateNode, nodeRef, hasBeenResized]);

  // 当内容变化或LLM状态变化时，更新节点内部尺寸 - React Flow 最佳实践
  useEffect(() => {
    // 如果正在调整大小，不触发更新避免闪回
    if (!isResizing) {
      debouncedUpdateNodeInternals();
    }

    // 清理函数
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, [data?.content, llmStatus, isExpanded, debouncedUpdateNodeInternals, isResizing]);

  // 使用通用的防缩放 Hook
  usePreventZoomOnly(nodeRef as any, selected);
  return (
    <ErrorBoundary
      fallback={
        <div className="bg-red-50 border border-red-200 rounded-md p-4 min-h-[120px] flex items-center justify-center">
          <span className="text-red-600 text-sm">节点渲染失败</span>
        </div>
      }
    >
      <div
        ref={nodeRef}
        className={`bg-[#F9FAFC] w-full rounded-md border border-[hsla(0, 0%, 85%, 1)] flex flex-col shadow-sm relative
          ${hasBeenResized ? "h-full" : "h-full"}
        `}
        style={{
          minWidth: "200px",
          minHeight: hasBeenResized ? undefined : "100px",
        }}
        onMouseEnter={() => {
          if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
          setIsHovering(true);
        }}
        onMouseLeave={() => {
          if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
          hoverDelayRef.current = setTimeout(() => {
            // 仅在未固定时才收起
            if (!isPinned) setIsHovering(false);
          }, HOVER_HIDE_DELAY);
        }}
      >
        {/* 标题区域 */}
        <div className="flex-shrink-0 bg-background rounded-t-md">
          <NodeTitle
            id={id}
            title={data?.title}
            color={data?.color}
            isExpanded={isExpanded}
            setIsExpanded={setIsExpanded}
          />
        </div>

        {/* 内容区域 */}
        <div
          className={`flex-1 min-h-0 rounded-b-md ${hasBeenResized ? "overflow-auto" : "overflow-visible"
            }`}
        >
          <NodeContent
            id={id}
            content={data?.content}
            selected={selected}
            isBusy={llmStatus === LLM_STATUS.STREAMING}
            hasBeenResized={hasBeenResized}
          />
        </div>

        {/* 标签区域 */}
        <NodeTags tags={data?.tags} nodeId={id} />

        <Handle
          type="target"
          position={Position.Left}
          id="left"
          style={{
            // left: "-4px",
            top: "16px",
            width: "8px",
            height: "8px",
            backgroundColor: "#fff",
            border: "1px solid #D8D8D8",
          }}
        />
        <Handle
          type="source"
          position={Position.Right}
          id="right"
          style={{
            // right: "-4px",
            top: "16px",
            width: "8px",
            height: "8px",
            backgroundColor: "#fff",
            border: "1px solid #D8D8D8",
          }}
        />
        {showSendButton && (
          <div
            className="absolute top-1/2 right-0 translate-x-6 -translate-y-2"
            onMouseEnter={() => {
              if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
              setIsHovering(true);
            }}
            onMouseLeave={() => {
              if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
              hoverDelayRef.current = setTimeout(() => {
                if (!isPinned) setIsHovering(false);
              }, HOVER_HIDE_DELAY);
            }}
          >
            <Button
              className="p-0 h-5 w-5 rounded-full flex items-center justify-center"
              variant={"ghost"}
              onClick={(e) => {
                e.stopPropagation();
                if (!data?.content) {
                  toast.error("你还没有可以提问的内容～");
                  return;
                }
                openAskAnyNode();
                // 点击后固定显示
                setIsPinned(true);
              }}
              disabled={loading}
            >
              <div
                className={loading ? "animate-orbit" : ""}
                style={{
                  transformOrigin: "center",
                }}
              >
                <Send size={14} className=" text-primary" />
              </div>
            </Button>
          </div>
        )}
        {/* 调试日志（如需调试可在此处临时打开） */}
        {hasTemporaryNodes && (isHovering || isPinned) && (
          <div
            className="absolute top-1/2 right-0 translate-x-6 -translate-y-2"
            onMouseEnter={() => {
              if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
              setIsHovering(true);
            }}
            onMouseLeave={() => {
              if (hoverDelayRef.current) clearTimeout(hoverDelayRef.current);
              hoverDelayRef.current = setTimeout(() => {
                if (!isPinned) setIsHovering(false);
              }, HOVER_HIDE_DELAY);
            }}
          >
            <div
              className="p-0 rounded-full flex items-center justify-center"
              onClick={(e) => {
                e.stopPropagation();
                closeAskAnyNode();
                // 关闭后取消固定
                setIsPinned(false);
              }}
            >
              <div className="text-primary text-xs">
                关闭
              </div>
            </div>
          </div>
        )}

        <NodeResizer
          minWidth={200}
          minHeight={100}
          isVisible={selected}
          shouldResize={() => true}
          onResizeStart={() => {
            // 开始调整大小时设置标记
            setIsResizing(true);
            // 立即设置本地状态，确保UI立即响应
            setLocalHasBeenResized(true);
            // 在节点数据中标记为手动调整过
            updateNode(id, { 
              data: { 
                ...data, 
                hasBeenResized: true 
              } 
            });
          }}
          onResize={() => {
            // 拖拽过程中不调用 debouncedUpdateNodeInternals 避免闪回
            // hasBeenResized 会通过尺寸比较自动更新
          }}
          onResizeEnd={() => {
            // 调整结束时重置状态
            setIsResizing(false);
            // 设置状态后，useEffect 会自动触发 debouncedUpdateNodeInternals
            // 无需在此处重复调用
          }}
        />
        {selected && (
          <>
            <NodeToolBar id={id} content={data} />
          </>
        )}
      </div>
    </ErrorBoundary>
  );
};
