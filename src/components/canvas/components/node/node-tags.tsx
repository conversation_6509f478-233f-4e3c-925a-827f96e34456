import React, { useMemo, useState } from "react";
import { useTagStore } from "@/components/tag-management/stores/useTagStore";
import { useReactFlow } from "@xyflow/react";
import { Plus, X } from "lucide-react";
import TagSearch from "@/components/tag-management/tag-search";

type Tag = {
  name: string;
  color?: string;
  backgroundColor?: string;
};

type NodeTagsProps = {
  // 兼容两种形态：直接传 Tag 对象，或仅传 tagId 字符串
  tags?: Array<Tag | string>;
  className?: string;
  nodeId?: string;
};

export const NodeTags: React.FC<NodeTagsProps> = ({ tags, className = "", nodeId }) => {
  // 订阅：分组数据变化时触发重渲染，不在此发起加载
  const findTagInGroups = useTagStore((s) => s.findTagInGroups);
  const tagGroups = useTagStore((s) => s.tagGroups);
  const { setNodes, updateNodeData } = useReactFlow();
  const [open, setOpen] = useState(false);

  const displayTags = useMemo<Array<{ id?: string; tag: Tag }>>(() => {
    if (!tags) return [];
    return (tags as Array<Tag | string>)
      .map((t) => {
        if (typeof t === "string") {
          const found = findTagInGroups(t);
          return { id: t, tag: { name: found?.tag?.name || t } };
        }
        // 尝试根据名称反查 id（若存在于分组）
        const match = tagGroups.flatMap((g) => g.tags).find((x) => x.name === t.name);
        return { id: match?.id, tag: t };
      })
      .filter(Boolean) as Array<{ id?: string; tag: Tag }>;
  }, [tags, findTagInGroups, tagGroups]);

  const handleRemove = (id?: string, name?: string) => {
    if (!nodeId) return;
    const current = tags ?? [];
    if (current.length === 0) return;
    const isIdArray = current.every((t) => typeof t === "string");
    if (isIdArray && id) {
      const newTags = (current as string[]).filter((tid) => tid !== id);
      updateNodeData(nodeId, { tags: newTags });
      return;
    }
    if (!isIdArray && name) {
      const newTags = (current as Tag[]).filter((t) => t.name !== name);
      updateNodeData(nodeId, { tags: newTags });
    }
  };

  if (!displayTags || displayTags.length === 0) return null;

  return (
    <div className={`flex-shrink-0 px-4 py-2 rounded-b-md border-t border-border relative ${className}`}>
      {/* 打开标签管理 */}
      <button
        aria-label="manage-tags"
        className="absolute right-2 top-2 h-5 w-5 flex items-center justify-center rounded hover:bg-gray-100"
        onClick={(e) => {
          e.stopPropagation();
          if (nodeId) {
            setNodes((ns) => ns.map((n) => ({ ...n, selected: n.id === nodeId })));
          }
          setOpen(true);
        }}
      >
        <Plus size={14} className="text-gray-500" />
      </button>
      <div className="flex gap-2 flex-wrap">
        {displayTags.map(({ id: tagId, tag }, index) => (
          <div
            key={index}
            className="px-2 py-1 rounded text-xs font-light flex items-center gap-1 group"
            style={{
              backgroundColor: tag.backgroundColor || "var(--muted)",
              color: tag.color || "var(--primary)",
            }}
          >
            {tag.name}
            <button
              aria-label="remove-tag"
              className="ml-1 hover:bg-gray-300/60 rounded-full p-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-150 focus:opacity-100"
              onClick={(e) => {
                e.stopPropagation();
                handleRemove(tagId, tag.name);
              }}
            >
              <X size={10} />
            </button>
          </div>
        ))}
      </div>
      {open && <TagSearch onClose={() => setOpen(false)} nodeId={nodeId} />}
    </div>
  );
};