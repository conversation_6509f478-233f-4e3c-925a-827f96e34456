import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";

type QuestionNodeProps = {
  type: string;
  id: string;
  data?: {
    title?: string;
    content?: string;
    color?: string;
    isTemporary?: boolean;
    onConfirm?: () => void;
    [key: string]: any;
  };
  selected: boolean;
};

export const QuestionNode = ({
  type,
  id,
  data,
  selected,
}: QuestionNodeProps) => {
  const isTemporary = data?.isTemporary || false;
  console.log("临时节点", data);
  return (
    <>
      <div
        data-temp-node={isTemporary}
        className={`w-[400px] rounded-[5px] flex flex-col justify-center border-[1px] border-[rgba(151, 151, 151, 0.50)] cursor-default transition-all duration-200 `}
      >
        <Label className="text-xs font-light text-[#13123C] truncate  p-2 flex justify-between items-center gap-2 cursor-pointer">
          <span className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">{data?.title || "问题节点"}</span>
          <Checkbox defaultChecked={data?.isSelect} onCheckedChange={()=>{
            console.log('data?.onSelect',data?.onSelect)
            data?.onSelect(data?.title)
          }} />
        </Label>
        {/* <div className="flex-1 p-3 pt-0">
          <div className="text-xs text-gray-500 truncate">{data?.content || ""}</div>
        </div> */}
        {/* <Handle type="source" position={Position.Right} />
        <Handle type="target" position={Position.Left} /> */}
      </div>
    </>
  );
};
