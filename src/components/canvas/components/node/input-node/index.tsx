import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position } from "@xyflow/react";
import { Send, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

type InputNodeProps = {
  type: string;
  id: string;
  data?: {
    placeholder?: string;
    onSend?: (text: string) => void;
    onInputChange?: (hasContent: boolean) => void;
    onDirectCreate?: (text: string) => void;
    isTemporary?: boolean;
    [key: string]: any;
  };
  selected: boolean;
};

export const InputNode = ({ type, id, data, selected }: InputNodeProps) => {
  const [inputValue, setInputValue] = useState("");
  const isTemporary = data?.isTemporary || false;

  const handleInputChange = useCallback((value: string) => {
    console.log('InputNode handleInputChange 被调用:', value); // 调试日志
    setInputValue(value);
    // 通知父组件输入框内容变化
    const hasContent = value.trim().length > 0;
    console.log('调用父组件 onInputChange:', hasContent); // 调试日志
    data?.onInputChange?.(hasContent);
  }, [data]);

  const handleSend = useCallback(() => {
    if (inputValue.trim()) {
      // 优先使用直接创建节点的回调，回退到原有的 onSend
      if (data?.onDirectCreate) {
        data.onDirectCreate(inputValue.trim());
      } else if (data?.onSend) {
        data.onSend(inputValue.trim());
      }
      setInputValue(""); // 清空输入框
      // 清空后通知父组件
      data?.onInputChange?.(false);
    }
  }, [inputValue, data]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  return (
    <>
      <div
        data-temp-node={isTemporary}
        className={`h-full rounded-lg flex items-center cursor-default transition-all duration-200 `}
      >
        <div className="flex-1 flex items-center gap-2">
          <div className="relative flex-1 flex items-center">
            <Search size={16} className="absolute left-3 text-gray-400" />
            <Input
              type="text"
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={data?.placeholder}
              className="flex-1 text-sm outline-none rounded-full bg-transparent placeholder-gray-400 pl-10 pr-10"
              autoFocus={isTemporary}
            />
            <button
              onClick={handleSend}
              disabled={!inputValue.trim()}
              className={`absolute right-3 p-1 rounded transition-colors ${
                inputValue.trim()
                  ? "text-blue-600 hover:text-blue-800 hover:bg-blue-50 cursor-pointer"
                  : "text-gray-400 cursor-not-allowed"
              }`}
              title="发送问题"
            >
              <Send size={16} />
            </button>
          </div>
        </div>
        <Handle type="source" position={Position.Right} />
        <Handle type="target" position={Position.Left} />
      </div>
    </>
  );
};
