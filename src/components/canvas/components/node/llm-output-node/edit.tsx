import { Remark } from "react-remark";
import { useEffect, useState, useRef, useCallback } from "react";
import { FlowEditor, NodeEditor } from "@/components/lexicalEditor/App";
import { useReactFlow, useUpdateNodeInternals } from "@xyflow/react";
import { FlowEditorWithHook } from "@/components/lexicalEditor/Editor";

// 自定义 hook：检测点击外部
const useClickOutside = (
  ref: React.RefObject<HTMLElement | null>,
  callback: () => void
) => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        callback();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref, callback]);
};

// 自定义 hook：键盘事件监听
const useKeyboardEvents = (callback: () => void) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        event.preventDefault();
        callback();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [callback]);
};

type NodeContentProps = {
  id: string;
  content: string;
  selected: boolean;
  onBlur: () => void;
};

export const Edit = ({ id, content, onBlur }: NodeContentProps) => {
  const { updateNodeData, getNode } = useReactFlow();
  const editorRef = useRef<HTMLDivElement>(null);
  

  return (
    <div
      ref={editorRef}
      className="vditor nodrag w-full max-h-full h-full overflow-y-scroll"
      onClick={(e) => e.stopPropagation()}
      onDoubleClick={(e) => e.stopPropagation()}
    >
      <FlowEditor
        onChange={(value: string | undefined) => {
          const node = getNode(id);
          updateNodeData(id, {
            ...node?.data,
            content: value || "",
          });
          onBlur();
        }}
        value={content}
      />
    </div>
  );
};
