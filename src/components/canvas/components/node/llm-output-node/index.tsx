import { Handle, Panel, Position } from "@xyflow/react";
import NodeTitle from "./title";
import { NodeToolBar } from "../../node-tool-bar";
import { useState } from "react";
import { Edit } from "./edit";
import { Preview } from "./preview";
import { ShineBorder } from "@/components/magicui/shine-border";

// 自定义滚动条样式
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }
`;

// 注入样式
if (typeof document !== "undefined") {
  const style = document.createElement("style");
  style.textContent = scrollbarStyles;
  document.head.appendChild(style);
}

type MarkdownNodeProps = {
  type: string;
  id: string;
  data?: {
    [key: string]: any;
  };
  selected: boolean;
};

export const LLMOutputNode = ({
  type,
  id,
  data,
  selected,
}: MarkdownNodeProps) => {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <>
      <div className={`bg-white w-96 rounded-lg flex flex-col relative`}>
        <div className="flex-shrink-0">
          <NodeTitle
            id={id}
            title={data?.title}
            color={data?.color || "#dbeafe"}
          />
        </div>
        <div className="flex-1 min-h-[100px] max-h-[500px] overflow-y-auto w-full custom-scrollbar">
          {isEditing ? (
            <Edit
              id={id}
              content={data?.content}
              selected={selected}
              onBlur={() => setIsEditing(false)}
            />
          ) : (
            <Preview
              content={data?.content}
              onEdit={() => setIsEditing(true)}
            />
          )}
        </div>
        <Handle type="source" position={Position.Right} />
        <Handle type="target" position={Position.Left} />
        {/* <NodeResizer minWidth={200} minHeight={100} isVisible={selected} /> */}
        {selected && (
          <Panel position="bottom-center">
            <div className=" -translate-x-[15px] translate-y-[80%] h-[20px] w-96">
              <NodeToolBar id={id} />
            </div>
          </Panel>
        )}
        {data?.executionState === "running" && (
          <ShineBorder
            borderWidth={3}
            shineColor={["#A07CFE", "#FE8FB5", "#FFBE7B"]}
          />
        )}
      </div>
    </>
  );
};
