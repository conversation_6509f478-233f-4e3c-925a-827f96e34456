import { memo, useState, forwardRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SketchPicker } from "react-color";
import { useNodeDelete } from "@/components/flow/hooks/node-delete.ts";
import { useNodeColor } from "@/components/flow/hooks/node-color.ts";
import { useNodeChat } from "@/components/flow/hooks/node-chat.ts";
import { useEditNodeStore } from "@/store/workerspace-store/edit-node-store";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { trim } from "lodash-es";
import { NotebookHoverSelector, useNotebookHoverSelector } from "@/components/shared/notebook";
import { useReactFlow } from "@xyflow/react";
import { get } from "lodash-es";
import { getNodeHighlight, usePdfStore } from "@/store/pdf-store";

interface ToolButtonProps {
  icon: React.ReactNode;
  tooltip: string;
  onClick: (e: React.MouseEvent) => void;
  onMouseEnter?: (e: React.MouseEvent) => void;
  onMouseLeave?: (e: React.MouseEvent) => void;
  className?: string;
}

const ToolButton = memo(
  forwardRef<HTMLButtonElement, ToolButtonProps>(
    ({ icon, tooltip, onClick, onMouseEnter, onMouseLeave, className }, ref) => (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            ref={ref}
            variant="ghost"
            size="sm"
            className={cn(
              "h-4 w-4 p-[2px] rounded-full bg-[#F1F1F1] hover:bg-[#F0F0FA] hover:text-[#6E6BEE] text-[#979797]",
              className
            )}
            onClick={onClick}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
          >
            {icon}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    )
  )
);

interface NodeToolBarProps {
  className?: string;
  id: string;
  color?: string;
  content?: any;
  tools?: {
    delete?: boolean;
    chat?: boolean;
    notebook?: boolean;
    edit?: boolean;
    color?: boolean;
  };
}

export const NodeToolBar = memo(
  ({
    className,
    id,
    color,
    content,
    tools = {
      delete: true,
      chat: true,
      notebook: true,
      edit: true,
      color: true,
    },
  }: NodeToolBarProps) => {
    // 颜色列表
    const colorList = [
      "#FFDF7E",
      "#FFAB7E",
      "#B692FC",
      "#03D2FC",
      "#DAF329",
      "#fff",
    ];
    // 是否显示颜色列表
    const [showColorList, setShowColorList] = useState(false);
    // 是否显示颜色选择器面板
    const [showColorPicker, setShowColorPicker] = useState(false);
    // 编辑节点弹窗状态
    const { openEditNode } = useEditNodeStore();
    // 删除节点
    const { handleDeleteNode } = useNodeDelete();
    // 选择颜色
    const { handleColorChange } = useNodeColor();
    // AI聊天
    const { handleChat } = useNodeChat();
    // React Flow 实例
    const { getNodes } = useReactFlow();
    
    // 悬停式笔记本选择器
    const {
      selectorVisible,
      selectorPosition,
      showSelector,
      hideSelector,
      startHideSelector,
      cancelHideSelector,
      selectNotebook,
      quickCopyToLastNotebook,
      lastSelectedNotebook
    } = useNotebookHoverSelector({
      generateContent: () => {
        // 获取节点数据
        const allNodes = getNodes();
        const node = allNodes.find(n => n.id === id);
        if (!node) return '';

        const nodeTitle = trim(get(node, 'data.title') as string || '');
        const nodeContent = trim(get(node, 'data.content') as string || '');
        
        let content = '';
        if (nodeTitle && nodeTitle.length > 0) {
          content += `\n## ${nodeTitle}\n`;
        }
        if (nodeContent && nodeContent.length > 0) {
          content += `\n${nodeContent}\n `;
        }
        
        // 获取高亮信息
        const highlights = getNodeHighlight([id]);
        const highlight = highlights.find(h => h.nid === id);
        if (highlight) {
          const pdfFilename = usePdfStore.getState().pdfs.get(highlight.aid)?.filename || '';
          const pageNumber = highlight.position.boundingRect?.pageNumber;
          if (pageNumber) {
            content += `（来源：《${pdfFilename}》 第${pageNumber}页 ）`;
          } else {
            content += `（来源：《${pdfFilename}》）`;
          }
        } else {
          content += `\n (来源: 引用自节点) `;
        }

        return content;
      }
    });
    return (
      <TooltipProvider>
        <div
          className={cn(
            "z-20 absolute bottom-0 right-0 w-full translate-y-1/2 bg-transparent",
            className
          )}
        >
          <div className="node-toolbar w-full relative">
            <div className="w-full inline-flex justify-between items-center gap-2.5 p-2 rounded-md text-white cursor-pointer bg-transparent px-[12px]">
              {/* 删除按钮 */}
              {tools.delete && (
                <ToolButton
                  icon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                    >
                      <path
                        d="M9 4.5L8.5801 8.69901C8.51647 9.33526 8.48466 9.65339 8.34025 9.89375C8.21306 10.1055 8.02601 10.2747 7.8027 10.3802C7.54917 10.5 7.22945 10.5 6.59003 10.5H5.40997C4.77055 10.5 4.45083 10.5 4.1973 10.3802C3.97399 10.2747 3.78694 10.1055 3.65975 9.89375C3.51534 9.65339 3.48353 9.33526 3.4199 8.69901L3 4.5"
                        stroke="currentColor"
                        strokeWidth="0.8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M6.75 7.75V5.25"
                        stroke="currentColor"
                        strokeWidth="0.8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M5.25 7.75V5.25"
                        stroke="currentColor"
                        strokeWidth="0.8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2.25 3.25H4.55769M4.55769 3.25L4.75056 1.91424C4.80674 1.67079 5.00866 1.5 5.2403 1.5H6.75971C6.99134 1.5 7.19326 1.67079 7.24944 1.91424L7.44231 3.25M4.55769 3.25H7.44231M7.44231 3.25H9.75"
                        stroke="currentColor"
                        strokeWidth="0.8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  }
                  tooltip="删除节点"
                  onClick={(e) => {
                    handleDeleteNode([id]);
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                />
              )}

              {/* 工具按钮组 */}
              <div className="flex items-center gap-[3px]">
                {/* 颜色预览框 */}
                {tools.color && (
                  <div
                    className="relative overflow-hidden color-select"
                    onMouseEnter={() => setShowColorList(true)}
                    onMouseLeave={() => setShowColorList(false)}
                  >
                    {/* 历史颜色选择区域 */}
                    <div className="flex justify-around items-center">
                      {showColorList && (
                        <div
                          className="flex items-center gap-1 mr-1"
                          style={{
                            animation: `${
                              showColorList
                                ? "slideOutFromRight"
                                : "slideInToRight"
                            } 0.3s ease-in-out forwards`,
                          }}
                        >
                          {colorList.map((historyColor, index) => (
                            <div
                              key={index}
                              className="w-5 h-5 rounded-full border border-[##D8D8D8] hover:border-[#6E6BEE] cursor-pointer"
                              style={{ backgroundColor: historyColor }}
                              onClick={(e) => {
                                handleColorChange(id, historyColor);
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                            />
                          ))}
                        </div>
                      )}
                      <ToolButton
                        icon={
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="13"
                            height="13"
                            viewBox="0 0 13 13"
                            fill="none"
                          >
                            <g clipPath="url(#clip0_115_2762)">
                              <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M9.67609 9.67625C10.2328 9.67625 10.6841 9.22494 10.6841 8.66825C10.6841 8.2971 10.3481 7.7931 9.67609 7.15625C9.00408 7.7931 8.66809 8.2971 8.66809 8.66825C8.66809 9.22494 9.1194 9.67625 9.67609 9.67625Z"
                                fill="currentColor"
                              />
                              <path
                                d="M5.25452 1L6.23358 1.97908"
                                stroke="currentColor"
                                strokeWidth="0.830774"
                                strokeLinecap="round"
                              />
                              <path
                                d="M6.03803 1.7832L1.7301 6.09115L4.86315 9.22419L9.17107 4.91624L6.03803 1.7832Z"
                                stroke="currentColor"
                                strokeWidth="0.830774"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M2.80286 5.03418L7.49977 6.57842"
                                stroke="currentColor"
                                strokeWidth="0.830774"
                                strokeLinecap="round"
                              />
                              <path
                                d="M1.36011 11.1875H11.4401"
                                stroke="currentColor"
                                strokeWidth="0.756"
                                strokeLinecap="round"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_115_2762">
                                <rect
                                  width="12.096"
                                  height="12.096"
                                  fill="white"
                                  transform="translate(0.352051 0.351562)"
                                />
                              </clipPath>
                            </defs>
                          </svg>
                        }
                        tooltip="选择颜色"
                        onClick={(e) => {
                          setShowColorPicker(true);
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                      />
                    </div>
                  </div>
                )}
                {tools.chat && (
                  <ToolButton
                    icon={
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                      >
                        <g
                          fill="none"
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="1.5"
                        >
                          <path d="M14.17 20.89c4.184-.277 7.516-3.657 7.79-7.9c.053-.83.053-1.69 0-2.52c-.274-4.242-3.606-7.62-7.79-7.899a33 33 0 0 0-4.34 0c-4.184.278-7.516 3.657-7.79 7.9a20 20 0 0 0 0 2.52c.1 1.545.783 2.976 1.588 4.184c.467.845.159 1.9-.328 2.823c-.35.665-.526.997-.385 1.237c.14.24.455.248 1.084.263c1.245.03 2.084-.322 2.75-.813c.377-.279.566-.418.696-.434s.387.09.899.3c.46.19.995.307 1.485.34c1.425.094 2.914.094 4.342 0" />
                          <path d="m7.5 15l1.842-5.526a.694.694 0 0 1 1.316 0L12.5 15m3-6v6m-7-2h3" />
                        </g>
                      </svg>
                    }
                    tooltip="AI聊天"
                    onClick={(e) => {
                      if (trim(content)) {
                        handleChat(id);
                        e.preventDefault();
                        e.stopPropagation();
                      } else {
                        toast.error("当前节点为空");
                      }
                    }}
                  />
                )}
                {tools.notebook && (
                  <ToolButton
                    icon={
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 14 14"
                        fill="none"
                      >
                        <path
                          d="M5.40006 3.26686H4.33339C4.05049 3.26686 3.77918 3.37924 3.57914 3.57928C3.3791 3.77932 3.26672 4.05063 3.26672 4.33353V10.7335C3.26672 11.0164 3.3791 11.2877 3.57914 11.4878C3.77918 11.6878 4.05049 11.8002 4.33339 11.8002H9.66672C9.94962 11.8002 10.2209 11.6878 10.421 11.4878C10.621 11.2877 10.7334 11.0164 10.7334 10.7335V4.33353C10.7334 4.05063 10.621 3.77932 10.421 3.57928C10.2209 3.37924 9.94962 3.26686 9.66672 3.26686H8.60006M5.40006 3.26686C5.40006 2.98396 5.51244 2.71265 5.71248 2.51261C5.91251 2.31258 6.18383 2.2002 6.46672 2.2002H7.53339C7.81629 2.2002 8.0876 2.31258 8.28764 2.51261C8.48768 2.71265 8.60006 2.98396 8.60006 3.26686M5.40006 3.26686C5.40006 3.54976 5.51244 3.82107 5.71248 4.02111C5.91251 4.22115 6.18383 4.33353 6.46672 4.33353H7.53339C7.81629 4.33353 8.0876 4.22115 8.28764 4.02111C8.48768 3.82107 8.60006 3.54976 8.60006 3.26686M5.40006 7.0002H8.60006M5.40006 9.13353H8.60006"
                          stroke="currentColor"
                          strokeWidth="0.8"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    }
                    tooltip={lastSelectedNotebook ? `点击快速复制到"${lastSelectedNotebook.file_name}"，悬停查看所有笔记本` : "复制到笔记本"}
                    onClick={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (trim(content)) {
                        await quickCopyToLastNotebook();
                      } else {
                        toast.error("当前节点为空");
                      }
                    }}
                    onMouseEnter={(e) => {
                      if (trim(content)) {
                        cancelHideSelector(); // 取消任何延迟隐藏
                        const target = e.currentTarget;
                        const rect = target.getBoundingClientRect();
                        
                        // 使用 Portal 渲染到 body，所以位置计算基于视窗坐标
                        // 悬浮框显示在按钮上方，偏移避免超出边界
                        const mockEvent = {
                          currentTarget: {
                            getBoundingClientRect: () => ({
                              top: rect.top - 10, // 偏移
                              left: Math.max(rect.left + 0, 10), // 偏移
                              bottom: rect.top + 10,
                              right: rect.right,
                              width: 200,
                              height: 200
                            })
                          }
                        } as any;
                        showSelector(mockEvent);
                      }
                    }}
                    onMouseLeave={startHideSelector}
                    className="notebook-button"
                  />
                )}
                {/* 编辑按钮 */}
                {tools.edit && (
                  <ToolButton
                    icon={
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                      >
                        <path
                          d="M1.91992 10.0801H9.59992"
                          stroke="currentColor"
                          strokeWidth="0.72"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M2.71976 6.33033C2.51502 6.53534 2.40002 6.81324 2.40002 7.10299V8.64043H3.94687C4.23689 8.64043 4.51503 8.52517 4.72006 8.32002L9.28 3.75746C9.7067 3.33052 9.7067 2.6385 9.28 2.21156L8.82954 1.76084C8.40246 1.33351 7.70979 1.33364 7.28287 1.76113L2.71976 6.33033Z"
                          stroke="currentColor"
                          strokeWidth="0.72"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    }
                    tooltip="编辑节点"
                    onClick={(e) => {
                      openEditNode(id);
                      // TODO: 编辑节点
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  />
                )}
              </div>
            </div>
            {/* 颜色选择器面板 */}
            {showColorPicker && (
              <div className="absolute z-[2000] top-full right-0 nodrag">
                <SketchPicker
                  color={color}
                  onChange={(color) => {
                    handleColorChange(id, color.hex);
                    setShowColorPicker(false);
                  }}
                />
              </div>
            )}
            
            {/* 悬停式笔记本选择器 */}
            <NotebookHoverSelector
              position={selectorPosition}
              visible={selectorVisible}
              onSelect={selectNotebook}
              onClose={hideSelector}
              onStartHide={startHideSelector}
              onCancelHide={cancelHideSelector}
            />
          </div>
        </div>
      </TooltipProvider>
    );
  }
);
