import { NodeResizer } from "@xyflow/react";
import { ReactNode } from "react";
// TODO: 暂时不需要
type Props = {
  children: ReactNode;
  id: string;
  data?: Record<string, unknown> & {
    model?: string;
    source?: string;
    generated?: object;
  };
  title: string;
  type: string;
  toolbar?: {
    tooltip?: string;
    children: ReactNode;
  }[];
  className?: string;
  selected?: boolean;
};
export const NodeLayout = ({
  children,
  id,
  data,
  title,
  type,
  toolbar,
  className,
  selected,
}: Props) => {
  return (
    <>
      <NodeResizer isVisible={selected} minWidth={100} minHeight={30} />
      <div>
        <div>{title}</div>
        <div>{type}</div>
      </div>
      <div>{children}</div>
    </>
  );
};
/**
 * 自适应高度
 * 1. 添加折叠
 * 2. 展开时内容自适应高度
 * 3. 在选中时，根据自适应后的高度，更新节点的高度，来重制手动控制节点的大小的 resize 的高度，感觉冲突啊。
 * 
 */
