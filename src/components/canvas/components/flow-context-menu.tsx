import { forwardRef, useState, useCallback } from "react";
import { ContextMenuType } from "../hooks/use-canvas-context";
import { useReactFlow } from "@xyflow/react";
import TagSearch from "@/components/tag-management/tag-search.tsx";
import { message } from "antd";
import { useAddNode } from "@/components/flow/hooks/node-add";
import { ChevronRight } from "lucide-react";
import { createNode } from "../lib/node-factory";
import { NODE_TYPE_MAPS } from "../lib/node-types";

// 简单的 ContextMenuItem 组件
interface ContextMenuItemProps {
  disabled?: boolean;
  onClick?: () => void;
  children?: React.ReactNode;
  separator?: boolean;
}

const ContextMenuItem = ({
  disabled = false,
  onClick,
  children,
  separator = false,
}: ContextMenuItemProps) => {
  if (separator) {
    return (
      <div
        className="flow-context-menu-item"
        style={{ borderTop: "1px solid #eee", margin: "4px 0" }}
      />
    );
  }

  return (
    <div
      className={`flow-context-menu-item${disabled ? " disabled" : ""}`}
      style={{
        padding: "8px 16px",
        cursor: disabled ? "not-allowed" : "pointer",
        color: disabled ? "#bbb" : undefined,
      }}
      onClick={disabled ? undefined : onClick}
    >
      {children}
    </div>
  );
};

// 支持二级菜单的菜单项组件
interface SubMenuContextMenuItemProps {
  disabled?: boolean;
  children?: React.ReactNode;
  subMenuItems?: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
  }[];
}

const SubMenuContextMenuItem = ({
  disabled = false,
  children,
  subMenuItems = [],
}: SubMenuContextMenuItemProps) => {
  const [showSubMenu, setShowSubMenu] = useState(false);

  return (
    <div
      className={`flow-context-menu-item${disabled ? " disabled" : ""}`}
      style={{
        padding: "8px 16px",
        cursor: disabled ? "not-allowed" : "pointer",
        color: disabled ? "#bbb" : undefined,
        position: "relative",
      }}
      onMouseEnter={() => !disabled && setShowSubMenu(true)}
      onMouseLeave={() => setShowSubMenu(false)}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {children}
        <ChevronRight size={16} />
      </div>

      {showSubMenu && !disabled && (
        <div
          className="flow-context-submenu"
          style={{
            position: "absolute",
            left: "100%",
            top: 0,
            background: "#fff",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            padding: "8px 0",
            minWidth: 150,
            zIndex: 10000,
          }}
        >
          {subMenuItems.map((item, index) => (
            <div
              key={index}
              className={`flow-context-menu-item${item.disabled ? " disabled" : ""}`}
              style={{
                padding: "8px 16px",
                cursor: item.disabled ? "not-allowed" : "pointer",
                color: item.disabled ? "#bbb" : undefined,
              }}
              onClick={item.disabled ? undefined : item.onClick}
            >
              {item.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface FlowContextMenuProps {
  contextMenuState: ContextMenuType | null;
  onClick?: React.MouseEventHandler<HTMLDivElement> | (() => void);
  operations?: any; // 暂时使用 any，后续可以定义具体类型
}

const FlowContextMenu = forwardRef<HTMLDivElement, FlowContextMenuProps>(
  ({ contextMenuState, operations, ...props }, ref) => {
    const { getNodes, updateNodeData, addNodes } = useReactFlow();
    const { addNodeFromFree } = useAddNode();

    // 标签管理弹窗状态
    const [tagModalState, setTagModalState] = useState<{
      visible: boolean;
      nodeId?: string;
      node?: Record<any, any>;
    }>({ visible: false });

    // 使用传入的操作方法，如果没有则创建空方法
    const handleAddFreeNode = () => {
      addNodeFromFree({
        position: {
          x: contextMenuState?.canvasX ?? 0,
          y: contextMenuState?.canvasY ?? 0,
        },
        data: {
          content: "",
        },
      });
    };

    // 添加大模型节点
    const handleAddLLMNode = () => {
      
      const newNode = createNode(NODE_TYPE_MAPS.llm, {
        x: contextMenuState?.canvasX ?? 0,
        y: contextMenuState?.canvasY ?? 0,
      });
      addNodes([newNode]);
    };

    // 添加结束节点
    const handleAddEndNode = () => {
      const newNode = createNode(NODE_TYPE_MAPS.llmEndNode, {
        x: contextMenuState?.canvasX ?? 0,
        y: contextMenuState?.canvasY ?? 0,
      });
      addNodes([newNode]);
    };
    // 添加提问节点
    const handleAddAskAnyNode = () => {
      const newNode = createNode(NODE_TYPE_MAPS.askAnyNode, {
        x: contextMenuState?.canvasX ?? 0,
        y: contextMenuState?.canvasY ?? 0,
      });
      addNodes([newNode]);
    };
    // 添加输出节点
    const handleAddOutputNode = () => {
      const newNode = createNode(NODE_TYPE_MAPS.llmOutput, {
        x: contextMenuState?.canvasX ?? 0,
        y: contextMenuState?.canvasY ?? 0,
      });
      addNodes([newNode]);
    };

    // const handleAddSubTopic = operations?.handleAddSubTopic || (() => {});
    const handleAddSubTopic = operations?.handleAddSubTopNode || (() => {});

    const handleGenerateSummary =
      operations?.handleGenerateSummary || (() => {});
    const handleToChat = operations?.handleToChat || (() => {});
    const handleToNotePad = operations?.handleToNotePad || (() => {});
    const handleClearNodeContent =
      operations?.handleClearNodeContent || (() => {});
    const handleDelete = operations?.handleDelete || (() => {});
    const handleCreateGroup = operations?.handleCreateGroup || (() => {});

    // 标签管理弹窗处理函数
    const handleTagModalCancel = useCallback(() => {
      setTagModalState({ visible: false });
    }, []);

    const handleTagModalOk = useCallback(
      (selectedTags: string[]) => {
        // 这里应该调用API保存节点的标签
        console.log("节点标签已更新:", {
          nodeId: tagModalState.nodeId,
          selectedTags,
        });

        // 更新节点数据中的标签
        if (tagModalState.nodeId) {
          updateNodeData(tagModalState.nodeId, {
            tags: selectedTags,
          });
        }

        setTagModalState({ visible: false });
        message.success("标签已更新");
      },
      [tagModalState.nodeId, tagModalState.node]
    );

    // 标签管理处理
    const handleTagManagement = useCallback(() => {
      const selectedNodes = getNodes().filter((node) => node.selected);
      if (selectedNodes.length === 0) {
        message.error("当前节点为空");
        return;
      }

      // 如果有选中节点，使用第一个节点
      const firstNode = selectedNodes[0];
      setTagModalState({
        visible: true,
        nodeId: firstNode.id,
        node: firstNode,
      });
    }, [getNodes]);

    // 呼出菜单时看看有没有选中
    // const selectedNodes = useMemo(() => {
    //   if (contextMenuState) {
    //     return getNodes().filter((node) => node.selected).length;
    //   }
    //   return [];
    // }, [contextMenuState]);

    return (
      <>
        <div
          ref={ref}
          className="flow-context-menu"
          style={{
            display: contextMenuState ? "block" : "none",
            position: "fixed",
            left: contextMenuState?.position.left,
            top: contextMenuState?.position.top,
            zIndex: 9999,
            background: "#fff",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            padding: "8px 0",
            minWidth: 180,
          }}
          onContextMenu={(e) => e.preventDefault()}
          onClick={(e) => e?.stopPropagation()}
          onMouseDown={(e) => e?.stopPropagation()}
          {...props}
        >
          <style>{`
            .flow-context-menu-item {
                transition: background 0.15s;
            }
            .flow-context-menu-item:not(.disabled):hover {
                background: #f0f3fa;
            }
            .flow-context-menu-item.disabled {
                pointer-events: none;
            }
            .flow-context-submenu .flow-context-menu-item:hover {
                background: #f0f3fa;
            }
        `}</style>
          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "pane" &&
              contextMenuState?.type !== "edge"
            }
            onClick={handleAddFreeNode}
          >
            插入自由节点
          </ContextMenuItem>

          <ContextMenuItem
            disabled={contextMenuState?.type !== "node"}
            onClick={handleAddSubTopic}
          >
            插入子主题
          </ContextMenuItem>
          <ContextMenuItem
            disabled={contextMenuState?.type !== "selection"}
            onClick={handleGenerateSummary}
          >
            生成概括节点
          </ContextMenuItem>
          <SubMenuContextMenuItem
            disabled={
              contextMenuState?.type !== "pane" &&
              contextMenuState?.type !== "edge"
            }
            subMenuItems={[
              {
                label: "大模型节点",
                onClick: handleAddLLMNode,
              },
              {
                label: "结束节点",
                onClick: handleAddEndNode,
              },
              {
                label: "输出节点",
                onClick: handleAddOutputNode,
              },
              {
                label: "提问节点",
                onClick: handleAddAskAnyNode,
              },
            ]}
          >
            插入模型节点
          </SubMenuContextMenuItem>
          <ContextMenuItem separator />
          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "selection" &&
              contextMenuState?.type !== "node"
            }
            onClick={handleToChat}
          >
            ToChat
          </ContextMenuItem>
          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "selection" &&
              contextMenuState?.type !== "node"
            }
            onClick={handleToNotePad}
          >
            ToNotePad
          </ContextMenuItem>
          <ContextMenuItem>一键编制</ContextMenuItem>
          <ContextMenuItem>导出为png</ContextMenuItem>
          <ContextMenuItem disabled>复制</ContextMenuItem>
          <ContextMenuItem disabled>剪切</ContextMenuItem>
          <ContextMenuItem disabled>粘贴</ContextMenuItem>
          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "node" &&
              contextMenuState?.type !== "selection"
            }
            onClick={handleTagManagement}
          >
            标签管理
          </ContextMenuItem>

          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "node" &&
              contextMenuState?.type !== "selection"
            }
            onClick={handleClearNodeContent}
          >
            清空节点内容
          </ContextMenuItem>
          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "node" &&
              contextMenuState?.type !== "selection"
            }
            onClick={handleDelete}
          >
            删除
          </ContextMenuItem>
          <ContextMenuItem
            disabled={
              contextMenuState?.type !== "selection" &&
              contextMenuState?.type !== "node"
            }
            onClick={handleCreateGroup}
          >
            创建组
          </ContextMenuItem>
        </div>
        {tagModalState.visible && <TagSearch onClose={handleTagModalCancel} />}
      </>
    );
  }
);

FlowContextMenu.displayName = "FlowContextMenu";

export default FlowContextMenu;
