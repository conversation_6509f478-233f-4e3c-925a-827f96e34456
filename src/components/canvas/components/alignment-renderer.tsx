import React, { useMemo } from 'react';
import { useStore } from '@xyflow/react';
import { AlignmentGuide } from '../hooks/use-alignment-system';

interface AlignmentRendererProps {
  guides: AlignmentGuide[];
  fadeOut?: boolean;
}

/**
 * 高性能对齐指导线渲染组件
 * 使用 SVG 和 CSS 动画提供流畅的视觉反馈
 */
export const AlignmentRenderer: React.FC<AlignmentRendererProps> = ({ 
  guides, 
  fadeOut = false 
}) => {
  // 获取画布变换信息（缩放和平移）
  const transform = useStore((state) => state.transform);
  const [x, y, zoom] = transform;

  // 优化：只在 guides 变化时重新计算渲染数据
  const renderData = useMemo(() => {
    return guides.map(guide => {
      const isVertical = guide.orientation === 'vertical';
      
      // 根据画布变换计算实际渲染位置
      const transformedPosition = guide.position * zoom + (isVertical ? x : y);
      
      // 根据对齐类型选择颜色
      const getGuideColor = (type: string, strength: number): string => {
        const alpha = Math.min(0.8, strength);
        switch (type) {
          case 'center':
          case 'middle':
            return `rgba(99, 102, 241, ${alpha})`; // 紫色 - 中心对齐
          case 'adjacent':
            return `rgba(16, 185, 129, ${alpha})`; // 绿色 - 相邻对齐
          default:
            return `rgba(156, 163, 175, ${alpha})`; // 浅灰色 - 边缘对齐
        }
      };

      return {
        id: guide.id,
        isVertical,
        position: transformedPosition,
        color: getGuideColor(guide.type, guide.strength),
        strength: guide.strength,
        // 固定线条宽度为1px
        strokeWidth: 1
      };
    });
  }, [guides, x, y, zoom]);

  // 如果没有指导线，不渲染任何内容
  if (renderData.length === 0) {
    return null;
  }

  return (
    <svg 
      className={`alignment-guides ${fadeOut ? 'fade-out' : ''}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10,
        transition: fadeOut ? 'opacity 0.2s ease-out' : 'none',
        opacity: fadeOut ? 0 : 1
      }}
    >
      {renderData.map(({ id, isVertical, position, color, strokeWidth }) => (
        <g key={id}>
          {/* 主要指导线 */}
          <line
            className="alignment-guide-line"
            x1={isVertical ? position : 0}
            y1={isVertical ? 0 : position}
            x2={isVertical ? position : '100%'}
            y2={isVertical ? '100%' : position}
            stroke={color}
            strokeWidth={strokeWidth}
            strokeDasharray="4 4"
            style={{
              animation: 'alignmentPulse 1.5s ease-in-out infinite'
            }}
          />
          
          {/* 发光效果 */}
          <line
            className="alignment-guide-glow"
            x1={isVertical ? position : 0}
            y1={isVertical ? 0 : position}
            x2={isVertical ? position : '100%'}
            y2={isVertical ? '100%' : position}
            stroke={color}
            strokeWidth={2}
            strokeDasharray="4 4"
            opacity="0.2"
            filter="blur(1px)"
            style={{
              animation: 'alignmentPulse 1.5s ease-in-out infinite'
            }}
          />
        </g>
      ))}
      
      {/* CSS 动画定义 */}
      <defs>
        <style>
          {`
            @keyframes alignmentPulse {
              0% { opacity: 0.6; }
              50% { opacity: 1; }
              100% { opacity: 0.6; }
            }
            
            .alignment-guide-line {
              transition: stroke-width 0.1s ease;
            }
            
            .alignment-guides.fade-out {
              opacity: 0;
            }
          `}
        </style>
      </defs>
    </svg>
  );
};

export default AlignmentRenderer;