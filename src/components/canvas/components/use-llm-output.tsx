import { createOpenAI } from "@ai-sdk/openai";
import { generateText, streamText, smoothStream } from "ai";
import { useCallback, useState } from "react";
import { useWorkflowStatus, useWorkflowStore } from "@/store/workflow-store";
import { useResources } from "@/store/resource-store";
import { find, flatten, get, map } from "lodash-es";

const isLocalDev = import.meta.env.VITE_LLM_MODEL === "deepseek";

export interface LLMOutputConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  stream?: boolean;
  provider?: "chatanywhere" | "deepseek";
}

export interface LLMOutputResult {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  error?: string;
}

export const useLLMOutput = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<LLMOutputResult | null>(null);
  const [streamingContent, setStreamingContent] = useState<string>("");
  const status = useWorkflowStatus();
  const setIdle = useWorkflowStore((state) => state.setIdle);

  const generateLLMOutput = useCallback(
    async (
      prompts: string[],
      config: LLMOutputConfig = {},
      onStreamUpdate?: (content: string, end?: boolean) => void
    ): Promise<LLMOutputResult> => {
      setIsLoading(true);
      setError(null);

      try {
        // 合并所有提示词
        const combinedPrompt = prompts.filter(Boolean).join("\n\n");

        if (!combinedPrompt.trim()) {
          throw new Error("没有有效的输入内容");
        }

        // 默认配置
        const defaultConfig: LLMOutputConfig = {
          model: "gpt-3.5-turbo",
          temperature: 0.7,
          maxTokens: 1000,
          systemPrompt:
            config.systemPrompt ||
            "你是一个有用的AI助手，请根据用户提供的内容进行回答。",
          stream: false,
          provider: "chatanywhere",
        };

        const finalConfig = { ...defaultConfig, ...config };
        // 根据 provider 调整默认模型

        // 根据 provider 选择不同的客户端配置
        const getOpenAIClient = (provider: "chatanywhere" | "deepseek") => {
          switch (provider) {
            case "deepseek":
              return createOpenAI({
                apiKey:
                  import.meta.env.VITE_DEEPSEEK_KEY ||
                  "sk-your-deepseek-api-key",
                baseURL: "https://api.deepseek.com/v1", // DeepSeek 官方 API 端点
              });
            case "chatanywhere":
            default:
              return createOpenAI({
                apiKey: "sk-gZDo7qkWZAwEfrEJHBj3ZE6mO96uYU4xYl2wmRa4L1OewqZy",
                baseURL: "https://api.chatanywhere.tech/v1", // ChatAnywhere 第三方服务
              });
          }
        };
        // 目前不支持手动选择模型
        // 开发环境使用 deepsee
        const customOpenAI = getOpenAIClient(
          isLocalDev ? "deepseek" : "chatanywhere"
        );

        const messages = [
          {
            role: "system" as const,
            content: finalConfig.systemPrompt!,
          },
          {
            role: "user" as const,
            content: combinedPrompt,
          },
        ];

        let llmResult: LLMOutputResult;

        if (finalConfig.stream) {
          // 流式输出
          setStreamingContent("");

          const { textStream } = streamText({
            model: customOpenAI(isLocalDev ? "deepseek-chat" : "gpt-3.5-turbo"), // customOpenAI(finalConfig.model!),
            messages,
            temperature: finalConfig.temperature,
            maxTokens: finalConfig.maxTokens,
            // 启用平滑流式输出，适配中文字符
            experimental_transform: smoothStream({
              delayInMs: 30, // 每个字符间隔30ms，适合阅读节奏
              chunking: /[\u4E00-\u9FFF]|\S+\s+/, // 中文字符逐字显示，英文按词显示
            }),
          });

          let fullContent = "";
          for await (const textPart of textStream) {
            fullContent += textPart;
            // 如果提供了回调函数，使用回调函数；否则使用默认的状态更新
            if (onStreamUpdate) {
              onStreamUpdate(fullContent);
            } else {
              setStreamingContent(fullContent);
            }
          }
          // 流式结束
          if (onStreamUpdate) {
            onStreamUpdate(fullContent, true);
          }

          llmResult = {
            content: fullContent,
            usage: {
              promptTokens: 0, // 流式模式下可能无法获取准确的token信息
              completionTokens: 0,
              totalTokens: 0,
            },
          };
        } else {
          // 非流式输出
          const result = await generateText({
            model: customOpenAI(isLocalDev ? "deepseek-chat" : "gpt-3.5-turbo"),
            messages,
            temperature: finalConfig.temperature,
            maxTokens: finalConfig.maxTokens,
          });

          llmResult = {
            content: result.text,
            usage: result.usage,
          };
        }

        setResult(llmResult);
        return llmResult;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "LLM 调用失败";
        setError(errorMessage);
        const errorResult = {
          content: "",
          error: errorMessage,
        };
        setResult(errorResult);
        return errorResult;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const executeWorkflow = useCallback(
    async (
      prompts: string[],
      config?: LLMOutputConfig,
      onStreamUpdate?: (content: string, end?: boolean) => void
    ) => {
      if (status !== "running") {
        return null;
      }

      if (prompts.length === 0) {
        setError("没有找到有效的输入数据");
        return null;
      }

      const result = await generateLLMOutput(prompts, config, onStreamUpdate);

      if (result.error) {
        setError(result.error);
        return null;
      }

      // 工作流执行完成，设置状态为空闲
      setIdle();

      return result;
    },
    [status, generateLLMOutput, setIdle]
  );

  return {
    isLoading,
    error,
    result,
    streamingContent,
    generateLLMOutput,
    executeWorkflow,
    clearError: () => setError(null),
    clearResult: () => setResult(null),
    clearStreamingContent: () => setStreamingContent(""),
  };
};
