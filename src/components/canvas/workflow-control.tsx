import { useWorkflowStatus, useWorkflowStore } from "@/store/workflow-store";
import { Button } from "../ui/button";
import { useLLMExecution } from "./use-llm-execution";
import { useCallback } from "react";
import { useReactFlow, type Edge, type Node } from "@xyflow/react";
import { forEach } from "lodash-es";
import { NODE_TYPE_MAPS } from "./components/node";

// 自动遍历 nodes 和 edges，找出 没有入边 的节点作为起点
// ！！需要考虑没有边的节点。
function findIsolatedAgentNodes(nodes: Node[], edges: Edge[]): string[] {
  const allConnectedIds = new Set(edges.flatMap((e) => [e.source, e.target]));

  return nodes
    .filter(
      (node) =>
        !allConnectedIds.has(node.id) && node.type === NODE_TYPE_MAPS.llm
    )
    .map((node) => node.id);
}
function findStartNodes(nodes: Node[], edges: Edge[]): string[] {
  const allTargetIds = new Set(edges.map((e) => e.target));
  const isolatedAgentNodes = findIsolatedAgentNodes(nodes, edges);

  return [...nodes.filter((n) => !allTargetIds.has(n.id)).map((n) => n.id)];
}

const WorkflowControl = () => {
  const status = useWorkflowStatus();
  const { runDataFlow } = useLLMExecution();
  const setRunning = useWorkflowStore((state) => state.setRunning);
  const { getNodes, getEdges } = useReactFlow();

  const handleStart = useCallback(() => {
    const nodes = getNodes();
    const edges = getEdges();
    const startNodes = findStartNodes(nodes, edges);
    forEach(startNodes, (startNodeId) => {
      runDataFlow(startNodeId, edges, nodes);
    });
  }, []);

  return (
    <div className="absolute top-10 right-10 flex gap-2 z-10">
      <Button
        type="button"
        variant="outline"
        disabled={status === "running"}
        onClick={handleStart}
      >
        开始
      </Button>
    </div>
  );
};

export default WorkflowControl;
