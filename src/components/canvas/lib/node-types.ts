import EditableEdge from "@/components/flow/edges/EditableEdge";

export const edgeTypes = {
  editableEdge: EditableEdge,
};


export type NodeType =
  | "llmOutput"
  | "groupNode"
  | "markdown"
  | "llm"
  | "drop"
  | "questionNode"
  | "askAnyNode"
  | "inputNode"
  | "confirmButtonNode"
  | "llmEndNode";

// 节点类型映射常量
export const NODE_TYPE_MAPS: Record<NodeType, NodeType> = {
  llmOutput: "llmOutput",
  groupNode: "groupNode",
  markdown: "markdown",
  llm: "llm",
  drop: "drop",
  questionNode: "questionNode",
  askAnyNode: "askAnyNode",
  inputNode: "inputNode",
  confirmButtonNode: "confirmButtonNode",
  llmEndNode: "llmEndNode",
} as const;
