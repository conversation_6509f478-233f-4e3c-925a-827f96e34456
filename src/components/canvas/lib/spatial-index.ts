import { Node } from "@xyflow/react";

/**
 * 节点边界信息
 */
export interface NodeBounds {
  nodeId: string;
  left: number;
  right: number;
  top: number;
  bottom: number;
  centerX: number;
  centerY: number;
  width: number;
  height: number;
}

/**
 * 空间分区
 */
interface Partition {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  nodes: NodeBounds[];
}

/**
 * 高性能空间索引系统
 * 使用网格分区算法优化节点查找性能，将复杂度从 O(n²) 降低到 O(log n)
 */
export class SpatialIndex {
  private partitions: Map<string, Partition> = new Map();
  private gridSize: number;
  private maxNodesPerPartition: number;

  constructor(gridSize = 500, maxNodesPerPartition = 20) {
    this.gridSize = gridSize;
    this.maxNodesPerPartition = maxNodesPerPartition;
  }

  /**
   * 获取分区键
   */
  private getPartitionKey(x: number, y: number): string {
    const gridX = Math.floor(x / this.gridSize);
    const gridY = Math.floor(y / this.gridSize);
    return `${gridX},${gridY}`;
  }

  /**
   * 获取节点可能跨越的所有分区键
   */
  private getRelevantPartitionKeys(bounds: NodeBounds): string[] {
    const keys: string[] = [];
    const startX = Math.floor(bounds.left / this.gridSize);
    const endX = Math.floor(bounds.right / this.gridSize);
    const startY = Math.floor(bounds.top / this.gridSize);
    const endY = Math.floor(bounds.bottom / this.gridSize);

    for (let x = startX; x <= endX; x++) {
      for (let y = startY; y <= endY; y++) {
        keys.push(`${x},${y}`);
      }
    }
    return keys;
  }

  /**
   * 添加节点到空间索引
   */
  addNode(bounds: NodeBounds): void {
    const partitionKeys = this.getRelevantPartitionKeys(bounds);

    partitionKeys.forEach(key => {
      let partition = this.partitions.get(key);
      if (!partition) {
        const [gridX, gridY] = key.split(',').map(Number);
        partition = {
          minX: gridX * this.gridSize,
          maxX: (gridX + 1) * this.gridSize,
          minY: gridY * this.gridSize,
          maxY: (gridY + 1) * this.gridSize,
          nodes: []
        };
        this.partitions.set(key, partition);
      }

      // 避免重复添加
      if (!partition.nodes.find(n => n.nodeId === bounds.nodeId)) {
        partition.nodes.push(bounds);
      }
    });
  }

  /**
   * 从空间索引中移除节点
   */
  removeNode(nodeId: string): void {
    this.partitions.forEach(partition => {
      partition.nodes = partition.nodes.filter(n => n.nodeId !== nodeId);
    });

    // 清理空分区
    const emptyKeys: string[] = [];
    this.partitions.forEach((partition, key) => {
      if (partition.nodes.length === 0) {
        emptyKeys.push(key);
      }
    });
    emptyKeys.forEach(key => this.partitions.delete(key));
  }

  /**
   * 查找指定范围内的节点
   */
  findNodesInRange(
    centerX: number,
    centerY: number,
    maxDistance: number,
    excludeNodeId?: string
  ): NodeBounds[] {
    const results: NodeBounds[] = [];
    const seenNodes = new Set<string>();

    // 计算搜索范围涉及的分区
    const minX = centerX - maxDistance;
    const maxX = centerX + maxDistance;
    const minY = centerY - maxDistance;
    const maxY = centerY + maxDistance;

    const startGridX = Math.floor(minX / this.gridSize);
    const endGridX = Math.floor(maxX / this.gridSize);
    const startGridY = Math.floor(minY / this.gridSize);
    const endGridY = Math.floor(maxY / this.gridSize);

    // 遍历相关分区
    for (let x = startGridX; x <= endGridX; x++) {
      for (let y = startGridY; y <= endGridY; y++) {
        const key = `${x},${y}`;
        const partition = this.partitions.get(key);
        
        if (partition) {
          partition.nodes.forEach(nodeBounds => {
            if (excludeNodeId && nodeBounds.nodeId === excludeNodeId) {
              return;
            }

            if (seenNodes.has(nodeBounds.nodeId)) {
              return;
            }

            // 粗略距离检查（使用曼哈顿距离快速筛选）
            const roughDistance = Math.abs(nodeBounds.centerX - centerX) + 
                                Math.abs(nodeBounds.centerY - centerY);
            
            if (roughDistance <= maxDistance * 1.5) { // 1.5倍容错
              seenNodes.add(nodeBounds.nodeId);
              results.push(nodeBounds);
            }
          });
        }
      }
    }

    return results;
  }

  /**
   * 重建整个索引
   */
  rebuild(nodes: NodeBounds[]): void {
    this.clear();
    nodes.forEach(node => this.addNode(node));
  }

  /**
   * 清空索引
   */
  clear(): void {
    this.partitions.clear();
  }

  /**
   * 获取索引统计信息
   */
  getStats(): {
    partitionCount: number;
    totalNodes: number;
    averageNodesPerPartition: number;
    maxNodesInPartition: number;
  } {
    let totalNodes = 0;
    let maxNodes = 0;

    this.partitions.forEach(partition => {
      totalNodes += partition.nodes.length;
      maxNodes = Math.max(maxNodes, partition.nodes.length);
    });

    return {
      partitionCount: this.partitions.size,
      totalNodes,
      averageNodesPerPartition: this.partitions.size > 0 ? totalNodes / this.partitions.size : 0,
      maxNodesInPartition: maxNodes
    };
  }
}