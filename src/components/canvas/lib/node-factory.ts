import { nanoid } from "nanoid";
import { DEFAULT_COLOR } from "./constants";
import { NODE_DEFAULTS } from "@/components/flow/constants/node-defaults";
import { NODE_TYPE_MAPS, NodeType } from "./node-types";
import type { XYPosition } from "@xyflow/react";

export type ExecutionState =
  | "idle"
  | "pending"
  | "running"
  | "success"
  | "error";
// LLM 节点输入字段 - 支持引用和数据集模式
export type InputFields = {
  id: string;
  paramName: string; // 变量名称（用户自定义）
  paramValueType: "reference" | "dataset"; // 引用模式 或 数据集模式
  paramValue: string; // 参数值：引用时为"节点ID.字段名"，数据集时为选择的数据源ID
  // 以下字段用于数据集模式
  sourceType?: string; // 数据源类型：nodes、pdfs、lexical
  nodeContent?: string; // 节点内容
  nodeTitle?: string; // 节点标题
};
export type OutputFields = {
  id?: string;
  name: string;
  aliasName: string;
};
export interface AINodeData {
  id?: string;
  title: string;
  content?: string; // 可以展示内容的节点必须要有 content，上游节点可控制当前节点的输出
  inputFields: InputFields[];
  outputFields: OutputFields[];
  prompts: any[];
  systemPrompt: string;
  userPrompt: string;
  executionState: ExecutionState;
  model: string;
  result?: string; // LLM 的输出，不确定是否需要单独的流式字段
  error?: string;
  // lastExecutionTime
  // 需要添加上游节点的执行状态
  // 整理 LLM 数据结构
  // TODO: 整理 DB 数据结构，最后回过来整理这里不然不好确定type
  // llmConfig:{
  //   inputFields:{
  //     id:string,
  //     name:string,
  //     sourceType:string, // 来源类型：来自引用和来自数据集
  //     type:string, // 这里的类型可以是节点类型或者是文件类型，比较杂乱 TODO: 先整理一下数据类型可能会更好点儿？
  //     content:string,  // 文件内容
  //   }
  // }
  [key: string]: unknown; // 添加索引签名以兼容 Record<string, unknown>
}

/**
 * 节点工厂函数 - 根据节点类型创建对应的节点实例
 *
 * @param nodeType - 节点类型，决定创建哪种类型的节点
 * @param position - 节点在画布上的位置坐标 {x, y}
 * @returns 返回创建的节点对象，包含id、type、position和特定的data数据
 * @throws 当节点类型为空或未知时抛出错误
 */

export function createNode(
  nodeType: NodeType,
  position: XYPosition,
  options?: { title?: string; isTemporary?: boolean; [key: string]: any }
) {
  if (!nodeType) {
    throw new Error("Node type is required");
  }
  options = options || {};

  // 为临时节点生成特殊的ID前缀，便于识别和批量删除
  const isTemporary = options.isTemporary || false;
  const nodeId = isTemporary ? `temp_${nanoid()}` : nanoid();

  switch (nodeType) {
    // 右键或者创建新节点时，选择节点类型的节点
    case NODE_TYPE_MAPS.drop:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.drop,
        position,
        data: {
          isSource: false, // TODO: 是否是源节点
          isTemporary,
          ...options,

        },
      };
    case NODE_TYPE_MAPS.markdown:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.markdown,
        position,
        data: {
          content: "",
          title: "未命名",
          color: DEFAULT_COLOR, // 默认颜色
          isTemporary,
          ...options,
        },
      };

    case NODE_TYPE_MAPS.groupNode:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.groupNode,
        position,
        width: NODE_DEFAULTS.WIDTH,
        height: NODE_DEFAULTS.MIN_HEIGHT,
        data: {
          label: "Group",
          isTemporary,
          ...options,
        },
      };

    case NODE_TYPE_MAPS.llm:
      const data: AINodeData = {
        title: "AI助手",
        model: "GPT-4",
        inputFields: [],
        outputFields: [],
        prompts: [],
        systemPrompt: "",
        userPrompt: "",
        executionState: "idle",
      };
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.llm,
        position,
        data: {
          ...data,
          ...options,
          isTemporary,
        },
      };

    case NODE_TYPE_MAPS.llmOutput:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.llmOutput,
        position,
        data: {
          title: options?.title || "LLM 输出节点",
          ...options,
        },
      };

    case NODE_TYPE_MAPS.questionNode:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.questionNode,
        position,
        data: {
          title: options?.title || "问题节点",
          content: "",
          color: DEFAULT_COLOR,
          isTemporary,
          ...options,
        },
      };

    case NODE_TYPE_MAPS.askAnyNode:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.askAnyNode,
        position,
        data: {
          isSource: false,
          content:
            "我是内容... 很长很长我是内容... 很长很长我是内容... 很长很长我是内容... 很长很长我是内容... 很长很长我是内容... 很长很长我是内容... 很长很长",
          isTemporary,
          ...options,
        },
      };

    case NODE_TYPE_MAPS.inputNode:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.inputNode,
        position,
        width: NODE_DEFAULTS.WIDTH,
        height: 60, // 输入框节点高度较小
        data: {
          placeholder: options?.title || "输入问题...",
          isTemporary,
          ...options,
        },
      };

    case NODE_TYPE_MAPS.confirmButtonNode:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.confirmButtonNode,
        position,
        data: {
          ...options,
          isTemporary,
        },
      };

    case NODE_TYPE_MAPS.llmEndNode:
      return {
        id: nodeId,
        type: NODE_TYPE_MAPS.llmEndNode,
        position,
        data: {
          title: options?.title || "结束节点",
          color: "#ef4444",
          isTemporary,
          ...options,
        },
      };

    default:
      throw new Error(`Unknown node type: ${nodeType}`);
  }
}
