import type { Node, Edge } from "@xyflow/react";

/**
 * 临时节点工具函数
 * 提供统一的临时节点和边的识别、筛选和删除功能
 */

/**
 * 检查节点是否为临时节点
 * @param node - 要检查的节点
 * @returns 如果是临时节点返回 true，否则返回 false
 */
export function isTemporaryNode(node: Node): boolean {
  // 方法1: 通过ID前缀识别
  if (node.id.startsWith('temp_')) {
    return true;
  }
  
  // 方法2: 通过data.isTemporary标识
  if (node.data?.isTemporary === true) {
    return true;
  }
  
  // 方法3: 通过特定类型识别（兼容现有逻辑）
  if (node.type === 'drop') {
    return true;
  }
  
  return false;
}

/**
 * 检查边是否为临时边
 * @param edge - 要检查的边
 * @returns 如果是临时边返回 true，否则返回 false
 */
export function isTemporaryEdge(edge: Edge): boolean {
  // 方法1: 通过ID前缀识别
  if (edge.id.startsWith('temp_')) {
    return true;
  }
  
  // 方法2: 通过data.isTemporary标识
  if (edge.data?.isTemporary === true) {
    return true;
  }
  
  // 方法3: 通过className识别（兼容现有逻辑）
  if (edge.className?.includes('temp')) {
    return true;
  }
  
  // 方法4: 通过type识别（兼容现有逻辑）
  if (edge.type === 'temporary') {
    return true;
  }
  
  return false;
}

/**
 * 从节点数组中筛选出非临时节点
 * @param nodes - 节点数组
 * @returns 筛选后的非临时节点数组
 */
export function filterOutTemporaryNodes(nodes: Node[]): Node[] {
  return nodes.filter(node => !isTemporaryNode(node));
}

/**
 * 从边数组中筛选出非临时边
 * @param edges - 边数组
 * @returns 筛选后的非临时边数组
 */
export function filterOutTemporaryEdges(edges: Edge[]): Edge[] {
  return edges.filter(edge => !isTemporaryEdge(edge));
}

/**
 * 从节点数组中筛选出临时节点
 * @param nodes - 节点数组
 * @returns 筛选后的临时节点数组
 */
export function filterTemporaryNodes(nodes: Node[]): Node[] {
  return nodes.filter(node => isTemporaryNode(node));
}

/**
 * 从边数组中筛选出临时边
 * @param edges - 边数组
 * @returns 筛选后的临时边数组
 */
export function filterTemporaryEdges(edges: Edge[]): Edge[] {
  return edges.filter(edge => isTemporaryEdge(edge));
}

/**
 * 创建临时边的ID
 * @param prefix - ID前缀，可选
 * @returns 带temp_前缀的临时边ID
 */
export function createTemporaryEdgeId(prefix?: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `temp_${prefix || 'edge'}_${timestamp}_${random}`;
}

/**
 * 批量删除所有临时节点和边
 * @param nodes - 当前所有节点
 * @param edges - 当前所有边
 * @returns 删除临时元素后的节点和边数组
 */
export function removeAllTemporaryElements(
  nodes: Node[], 
  edges: Edge[]
): { nodes: Node[]; edges: Edge[] } {
  return {
    nodes: filterOutTemporaryNodes(nodes),
    edges: filterOutTemporaryEdges(edges)
  };
}

/**
 * 检查特定源节点的临时节点数量
 * @param nodes - 节点数组
 * @param sourceNodeId - 源节点ID
 * @returns 与指定源节点相关的临时节点数量
 */
export function countTemporaryNodesFromSource(nodes: Node[], sourceNodeId: string): number {
  // 这里假设临时节点通过某种方式与源节点关联
  // 可以根据实际需求调整实现
  return filterTemporaryNodes(nodes).length;
}