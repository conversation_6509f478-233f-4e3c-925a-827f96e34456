import type { InternalNode, Node } from "@xyflow/react";

type Rect = {
  x: number;
  y: number;
  width: number;
  height: number;
};

type RelationResult = "contain" | "overlap" | "none";

export function getRectRelation(a: Rect, b: Rect): RelationResult {
  const ax1 = a.x;
  const ay1 = a.y;
  const ax2 = a.x + a.width;
  const ay2 = a.y + a.height;

  const bx1 = b.x;
  const by1 = b.y;
  const bx2 = b.x + b.width;
  const by2 = b.y + b.height;

  // 判断 B 是否包含 A
  const isContain = ax1 >= bx1 && ay1 >= by1 && ax2 <= bx2 && ay2 <= by2;

  if (isContain) return "contain";

  // 判断是否有交集（AABB 碰撞检测）
  const isOverlap = !(ax2 <= bx1 || ax1 >= bx2 || ay2 <= by1 || ay1 >= by2);

  if (isOverlap) return "overlap";

  // 否则完全无交集
  return "none";
}

// 获取坐标
export function getAbsolutePositions(nodes: InternalNode<Node>[]) {
  const allX: number[] = [];
  const allY: number[] = [];
  nodes.forEach((node) => {
    allX.push(node.internals.positionAbsolute?.x ?? 0);
    allY.push(node.internals.positionAbsolute?.y ?? 0);
  });
  return {
    allX,
    allY,
  };
}
