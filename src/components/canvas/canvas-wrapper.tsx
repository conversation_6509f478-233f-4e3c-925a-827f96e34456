import { Edge, Node, useReactFlow } from "@xyflow/react";
import { Canvas } from "./canvas";
import { useEffect, useRef, useState } from "react";
import { canvasService, workspaceService } from "@/local";
import { GetCanvasResp } from "@/local/services/canvas-service";
import { getWsId } from "@/tools/params";
import { useFetchData } from "../flow/hooks/fetch-data";
import { usePanelPosition } from "@/pages/provider/canvas-provider";
import { useMousePosition } from "../flow/hooks/mouse-position";
import WorkflowControl from "./workflow-control";
import { useSetResources } from "@/store/resource-store";
import { useReactFlowInstanceStore } from "@/store/reactflow-instance-store";
import BigSearch from "@/components/tag-management/big-search";

const CanvasWrapper = () => {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { flowPanelPosition } = usePanelPosition();
  const setResources = useSetResources();
  const { mousePosition, flowWrapperRef, handleMouseMove } = useMousePosition();
  const { setInstance, clearInstance } = useReactFlowInstanceStore();
  const [update, setUpdate]= useState<number>(9999)
  // TODO: 复制粘体逻辑再实现

  useEffect(() => {
    const wid = getWsId();
    // 获取wid并加载画布
    if (wid) {
      setIsLoading(true);
      setError(""); // 清除之前的错误
      canvasService
        .get({ wid })
        .then((res) => {
          try {
            const { content } = res as GetCanvasResp;
            const { nodes, edges } = JSON.parse(content);
            setNodes(nodes);
            setEdges(edges);
          } catch (error) {
            throw new Error("加载画布失败");
          }
        })
        .catch((error) => {
          console.log("加载画布失败", error);
          setError("加载画布失败");
        })
        .finally(() => {
          setIsLoading(false);
        });
      workspaceService.getFilesContentByWid(wid).then((res) => {
        setResources(res);
      });
    }

    setUpdate(Math.random())
  }, [setUpdate]); // 只在组件挂载时执行一次

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          {/* 旋转圆圈 */}
          <div className="relative">
            <div className="w-12 h-12 rounded-full border-4 border-gray-200"></div>
            <div className="w-12 h-12 rounded-full border-4 border-blue-500 border-t-transparent absolute top-0 left-0 animate-spin"></div>
          </div>
          {/* 加载文字 */}
          <div className="text-gray-600 text-sm font-medium">
            正在加载画布...
          </div>
          {/* 脉冲点 */}
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <div
              className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
              style={{ animationDelay: "0.2s" }}
            ></div>
            <div
              className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
              style={{ animationDelay: "0.4s" }}
            ></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex flex-col items-center gap-4 max-w-md text-center">
          {/* 错误图标 */}
          <div className="relative">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 relative">
                {/* X 图标 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-6 h-0.5 bg-red-500 rotate-45 absolute"></div>
                  <div className="w-6 h-0.5 bg-red-500 -rotate-45 absolute"></div>
                </div>
              </div>
            </div>
            {/* 错误动画波纹 */}
            <div className="absolute inset-0 w-16 h-16 bg-red-200 rounded-full animate-ping opacity-20"></div>
          </div>

          {/* 错误标题 */}
          <div className="text-red-600 text-lg font-semibold">加载失败</div>

          {/* 错误信息 */}
          <div className="text-gray-600 text-sm">{error}</div>

          {/* 重试按钮 */}
          <button
            onClick={() => {
              setError("");
              const wid = getWsId();
              if (wid) {
                setIsLoading(true);
                canvasService
                  .get({ wid })
                  .then((res) => {
                    try {
                      const { content } = res as GetCanvasResp;
                      const { nodes, edges } = JSON.parse(content);
                      setNodes(nodes);
                      setEdges(edges);
                    } catch (error) {
                      throw new Error("加载画布失败");
                    }
                  })
                  .catch((error) => {
                    setError("加载画布失败");
                  })
                  .finally(() => {
                    setIsLoading(false);
                  });

              }
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center gap-2"
          >
            {/* 重试图标 */}
            <div className="w-4 h-4 relative">
              <div className="absolute inset-0 border-2 border-white border-r-transparent rounded-full"></div>
              <div className="absolute top-0 right-0 w-2 h-2">
                <div className="w-0 h-0 border-l-2 border-b-2 border-white transform rotate-45 translate-x-0.5 translate-y-0.5"></div>
              </div>
            </div>
            重试
          </button>
        </div>
      </div>
    );
  }

  // CanvasWithViewport 组件需要在 ReactFlowProvider 内部使用 useViewport

  return (
    <div
      className="w-full h-full relative"
      ref={flowWrapperRef}
      style={{
        left: flowPanelPosition.x,
        width: flowPanelPosition.width,
      }}
    >
      <WorkflowControl />
      <BigSearch />
      <CanvasWithViewport nodes={nodes} edges={edges} key={update}/>
    </div>
  );
};
const CanvasWithViewport = ({
  nodes,
  edges,
}: {
  nodes: Node[];
  edges: Edge[];
}) => {
  const reactFlowInstance = useReactFlow();
  const { setInstance, clearInstance } = useReactFlowInstanceStore();
  useFetchData();

  // 设置 ReactFlow 实例到全局状态
  useEffect(() => {
    if (reactFlowInstance) {
      setInstance(reactFlowInstance);
    }
  }, [reactFlowInstance, setInstance]);
  useEffect(() => {
    return () => {
      clearInstance();
    };
  }, [clearInstance]);
  useEffect(() => {
    console.log("nodes", nodes);
    if (nodes.length) {
      const timer = setTimeout(() => {
        reactFlowInstance.fitView({
          padding: 0.1, // 10% 的边距
          includeHiddenNodes: false,
          minZoom: 0.1,
          maxZoom: 1.5,
          duration: 800, // 动画时长
        });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [nodes, reactFlowInstance]);

  return (
    <Canvas nodes={nodes} edges={edges}>
      <div />
    </Canvas>
  );
};

export default CanvasWrapper;
