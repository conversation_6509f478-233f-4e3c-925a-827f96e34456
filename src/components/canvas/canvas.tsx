import {
  ReactFlow,
  ReactFlowProps,
  MiniMap,
  Panel,
  type Node,
  useReactFlow,
} from "@xyflow/react";
import { useCallback, useEffect, useRef } from "react";
import { edgeTypes } from "./lib/node-types";
import { nodeTypes } from "./components/node";
import "@xyflow/react/dist/style.css";
import "./style/index.css";
import FlowContextMenu from "./components/flow-context-menu";
import { useEdgePopup } from "../flow/edges/use-edge-popup";
import ErrorBoundary from "@/components/error/ErrorBoundary";
import EdgePopup from "../flow/edges/EdgePopup";
import { EditNode } from "./components/node/EditNode";
import { FlowBar } from "../flow/FlowBar";
import { DevTools } from "../flow/DevTools";
// import AlignmentRenderer from "./components/alignment-renderer";
import {
  useCanvasState,
  useCanvasOperations,
  useCanvasInteractions,
  useCanvasKeyboard,
  useCanvasContext,
} from "./hooks";
import { useFlowSelection } from "../flow/hooks/flow-selection";
import { NodeEvents } from "@/local/services/node-service";
import { canvasService } from "@/local";
import { getWsId } from "@/tools/params";
import { useFlowStore } from "@/store/flow-store";
import { useTagStore } from "@/components/tag-management/stores/useTagStore";
import { LexicalJumpManager } from "@/components/lexicalEditor/utils/lexicalJumpUtils";

/**
 * Canvas 组件 - 基于 ReactFlow 的流程图画布
 *
 * 主要功能：
 * - 节点和边的管理
 * - 拖拽操作和连接
 * - 右键菜单和键盘快捷键
 * - 节点编辑和边弹框
 *
 * @param children - 子组件
 * @param props - ReactFlow 属性
 */
export const Canvas = ({ children, ...props }: ReactFlowProps) => {

  const {onSelectionChange} = useFlowSelection()
  const { setEditingEdgeId } = useFlowStore.getState();
  // 解构传入的属性
  const {
    onConnectEnd, // 连接结束回调
    onEdgesChange, // 边变更回调
    onNodesChange, // 节点变更回调
    nodes: initialNodes, // 初始节点数据
    edges: initialEdges, // 初始边数据
    ...rest // 其他 ReactFlow 属性
  } = props ?? {};

  // ==================== 节点点击处理 ====================
  
  /**
   * 处理节点点击事件
   * 当点击节点时，如果节点包含文本位置信息，则跳转到对应的 Lexical 编辑器
   */
  const handleNodeClick = useCallback((_: React.MouseEvent, node: Node) => {
    try {
      // 检查节点是否包含用于跳转的数据
      const nodeData = node.data;
      if (!nodeData) return;

      // 获取文件ID和文本信息
      const { aid, content, textPosition, color } = nodeData as any;
      
      if (aid && content) {
        // 尝试跳转到 Lexical 编辑器
        let jumpSuccess = false;

        // 只使用 Mark 高亮跳转（统一方案）
        jumpSuccess = LexicalJumpManager.jumpToMarkByNodeId(aid as string, node.id);

        if (jumpSuccess) {
          // 跳转到 Lexical 编辑器成功
        } else {
          console.warn(`跳转到 Lexical 编辑器失败: ${aid}, 内容: ${content}`);
        }
      }
    } catch (error) {
      console.error('节点点击跳转时出错:', error);
    }
  }, []);

  // ==================== 状态管理 ====================

  /**
   * 画布状态管理
   * 管理节点、边的状态和基础操作
   */
  const canvasState = useCanvasState({
    initialNodes,
    initialEdges,
    onNodesChange,
    onEdgesChange,
  });

  /**
   * 画布操作管理
   * 提供节点连接、复制粘贴、选择等操作
   */
  const canvasOperations = useCanvasOperations({
    nodes: canvasState.nodes,
    edges: canvasState.edges,
    setNodes: canvasState.setNodes,
    setEdges: canvasState.setEdges,
    copiedNodes: canvasState.copiedNodes,
    setCopiedNodes: canvasState.setCopiedNodes,
    save: canvasState.save,
  });

  /**
   * 画布交互管理
   * 处理拖拽、连接验证、样式等交互逻辑
   */
  const canvasInteractions = useCanvasInteractions({
    setNodes: canvasState.setNodes,
    setEdges: canvasState.setEdges,
    save: canvasState.save,
    addNode: canvasOperations.addNode,
  });

  /**
   * 键盘快捷键管理
   * 绑定全选、复制、粘贴、复制等快捷键
   */
  const canvasKeyboard = useCanvasKeyboard({
    handleSelectAll: canvasOperations.handleSelectAll,
    handleCopy: canvasOperations.handleCopy,
    handlePaste: canvasOperations.handlePaste,
    handleDuplicateAll: canvasOperations.handleDuplicateAll,
  });

  /**
   * 右键菜单上下文管理
   * 处理节点、边、画布的右键菜单
   */
  const canvasContext = useCanvasContext();

  // ==================== 特殊功能 ====================

  /**
   * 边弹框功能
   * 点击边时显示编辑弹框
   */
  const { selectedEdge, popupPosition, onEdgeClick, closePopup, onPaneClick } =
    useEdgePopup();

  /**
   * 右键菜单 DOM 引用
   * 用于检测菜单外部点击事件
   */
  const contextMenuRef = useRef<HTMLDivElement>(null);

  /**
   * ReactFlow 实例引用
   * 用于删除节点操作
   */
  const { deleteElements } = useReactFlow();

  // ==================== 标签分组初始化加载 ====================
  const loadTagGroups = useTagStore((s) => s.loadTagGroups);
  const tagGroups = useTagStore((s) => s.tagGroups);
  const isTagLoading = useTagStore((s) => s.isLoading);
  useEffect(() => {
    if (tagGroups.length === 0 && !isTagLoading) {
      void loadTagGroups();
    }
  }, [tagGroups.length, isTagLoading, loadTagGroups]);

  /**
   * 监听节点添加事件
   * 当外部组件请求添加节点时，更新画布并保存
   */
  useEffect(() => {
    const handleNodeAdded = (newNode: Node) => {
      // 添加节点到画布
      canvasState.setNodes(prevNodes => {
        const updatedNodes = [...prevNodes, newNode];
        // 使用更新后的节点列表进行持久化，避免闭包拿到旧的 nodes
        (async () => {
          try {
            const wid = getWsId();
            if (wid) {
              const content = JSON.stringify({
                nodes: updatedNodes,
                edges: canvasState.edges,
              });
              await canvasService.upsert({ wid, content });
            }
          } catch (error) {
            console.error('保存画布失败:', error);
          }
        })();
        return updatedNodes;
      });
    };

    // 监听节点添加事件
    NodeEvents.on('nodeAdded', handleNodeAdded);

    // 清理监听器
    return () => {
      NodeEvents.off('nodeAdded', handleNodeAdded);
    };
  }, [canvasState.setNodes]);  // 只保留必要的依赖

  /**
   * 监听节点删除事件
   * 当数据库中的节点被删除时，同步更新画布，并持久化
   * 保持监听器只注册一次，并使用 ref 读取最新节点/边
   */
  const nodesRef = useRef<Node[]>(canvasState.nodes);
  const edgesRef = useRef(canvasState.edges);
  useEffect(() => {
    nodesRef.current = canvasState.nodes;
    edgesRef.current = canvasState.edges;
  }, [canvasState.nodes, canvasState.edges]);

  useEffect(() => {
    const handleNodesDeleted = (nodeIds: string[]) => {
      const currentIds = nodesRef.current.map(n => n.id);
      const idsToDelete = nodeIds.filter(id => currentIds.includes(id));
      if (idsToDelete.length === 0) return;

      const updatedNodes = nodesRef.current.filter(node => !idsToDelete.includes(node.id));
      const updatedEdges = edgesRef.current.filter(e => !idsToDelete.includes(e.source) && !idsToDelete.includes(e.target));

      // 同步到本地状态
      canvasState.setNodes(() => updatedNodes);
      canvasState.setEdges(() => updatedEdges);

      // 持久化
      (async () => {
        try {
          const wid = getWsId();
          if (wid) {
            const content = JSON.stringify({ nodes: updatedNodes, edges: updatedEdges });
            await canvasService.upsert({ wid, content });
          }
        } catch (error) {
          console.error('保存画布失败:', error);
        }
      })();
    };
    NodeEvents.on('nodesDeleted', handleNodesDeleted);
    return () => NodeEvents.off('nodesDeleted', handleNodesDeleted);
  }, []);

  /**
   * 监听全局点击事件
   * 用于关闭右键菜单
   */
  useEffect(() => {
    const clickHandler = (event: Event) => {
      if (contextMenuRef.current) {
        const isClickInside = contextMenuRef.current.contains(
          event.target as HTMLElement
        );
        if (!isClickInside) {
          canvasContext.onCloseMenu();
        }
      }
    };

    window.addEventListener("click", clickHandler);
    return () => {
      window.removeEventListener("click", clickHandler);
    };
  }, [canvasContext.onCloseMenu]);

  return (
    <ErrorBoundary
      fallback={
        <div className="w-full h-full flex items-center justify-center bg-gray-50">
          <div className="text-center p-6">
            <div className="text-lg font-semibold text-gray-800 mb-2">
              画布加载失败
            </div>
            <p className="text-gray-600 mb-4">
              画布组件遇到了问题，请尝试刷新页面
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
            >
              刷新页面
            </button>
          </div>
        </div>
      }
      onError={(error, errorInfo) => {
        console.error('Canvas ErrorBoundary 捕获错误:', error);
        console.error('错误详情:', errorInfo);
        // 可以在这里发送错误到监控服务
      }}
    >
      <ReactFlow
        onSelectionChange={onSelectionChange}
        // 核心数据和状态变化
        nodes={canvasState.nodes}
        edges={canvasState.edges}
        onNodesChange={canvasState.flowProps.onNodesChange}
        onEdgesChange={canvasState.flowProps.onEdgesChange}
        // 右键菜单事件
        onNodeContextMenu={canvasContext.onNodeContextMenu}
        onPaneContextMenu={(event) => {
          canvasContext.onPaneContextMenu(event as React.MouseEvent);
        }}
        onSelectionContextMenu={canvasContext.onSelectionContextMenu}
        onEdgeContextMenu={canvasContext.onEdgeContextMenu}
        onPaneClick={() => {
          canvasContext.onCloseMenu();
          onPaneClick();
        }}
        onMoveStart={() => {
          closePopup();
        }}
        onMoveEnd={(event, viewport) => {
          // console.log('缩放/移动结束时的 zoom:', viewport.zoom);
        }}
        // 连接操作
        onConnect={canvasOperations.handleConnect}
        onConnectEnd={onConnectEnd || canvasOperations.handleConnectEnd}
        onConnectStart={canvasInteractions.handleConnectStart}
        isValidConnection={canvasInteractions.isValidConnection}
        connectionLineStyle={canvasInteractions.connectionLineStyle}
        // 节点交互
        onNodeClick={handleNodeClick}
        onNodeDrag={canvasInteractions.onNodeDrag}
        onNodeDragStop={canvasInteractions.onNodeDragStop}
        onDoubleClick={canvasInteractions.handleDrop}
        onEdgeClick={onEdgeClick}
        onEdgeDoubleClick={(e, edge) => {
          closePopup();
          setEditingEdgeId(edge.id);
        }}
        // 画布配置
        deleteKeyCode={["Delete", "Backspace"]}
        selectionKeyCode="Shift"

        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        panOnScroll

        zoomOnDoubleClick={false}
        // panOnDrag={false}
        // selectionOnDrag={true}
        multiSelectionKeyCode="Control"
        style={{ backgroundColor: "#F7F9FB" }}
        {...rest}
      >
        {/* ReactFlow 内部组件 */}
        {import.meta.env.DEV && <DevTools position="top-left" />}
        <MiniMap />
        <Panel position="bottom-center">
          <FlowBar />
        </Panel>
        <FlowContextMenu
          ref={contextMenuRef}
          onClick={canvasContext.onCloseMenu}
          contextMenuState={canvasContext.contextMenuState}
          operations={canvasOperations}
        />

        {/* 对齐指导线渲染器 */}
        {/* <AlignmentRenderer guides={canvasInteractions.alignmentSystem.guides} /> */}
      </ReactFlow>
      
      {/* 外部组件 - 用独立的ErrorBoundary包装 */}
      <ErrorBoundary
        fallback={<div className="hidden">节点编辑器加载失败</div>}
        onError={(error) => console.error('EditNode 错误:', error)}
      >
        <EditNode />
      </ErrorBoundary>
      
      {selectedEdge && (
        <ErrorBoundary
          fallback={<div className="hidden">边弹框加载失败</div>}
          onError={(error) => console.error('EdgePopup 错误:', error)}
        >
          <EdgePopup
            edge={selectedEdge}
            position={popupPosition}
            onClose={closePopup}
          />
        </ErrorBoundary>
      )}
    </ErrorBoundary>
  );
};