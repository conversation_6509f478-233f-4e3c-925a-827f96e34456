import * as React from "react"
import { cn } from "@/lib/utils"

const TitleInput = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          // 基础样式 - 看起来像标题，保持一致的 padding
          "flex h-8 w-full bg-transparent px-2 py-1 text-sm font-semibold text-foreground",
          "border border-transparent rounded-md transition-all duration-200",
          "placeholder:text-muted-foreground",
          // Hover 状态 - 显示可编辑提示，不改变 padding
          "hover:bg-muted/30 hover:border-border",
          // Focus 状态 - 完整的输入框样式，padding 保持一致
          "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
          "focus-visible:bg-background focus-visible:border-input focus-visible:shadow-sm",
          // 禁用状态
          "disabled:cursor-not-allowed disabled:opacity-50",
          // 文件输入样式
          "file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
TitleInput.displayName = "TitleInput"

export { TitleInput }