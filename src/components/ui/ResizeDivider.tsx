import {PanelPosition} from '@/pages/workspace/Panel';
import React, {useCallback, useRef, useState} from 'react';
import styled from 'styled-components';

const DividerContainer = styled.div<{
    x: number;
    y: number;
    height: number;
    isDragging: boolean;
    isHovered: boolean;
}>`
    position: absolute;
    left: ${props => props.x - 2}px;
    top: ${props => props.y}px;
    width: 4px;
    height: ${props => props.height}px;
    background-color: ${props =>
            props.isDragging ? '#0078d4' :
                    props.isHovered ? '#666' :
                            'transparent'
    };
    cursor: col-resize;
    /* 分割线层级：高于各面板，但不过度夸张，便于统一管理 */
    z-index: 3000;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #1890ff;
    }

    &:active {
        background-color: rgba(24, 144, 255, 0.25);
    }
`;

interface ResizeDividerProps {
    // 分割线的位置和高度
    x: number;
    y: number;
    height: number;

    // 左右面板的当前位置
    leftPanel: PanelPosition;
    rightPanel: PanelPosition;

    // 更新面板位置的函数
    setLeftPanelPosition: (position: PanelPosition | ((prev: PanelPosition) => PanelPosition)) => void;
    setRightPanelPosition: (position: PanelPosition | ((prev: PanelPosition) => PanelPosition)) => void;

    // 最小宽度限制
    minLeftWidth?: number;
    minRightWidth?: number;
}

export const ResizeDivider: React.FC<ResizeDividerProps> = ({
                                                                x,
                                                                y,
                                                                height,
                                                                leftPanel,
                                                                rightPanel,
                                                                setLeftPanelPosition,
                                                                setRightPanelPosition,
                                                                minLeftWidth = 200,
                                                                minRightWidth = 200,
                                                            }) => {
    const [isDragging, setIsDragging] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const dragStartX = useRef<number>(0);
    const initialLeftWidth = useRef<number>(0);
    const initialRightWidth = useRef<number>(0);
    const initialRightX = useRef<number>(0);

    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        setIsDragging(true);
        dragStartX.current = e.clientX;
        initialLeftWidth.current = leftPanel.width;
        initialRightWidth.current = rightPanel.width;
        initialRightX.current = rightPanel.x;

        const handleMouseMove = (e: MouseEvent) => {
            const deltaX = e.clientX - dragStartX.current;

            // 计算实际可调整的deltaX，确保不会超出最小宽度限制
            const maxLeftIncrease = initialRightWidth.current - minRightWidth;
            const maxRightIncrease = initialLeftWidth.current - minLeftWidth;

            const actualDelta = Math.max(
                -maxRightIncrease,
                Math.min(deltaX, maxLeftIncrease)
            );

            // 更新左面板
            setLeftPanelPosition(prev => ({
                ...prev,
                width: initialLeftWidth.current + actualDelta
            }));

            // 更新右面板
            setRightPanelPosition(prev => ({
                ...prev,
                x: initialRightX.current + actualDelta,
                width: initialRightWidth.current - actualDelta
            }));
        };

        const handleMouseUp = () => {
            setIsDragging(false);
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }, [
        leftPanel.width,
        rightPanel.width,
        rightPanel.x,
        setLeftPanelPosition,
        setRightPanelPosition,
        minLeftWidth,
        minRightWidth
    ]);

    return (
        <DividerContainer
            x={x}
            y={y}
            height={height}
            isDragging={isDragging}
            isHovered={isHovered}
            onMouseDown={handleMouseDown}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        />
    );
}; 