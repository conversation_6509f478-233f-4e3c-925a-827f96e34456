import { autoScrollForElements } from "@atlaskit/pragmatic-drag-and-drop-auto-scroll/element";
import { extractClosestEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge";
import { reorderWithEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import { monitorForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { reorder } from "@atlaskit/pragmatic-drag-and-drop/reorder";
import { useEffect, useRef, useState } from "react";
import invariant from "tiny-invariant";
import { Column } from "./column";
import {
  isTagData,
  isTagDropTargetData,
  isColumnData,
  isDraggingATag,
  isDraggingAColumn,
  TBoard,
  TColumn,
} from "./data";
import { unsafeOverflowAutoScrollForElements } from "@atlaskit/pragmatic-drag-and-drop-auto-scroll/unsafe-overflow/element";
import { Plus, Tag } from "lucide-react";
import { Button } from "../ui/button";
import { useTagOperations } from "./hooks/useTagOperations";

export function Board({ initial }: { initial: TBoard }) {
  const [data, setData] = useState(initial);
  const scrollableRef = useRef<HTMLDivElement | null>(null);
  const { reorderGroups } = useTagOperations();

  useEffect(() => {
    const element = scrollableRef.current;
    invariant(element);
    // 标签管理拖拽监听器已初始化
    return combine(
      monitorForElements({
        canMonitor: isDraggingATag,
        onDragStart() {
          // 标签管理拖拽开始
        },
        onDrop({ source, location }) {
          // 标签管理拖拽放置
          const dragging = source.data;
          if (!isTagData(dragging)) {
            return;
          }

          const innerMost = location.current.dropTargets[0];

          if (!innerMost) {
            return;
          }
          const dropTargetData = innerMost.data;
          const homeColumnIndex = data.columns.findIndex(
            (column) => column.id === dragging.columnId
          );
          const home: TColumn | undefined = data.columns[homeColumnIndex];

          if (!home) {
            return;
          }
          const tagIndexInHome = home.tags.findIndex(
            (tag) => tag.id === dragging.tag.id
          );

          // dropping on a tag
          if (isTagDropTargetData(dropTargetData)) {
            const destinationColumnIndex = data.columns.findIndex(
              (column) => column.id === dropTargetData.columnId
            );
            const destination = data.columns[destinationColumnIndex];
            // reordering in home column
            if (home === destination) {
              const tagFinishIndex = home.tags.findIndex(
                (tag) => tag.id === dropTargetData.tag.id
              );

              // could not find tags needed
              if (tagIndexInHome === -1 || tagFinishIndex === -1) {
                return;
              }

              // no change needed
              if (tagIndexInHome === tagFinishIndex) {
                return;
              }

              const closestEdge = extractClosestEdge(dropTargetData);

              const reordered = reorderWithEdge({
                axis: "vertical",
                list: home.tags,
                startIndex: tagIndexInHome,
                indexOfTarget: tagFinishIndex,
                closestEdgeOfTarget: closestEdge,
              });

              const updated: TColumn = {
                ...home,
                tags: reordered,
              };
              const columns = Array.from(data.columns);
              columns[homeColumnIndex] = updated;
              setData({ ...data, columns });
              return;
            }

            // moving tag from one column to another

            // unable to find destination
            if (!destination) {
              return;
            }

            const indexOfTarget = destination.tags.findIndex(
              (tag) => tag.id === dropTargetData.tag.id
            );

            const closestEdge = extractClosestEdge(dropTargetData);
            const finalIndex =
              closestEdge === "bottom" ? indexOfTarget + 1 : indexOfTarget;

            // remove tag from home list
            const homeTags = Array.from(home.tags);
            homeTags.splice(tagIndexInHome, 1);

            // insert into destination list
            const destinationTags = Array.from(destination.tags);
            destinationTags.splice(finalIndex, 0, dragging.tag);

            const columns = Array.from(data.columns);
            columns[homeColumnIndex] = {
              ...home,
              tags: homeTags,
            };
            columns[destinationColumnIndex] = {
              ...destination,
              tags: destinationTags,
            };
            setData({ ...data, columns });
            return;
          }

          // dropping onto a column, but not onto a tag
          if (isColumnData(dropTargetData)) {
            const destinationColumnIndex = data.columns.findIndex(
              (column) => column.id === dropTargetData.column.id
            );
            const destination = data.columns[destinationColumnIndex];

            if (!destination) {
              return;
            }

            // dropping on home
            if (home === destination) {
              console.log("moving tag to home column");

              // move to last position
              const reordered = reorder({
                list: home.tags,
                startIndex: tagIndexInHome,
                finishIndex: home.tags.length - 1,
              });

              const updated: TColumn = {
                ...home,
                tags: reordered,
              };
              const columns = Array.from(data.columns);
              columns[homeColumnIndex] = updated;
              setData({ ...data, columns });
              return;
            }

            console.log("moving tag to another column");

            // remove tag from home list

            const homeTags = Array.from(home.tags);
            homeTags.splice(tagIndexInHome, 1);

            // insert into destination list
            const destinationTags = Array.from(destination.tags);
            destinationTags.splice(destination.tags.length, 0, dragging.tag);

            const columns = Array.from(data.columns);
            columns[homeColumnIndex] = {
              ...home,
              tags: homeTags,
            };
            columns[destinationColumnIndex] = {
              ...destination,
              tags: destinationTags,
            };
            setData({ ...data, columns });
            return;
          }
        },
      }),
      monitorForElements({
        canMonitor: isDraggingAColumn,
        onDrop({ source, location }) {
          const dragging = source.data;
          if (!isColumnData(dragging)) {
            return;
          }

          const innerMost = location.current.dropTargets[0];

          if (!innerMost) {
            return;
          }
          const dropTargetData = innerMost.data;

          if (!isColumnData(dropTargetData)) {
            return;
          }

          const homeIndex = data.columns.findIndex(
            (column) => column.id === dragging.column.id
          );
          const destinationIndex = data.columns.findIndex(
            (column) => column.id === dropTargetData.column.id
          );

          if (homeIndex === -1 || destinationIndex === -1) {
            return;
          }

          if (homeIndex === destinationIndex) {
            return;
          }

          const reordered = reorder({
            list: data.columns,
            startIndex: homeIndex,
            finishIndex: destinationIndex,
          });
          setData({ ...data, columns: reordered });
          
          // 持久化排序更改到数据库
          const groupIds = reordered.map(column => column.id);
          reorderGroups(groupIds).catch(error => {
            console.error('保存标签组排序失败:', error);
            // 如果保存失败，可以考虑回滚本地状态
            // setData(data);
          });
        },
      }),
      autoScrollForElements({
        canScroll({ source }) {
          return isDraggingATag({ source }) || isDraggingAColumn({ source });
        },
        getConfiguration: () => ({ maxScrollSpeed: "fast" }),
        element,
      }),
      unsafeOverflowAutoScrollForElements({
        element,
        getConfiguration: () => ({ maxScrollSpeed: "fast" }),
        canScroll({ source }) {
          return isDraggingATag({ source }) || isDraggingAColumn({ source });
        },
        getOverflow() {
          return {
            forLeftEdge: {
              top: 1000,
              left: 1000,
              bottom: 1000,
            },
            forRightEdge: {
              top: 1000,
              right: 1000,
              bottom: 1000,
            },
          };
        },
      })
    );
  }, [data, reorderGroups]);

  useEffect(() => {
    let cleanupActive: (() => void) | null = null;
    const scrollable = scrollableRef.current;
    invariant(scrollable);

    function begin({ startX }: { startX: number }) {
      let lastX = startX;

      function handlePointerMove(event: PointerEvent) {
        const currentX = event.clientX;
        const diffX = lastX - currentX;

        lastX = currentX;
        scrollable?.scrollBy({ left: diffX });
      }

      function cleanup() {
        window.removeEventListener("pointermove", handlePointerMove, {
          capture: true,
        });
        window.removeEventListener("pointercancel", cleanup, { capture: true });
        window.removeEventListener("pointerup", cleanup, { capture: true });
        window.removeEventListener("pointerdown", cleanup, { capture: true });
        window.removeEventListener("keydown", cleanup, { capture: true });
        window.removeEventListener("resize", cleanup, { capture: true });
        window.removeEventListener("click", cleanup, { capture: true });
        window.removeEventListener("visibilitychange", cleanup, {
          capture: true,
        });
        cleanupActive = null;
      }

      window.addEventListener("pointermove", handlePointerMove, {
        capture: true,
      });
      window.addEventListener("pointercancel", cleanup, { capture: true });
      window.addEventListener("pointerup", cleanup, { capture: true });
      window.addEventListener("pointerdown", cleanup, { capture: true });
      window.addEventListener("keydown", cleanup, { capture: true });
      window.addEventListener("resize", cleanup, { capture: true });
      window.addEventListener("click", cleanup, { capture: true });
      window.addEventListener("visibilitychange", cleanup, { capture: true });

      cleanupActive = cleanup;
    }

    function handlePointerDown(event: PointerEvent) {
      if (!(event.target instanceof HTMLElement)) {
        return;
      }
      // ignore interactive elements with data-block-board-panning attribute
      if (event.target.closest("[data-block-board-panning]")) {
        return;
      }

      begin({ startX: event.clientX });
    }

    scrollable.addEventListener("pointerdown", handlePointerDown);

    return function cleanupAll() {
      scrollable.removeEventListener("pointerdown", handlePointerDown);
      cleanupActive?.();
    };
  }, []);

  const tagOperations = useTagOperations();

  return (
    <div className="flex h-full flex-col bg-gray-100 overflow-y-auto tag-management-board" data-tag-management-page>
      <header className="flex w-full min-w-full flex-col gap-4 sticky top-0 bg-gray-100 p-6 m-0 z-10">
        <div className="flex items-center gap-2">
          <Tag /> 标签
        </div>
        <Button
          size="sm"
          className="flex items-center gap-2 bg-[#6E6BEE] hover:bg-[#5a57cc] text-white border-[#6E6BEE] hover:border-[#5a57cc] rounded-[10px] h-[38.7px] px-4 w-fit"
          onClick={() => {
            tagOperations.createGroup("Tag Group");
          }}
        >
          <Plus size={16} />
          Tag Group
        </Button>
      </header>
      <div className="flex h-full flex-col gap-6 p-6 overflow-y-auto" ref={scrollableRef}>
        {data.columns.map((column) => (
          <Column key={column.id} column={column} canDelete={data.columns.length >1} />
        ))}
      </div>
    </div>
  );
}
