import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { Hash, ChevronDown, ChevronRight, Check, X } from "lucide-react";
import { useTagStore, useTagGroups } from "./stores/useTagStore";
import { Tag } from "./TagGroup";
import { TagGroupData } from "./mock-data";
import { useReactFlow } from "@xyflow/react";

const TagSearch: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const [searchValue, setSearchValue] = useState("");
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(
    new Set()
  );
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const tagGroups = useTagGroups();
  const { loadTagGroups, isLoading } = useTagStore();
  const { getNodes,updateNodeData} = useReactFlow()
  const nodes = getNodes()
  const [selectedNodes] = nodes.filter(n => n.selected)

  // 确保数据已加载
  useEffect(() => {
    if (tagGroups.length === 0 && !isLoading) {
      loadTagGroups();
    }
 
    const tags = selectedNodes?.data?.tags as [] || []
    if(tags){
      setSelectedTags(new Set(tags))
    }
  }, [tagGroups.length, isLoading, loadTagGroups]);

  // 切换分组折叠状态
  const toggleGroupCollapse = (groupId: string) => {
    setCollapsedGroups((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  // 切换标签选择状态
  const toggleTagSelection = (tagId: string) => {
    setSelectedTags((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(tagId)) {
        newSet.delete(tagId);
      } else {
        newSet.add(tagId);
      }
      console.log("selectedNodes", selectedNodes);
      updateNodeData(selectedNodes.id, {
        tags: Array.from(newSet)
      })
      return newSet;
    });
  };

  // 获取选中的标签数据
  const getSelectedTagsData = () => {
    const selectedTagsData: Array<{ tag: Tag; group: TagGroupData }> = [];

    tagGroups.forEach((group) => {
      group.tags.forEach((tag) => {
        if (selectedTags.has(tag.id)) {
          selectedTagsData.push({ tag, group });
        }
      });
    });

    return selectedTagsData;
  };

  // 过滤逻辑：同时过滤标签名和组名
  const getFilteredData = () => {
    if (!searchValue) return tagGroups;

    return tagGroups
      .map((group) => {
        // 过滤该组下的标签
        const filteredTags = group.tags.filter((tag) =>
          tag.name.toLowerCase().includes(searchValue.toLowerCase())
        );

        // 如果组名匹配搜索词，返回所有标签；否则只返回匹配的标签
        const groupNameMatches = group.title
          .toLowerCase()
          .includes(searchValue.toLowerCase());

        return {
          ...group,
          tags: groupNameMatches ? group.tags : filteredTags,
        };
      })
      .filter((group) => {
        // 只显示有标签的组或者组名匹配的组
        const groupNameMatches = group.title
          .toLowerCase()
          .includes(searchValue.toLowerCase());
        return groupNameMatches || group.tags.length > 0;
      });
  };

  const filteredGroups = getFilteredData();

  // 渲染加载状态的内容
  const loadingContent = (
    <>
      {/* 模态框背景 */}
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[99998]" />

      {/* 内容容器 */}
      <div
        className="fixed inset-x-0 z-[99999] flex justify-center"
        style={{ top: "30%" }}
      >
        <div className="w-full max-w-lg px-4">
          <Command className="rounded-lg border shadow-xl md:min-w-[450px] bg-white/95 backdrop-blur-md">
            <CommandInput placeholder={"搜索标签或分组..."} disabled />
            <CommandList>
              <div className="flex items-center justify-center py-6">
                <span className="text-sm text-gray-500">
                  加载标签数据中...
                </span>
              </div>
            </CommandList>
          </Command>
        </div>
      </div>
    </>
  );

  // 主要内容
  const mainContent = (
    <>
      {/* 模态框背景 */}
      <div
        className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[99999]"
        onClick={onClose}
      />

      {/* 内容容器 */}
      <div
        className="fixed inset-x-0 z-[99999] flex justify-center"
        style={{ top: "30%" }}
      >
        <div className="w-full max-w-lg px-4">
          <Command className="rounded-lg border shadow-xl md:min-w-[450px] bg-white/95 backdrop-blur-md">
            <CommandInput
              placeholder={"搜索标签或分组..."}
              value={searchValue}
              onInput={(e) => setSearchValue(e.currentTarget.value)}
            />

            {/* 显示选中的标签 */}
            {selectedTags.size > 0 ? (
              <div className="flex flex-wrap gap-2 p-3 h-10">
                {getSelectedTagsData().map(({ tag, group }) => (
                  <Badge
                    key={tag.id}
                    variant="secondary"
                    className="flex items-center gap-1 px-2 py-1"
                  >
                    <Hash size={10} className="text-gray-400" />
                    <span className="text-xs font-normal">{tag.name}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleTagSelection(tag.id);
                      }}
                      className="ml-1 hover:bg-gray-300 rounded-full p-0.5 transition-colors"
                    >
                      <X size={12} className="text-gray-500" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-xs text-gray-300 px-2 py-4 h-10 font-thin">
                未选择任何标签
              </div>
            )}

            <CommandList>
              {searchValue && filteredGroups.length === 0 ? (
                <CommandEmpty>没有找到匹配的标签或分组</CommandEmpty>
              ) : (
                filteredGroups.map(({ tags, title, id }) => {
                  const isCollapsed = collapsedGroups.has(id);

                  return (
                    <CommandGroup key={`${id}`}>
                      <div
                        className="flex items-center text-sm text-gray-600 px-2 py-1 cursor-pointer hover:bg-gray-100 transition-colors duration-150 rounded-sm"
                        onClick={() => toggleGroupCollapse(id)}
                      >
                        {isCollapsed ? (
                          <ChevronRight
                            size={14}
                            className="mr-1 text-gray-400"
                          />
                        ) : (
                          <ChevronDown
                            size={14}
                            className="mr-1 text-gray-400"
                          />
                        )}
                        <span className="select-none">{title}</span>
                      </div>

                      {!isCollapsed && (
                        <>
                          {!tags.length ? (
                            <div className="text-sm text-gray-500 px-6 py-2">
                              暂无标签。
                            </div>
                          ) : (
                            <div className="py-2">
                              {tags?.map((tag) => {
                                const isSelected = selectedTags.has(tag.id);

                                return (
                                  <CommandItem
                                    className="flex items-center justify-between pl-6 cursor-pointer"
                                    key={`${id}-${tag.id}`}
                                    onSelect={() => toggleTagSelection(tag.id)}
                                  >
                                    <div className="flex items-center">
                                      <Hash
                                        size={10}
                                        className="text-gray-400 mr-2"
                                      />
                                      <span>{tag.name}</span>
                                    </div>
                                    {isSelected && (
                                      <Check
                                        size={16}
                                        className="text-green-600"
                                      />
                                    )}
                                  </CommandItem>
                                );
                              })}
                            </div>
                          )}
                        </>
                      )}
                    </CommandGroup>
                  );
                })
              )}
            </CommandList>
          </Command>
        </div>
      </div>
    </>
  );

  // 使用 createPortal 渲染到 body
  return createPortal(
    isLoading ? loadingContent : mainContent,
    document.body
  );
};

export default TagSearch;
