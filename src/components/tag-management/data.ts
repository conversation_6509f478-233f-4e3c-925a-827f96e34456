export type TTag = {
  id: string;
  name: string;
};

export type TColumn = {
  id: string;
  title: string;
  tags: TTag[];
};

export type TBoard = {
  columns: TColumn[];
};

const tagKey = Symbol("tag");
export type TTagData = {
  [tagKey]: true;
  tag: TTag;
  columnId: string;
  rect: DOMRect;
};

export function getTagData({
  tag,
  rect,
  columnId,
}: Omit<TTagData, typeof tagKey> & { columnId: string }): TTagData {
  return {
    [tagKey]: true,
    rect,
    tag,
    columnId,
  };
}

export function isTagData(
  value: Record<string | symbol, unknown>
): value is TTagData {
  return Boolean(value[tagKey]);
}

export function isDraggingATag({
  source,
}: {
  source: { data: Record<string | symbol, unknown> };
}): boolean {
  return isTagData(source.data);
}

const tagDropTargetKey = Symbol("tag-drop-target");
export type TTagDropTargetData = {
  [tagDropTargetKey]: true;
  tag: TTag;
  columnId: string;
};

export function isTagDropTargetData(
  value: Record<string | symbol, unknown>
): value is TTagDropTargetData {
  return Boolean(value[tagDropTargetKey]);
}

export function getTagDropTargetData({
  tag,
  columnId,
}: Omit<TTagDropTargetData, typeof tagDropTargetKey> & {
  columnId: string;
}): TTagDropTargetData {
  return {
    [tagDropTargetKey]: true,
    tag,
    columnId,
  };
}

const columnKey = Symbol("column");
export type TColumnData = {
  [columnKey]: true;
  column: TColumn;
};

export function getColumnData({
  column,
}: Omit<TColumnData, typeof columnKey>): TColumnData {
  return {
    [columnKey]: true,
    column,
  };
}

export function isColumnData(
  value: Record<string | symbol, unknown>
): value is TColumnData {
  return Boolean(value[columnKey]);
}

export function isDraggingAColumn({
  source,
}: {
  source: { data: Record<string | symbol, unknown> };
}): boolean {
  return isColumnData(source.data);
}
