import { useState, useRef, useEffect } from "react";
import Fuse from "fuse.js";
import Mark from "mark.js";
import { Search } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { canvasService } from "@/local";
import {
  usePdfStore,
  type CustomHighlight,
} from "@/store/pdf-store";
import { reduce } from "lodash";
import { NODE_TYPE_MAPS } from "../canvas/components/node";
import { useReactFlowInstanceStore } from "@/store/reactflow-instance-store";
import { useMenu } from "@/components/pdf/hooks/menu";
import { useWorkerSpaceStore } from "@/store/workerspace-store/store";

// 添加高亮样式
const highlightStyles = `
  mark {
    background-color: #fef08a !important;
    color: inherit !important;
    padding: 1px 2px !important;
    border-radius: 2px !important;
    font-weight: 500 !important;
  }
  
  .dark mark {
    background-color: #a16207 !important;
  }
`;

// 注入样式到页面
if (typeof document !== "undefined") {
  const style = document.createElement("style");
  style.textContent = highlightStyles;
  document.head.appendChild(style);
}

// 节点数据类型
interface NodeData {
  id: string;
  title: string;
  content: string;
  color: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
}

// 文件搜索数据类型
interface FileSearchData {
  aid: string;
  filename: string;
  url: string;
  highlightTexts: string; // 合并所有高亮文本
  highlightCount: number; // 高亮数量
  fileType: 'pdf' | 'text' | 'unknown'; // 文件类型
}

// 文件类型辅助函数
const getFileType = (filename: string): FileSearchData['fileType'] => {
  const ext = filename.toLowerCase().split('.').pop();
  if (ext === 'pdf') return 'pdf';
  if (['txt', 'md', 'text', 'markdown'].includes(ext || '')) return 'text';
  return 'unknown';
};

const getFileTypeLabel = (fileType: FileSearchData['fileType']) => {
  switch(fileType) {
    case 'pdf': return 'PDF';
    case 'text': return 'TXT';
    default: return 'FILE';
  }
};

const getFileTypeColor = (fileType: FileSearchData['fileType']) => {
  switch(fileType) {
    case 'pdf': return 'bg-red-500';
    case 'text': return 'bg-blue-500';
    default: return 'bg-gray-500';
  }
};

const BigSearch = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [nodes, setNodes] = useState<NodeData[]>([]);
  const [filteredNodes, setFilteredNodes] = useState<NodeData[]>([]);
  const [files, setFiles] = useState<FileSearchData[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<FileSearchData[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isComposing, setIsComposing] = useState(false); // 用于处理中文输入法
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // 创建 Fuse.js 实例
  const fuseRef = useRef<Fuse<NodeData> | null>(null);
  const fileFuseRef = useRef<Fuse<FileSearchData> | null>(null);

  // 获取 PDF 数据
  const pdfStoreData = usePdfStore((state) => state.pdfs);
  const { onMenuClick } = useMenu();
  
  // 获取当前工作区 ID
  const wid = useWorkerSpaceStore(state => state.wid);

  // 检测操作系统并返回相应的快捷键提示
  const getShortcutText = () => {
    const isMac = navigator.userAgent.toUpperCase().indexOf("MAC") >= 0;
    return isMac ? "⌘K" : "Ctrl+K";
  };

  const fetchNodes = async () => {
    setLoading(true);
    try {
      // const response = await getNodesList("");
      // const { nodes } = response.data;
      // 只获取当前工作区的画布，如果 wid 不存在则获取全部
      const allCanvas = await canvasService.list({ wid: wid || "" });
      const nodes = reduce(
        allCanvas.list,
        (prev: any, curr: any) => {
          const currentCanvas = JSON.parse(curr.content);
          return [...prev, ...currentCanvas.nodes];
        },
        []
      )
        .filter((node: any) => node.type === NODE_TYPE_MAPS.markdown)
        .map((node: any) => {
          return {
            id: node.id,
            title: node.data.title,
            content: node.data.content,
            color: node.data.color,
            x: node.x,
            y: node.y,
            width: node.width,
            height: node.height,
          };
        });

      setNodes(nodes || []);
      setFilteredNodes(nodes || []);

      // 初始化 Fuse.js
      fuseRef.current = new Fuse(nodes || [], {
        keys: ["title", "content"],
        threshold: 0.3, // 恢复到更严格的阈值
        includeMatches: true,
        minMatchCharLength: 1,
        distance: 1000, // 增加搜索距离
        location: 0, // 从开头开始搜索
        findAllMatches: true, // 找到所有匹配
        ignoreLocation: true, // 忽略位置，全文搜索
        ignoreFieldNorm: true, // 忽略字段长度影响评分
      });
    } catch (error) {
      console.error("获取节点列表失败：", error);
      setNodes([]);
      setFilteredNodes([]);
    } finally {
      setLoading(false);
    }
  };
  // 获取节点数据 - 当 wid 变化时重新获取
  useEffect(() => {
    if (wid) {
      fetchNodes();
    }
  }, [wid]);

  // 获取和处理文件数据
  useEffect(() => {
    const processFileData = () => {
      const fileArray: FileSearchData[] = [];

      for (const [aid, pdfData] of pdfStoreData) {
        // 提取所有高亮文本
        const highlightTexts =
          pdfData.highlights
            ?.map((highlight: CustomHighlight) => highlight.content?.text || "")
            .filter((text) => text.trim() !== "")
            .join(" ") || "";

        fileArray.push({
          aid,
          filename: pdfData.filename,
          url: pdfData.url,
          highlightTexts,
          highlightCount: pdfData.highlights?.length || 0,
          fileType: getFileType(pdfData.filename),
        });
      }

      setFiles(fileArray);
      setFilteredFiles(fileArray);

      // 初始化文件 Fuse.js 实例
      fileFuseRef.current = new Fuse(fileArray, {
        keys: ["filename", "highlightTexts"],
        threshold: 0.3,
        includeMatches: true,
        minMatchCharLength: 1,
        distance: 1000,
        location: 0,
        findAllMatches: true,
        ignoreLocation: true,
        ignoreFieldNorm: true,
      });
    };

    processFileData();
  }, [pdfStoreData]);

  // 搜索和高亮效果
  useEffect(() => {
    // 如果正在输入中文，不进行搜索
    if (isComposing) {
      return;
    }

    if (!searchTerm.trim()) {
      setFilteredNodes(nodes);
      setFilteredFiles(files);
      // 清除高亮
      if (resultsRef.current) {
        new Mark(resultsRef.current).unmark();
      }
      return;
    }

    // 搜索节点
    if (fuseRef.current) {
      const searchResults = fuseRef.current.search(searchTerm);
      const results = searchResults.map((r) => r.item);
      setFilteredNodes(results);
    }

    // 搜索文件
    if (fileFuseRef.current) {
      const fileSearchResults = fileFuseRef.current.search(searchTerm);
      const fileResults = fileSearchResults.map((r) => r.item);
      setFilteredFiles(fileResults);
    }

    // 延迟执行高亮，确保 DOM 已更新
    setTimeout(() => {
      if (resultsRef.current) {
        const markInstance = new Mark(resultsRef.current);
        markInstance.unmark({
          done: () => {
            markInstance.mark(searchTerm, {
              element: "mark",
              className: "",
              separateWordSearch: false, // 不分词搜索，对中文很重要
              accuracy: "partially", // 部分匹配，对中文友好
              diacritics: false, // 禁用变音符号匹配，对中文很重要
              ignorePunctuation: [], // 不忽略标点符号
              caseSensitive: false, // 不区分大小写
            });
          },
        });
      }
    }, 0);
  }, [searchTerm, nodes, files, isComposing]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 检测 Cmd+K (Mac) 或 Ctrl+K (Windows)
      if ((event.metaKey || event.ctrlKey) && event.key === "k") {
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          fetchNodes();
          setTimeout(() => {
            inputRef.current?.focus();
          }, 100);
        } else {
          setIsOpen(false);
          setSearchTerm("");
        }
      }
      
      // ESC 键关闭搜索框
      if (event.key === "Escape" && isOpen) {
        event.preventDefault();
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen]);

  const handleFocus = () => {
    setIsOpen(true);
  };

  const handleBlur = (e: React.FocusEvent) => {
    // 检查焦点是否移动到了组件内部的其他元素
    const currentTarget = e.currentTarget;
    
    // 延迟检查，给点击事件时间处理
    setTimeout(() => {
      // 如果焦点仍在组件内，不关闭
      if (currentTarget.contains(document.activeElement)) {
        return;
      }
      setIsOpen(false);
    }, 150);
  };

  // 处理节点点击
  const handleNodeClick = (node: NodeData) => {
    // 获取 ReactFlow 实例
    const reactFlowInstance = useReactFlowInstanceStore.getState().instance;
    
    if (reactFlowInstance) {
      // 先查找 ReactFlow 中对应的节点以获取准确的位置和尺寸
      const flowNodes = reactFlowInstance.getNodes();
      const targetNode = flowNodes.find(n => n.id === node.id);
      
      if (targetNode) {
        // 计算节点中心位置
        const centerX = targetNode.position.x + (targetNode.width || 200) / 2;
        const centerY = targetNode.position.y + (targetNode.height || 100) / 2;
        
        // 使用 setCenter 方法将节点定位到视口中央
        reactFlowInstance.setCenter(
          centerX,
          centerY,
          {
            zoom: 1,       // 缩放级别
            duration: 800, // 动画持续时间（毫秒）
          }
        );
        
        // 设置节点为选中状态
        const updatedNodes = flowNodes.map((n) => ({
          ...n,
          selected: n.id === node.id, // 只选中点击的节点
        }));
        reactFlowInstance.setNodes(updatedNodes);
      }
    }
    
    setIsOpen(false);
  };

  // 处理文件点击
  const handleFileClick = (file: FileSearchData) => {
    onMenuClick({ id: file.aid, file_name: file.filename });
    setIsOpen(false);
  };

  // 处理中文输入法事件
  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
  };

  // 预览内容组件
  const PreviewContent = ({ node }: { node: NodeData }) => (
    <div className="w-80 max-w-sm">
      <div className="flex items-center gap-2 mb-3">
        <div
          className="w-4 h-4 rounded-full flex-shrink-0"
          style={{ backgroundColor: `${node.color}` }}
        />
        <h3 className="font-semibold text-sm truncate">
          {node.title || "无标题"}
        </h3>
      </div>

      <div className="space-y-2">
        <div className="text-sm leading-relaxed max-h-40 overflow-y-auto">
          {node.content ? (
            <div className="whitespace-pre-wrap break-words">
              {node.content.length > 200
                ? `${node.content.substring(0, 200)}...`
                : node.content}
            </div>
          ) : (
            <div className="text-muted-foreground italic">无内容</div>
          )}
        </div>
      </div>
    </div>
  );

  // 文件预览内容组件
  const FilePreviewContent = ({ file }: { file: FileSearchData }) => (
      <div className="w-80 max-w-sm">
        <div className="flex items-center gap-2 mb-3">
          <div className={`w-7 h-4 ${getFileTypeColor(file.fileType)} rounded flex-shrink-0 flex items-center justify-center`}>
            <span className="text-white text-xs font-bold">{getFileTypeLabel(file.fileType)}</span>
          </div>
          <h3 className="font-semibold text-sm truncate">
            {file.filename || "未命名文件"}
          </h3>
        </div>

        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">
            高亮数量: {file.highlightCount}
          </div>
          {file.highlightTexts && (
            <div className="text-sm leading-relaxed max-h-40 overflow-y-auto">
              <div className="whitespace-pre-wrap break-words">
                {file.highlightTexts.length > 200
                  ? `${file.highlightTexts.substring(0, 200)}...`
                  : file.highlightTexts}
              </div>
            </div>
          )}
          {!file.highlightTexts && (
            <div className="text-sm text-muted-foreground italic">
              暂无高亮内容
            </div>
          )}
        </div>
      </div>
  );

  return (
    <>
      {/* 放大镜按钮 */}
      {!isOpen && (
        <div className="absolute top-10 right-32 z-[9]">
          <button
            onClick={() => {
              setIsOpen(true);
              fetchNodes();
              setTimeout(() => {
                inputRef.current?.focus();
              }, 100);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:scale-105"
            title={`搜索 (${getShortcutText()})`}
          >
            <Search className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              搜索
            </span>
            <span className="text-xs text-gray-400 dark:text-gray-500 ml-1 px-2 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">
              {getShortcutText()}
            </span>
          </button>
        </div>
      )}

      {/* 搜索框 */}
      {isOpen && (
        <div className="absolute top-10 right-4 z-[9]">
          <div className="relative animate-in fade-in zoom-in duration-200">
            <Command>
              <CommandInput
                ref={inputRef}
                placeholder={`搜索节点、PDF或输入命令...`}
                className="w-[500px] h-[40px] rounded-full pr-16"
                onFocus={handleFocus}
                onBlur={handleBlur}
                onValueChange={setSearchTerm}
                onCompositionStart={handleCompositionStart}
                onCompositionEnd={handleCompositionEnd}
              />
              {/* 关闭按钮 */}
              <button
                onClick={() => {
                  setIsOpen(false);
                  setSearchTerm("");
                }}
                className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                title="关闭 (Esc)"
              >
                <div className="w-4 h-4 relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-3 h-0.5 bg-gray-500 rotate-45 absolute"></div>
                    <div className="w-3 h-0.5 bg-gray-500 -rotate-45 absolute"></div>
                  </div>
                </div>
              </button>
            <CommandList
              ref={resultsRef}
              onMouseEnter={() => {
                // 鼠标进入搜索结果区域时，保持面板开启
                if (inputRef.current) {
                  inputRef.current.focus();
                }
              }}
            >
              <CommandEmpty>
                {loading ? "加载中..." : "未找到相关结果。"}
              </CommandEmpty>

              {/* 节点列表 */}
              {filteredNodes.length > 0 && (
                <>
                  <CommandGroup heading={`Cards (${filteredNodes.length})`}>
                    {filteredNodes.map((node) => (
                      <HoverCard key={node.id}>
                        <HoverCardTrigger asChild>
                          <CommandItem
                            value={`node-${node.id}`}
                            onSelect={() => {
                              // 使用 setTimeout 避免与搜索框关闭的时序冲突
                              setTimeout(() => handleNodeClick(node), 0);
                            }}
                            className="cursor-pointer"
                            onMouseDown={(e) => {
                              // 防止鼠标按下时失去焦点
                              e.preventDefault();
                            }}
                          >
                            <div className="flex flex-col items-start gap-2 w-full">
                              {/* 节点颜色指示器 */}

                              <div className="flex-1 min-w-0 flex items-center gap-2">
                                <div
                                  className="w-3 h-3 rounded-full flex-shrink-0"
                                  style={{ backgroundColor: `${node.color}` }}
                                />
                                <div className="font-medium truncate">
                                  {node.title || "无标题"}
                                </div>
                                {node.content && (
                                  <div className="text-sm text-muted-foreground truncate"></div>
                                )}
                              </div>
                              <div className="text-xs text-muted-foreground text-left w-full">
                                {node.content.length > 50
                                  ? `${node.content.substring(0, 50)}...`
                                  : node.content || "无内容"}
                              </div>
                            </div>
                          </CommandItem>
                        </HoverCardTrigger>
                        <HoverCardContent
                          side="left"
                          align="start"
                          className="w-auto p-4"
                        >
                          <PreviewContent node={node} />
                        </HoverCardContent>
                      </HoverCard>
                    ))}
                  </CommandGroup>
                  <CommandSeparator />
                </>
              )}

              {/* 文件列表 */}
              {filteredFiles.length > 0 && (
                <>
                  <CommandGroup heading={`文件 (${filteredFiles.length})`}>
                    {filteredFiles.map((file) => (
                        <HoverCard key={file.aid}>
                          <HoverCardTrigger asChild>
                            <CommandItem
                              value={`file-${file.aid}`}
                              onSelect={() => handleFileClick(file)}
                              className="cursor-pointer"
                              onMouseDown={(e) => {
                                // 防止鼠标按下时失去焦点
                                e.preventDefault();
                              }}
                            >
                              <div className="flex flex-col items-start gap-2 w-full">
                                <div className="flex-1 min-w-0 flex items-center gap-2">
                                  <div className={`w-6 h-4 ${getFileTypeColor(file.fileType)} rounded flex-shrink-0 flex items-center justify-center`}>
                                    <span className="text-white text-[8px] font-bold">
                                      {getFileTypeLabel(file.fileType)}
                                    </span>
                                  </div>
                                  <div className="font-medium truncate">
                                    {file.filename || "未命名文件"}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    ({file.highlightCount} 高亮)
                                  </div>
                                </div>
                                {file.highlightTexts && (
                                  <div className="text-xs text-muted-foreground text-left w-full">
                                    {file.highlightTexts.length > 50
                                      ? `${file.highlightTexts.substring(
                                          0,
                                          50
                                        )}...`
                                      : file.highlightTexts}
                                  </div>
                                )}
                              </div>
                            </CommandItem>
                          </HoverCardTrigger>
                          <HoverCardContent
                            side="left"
                            align="start"
                            className="w-auto p-4"
                          >
                            <FilePreviewContent file={file} />
                          </HoverCardContent>
                        </HoverCard>
                    ))}
                  </CommandGroup>
                  <CommandSeparator />
                </>
              )}

              <CommandSeparator />
              <CommandGroup heading="AI相似度推荐">
                <CommandItem>暂无</CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      </div>
      )}
    </>
  );
};

export default BigSearch;
