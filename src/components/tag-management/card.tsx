"use client";

import {
  draggable,
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { preserveOffsetOnSource } from "@atlaskit/pragmatic-drag-and-drop/element/preserve-offset-on-source";
import { setCustomNativeDragPreview } from "@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview";
import { MutableRefObject, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import invariant from "tiny-invariant";
import { useTagOperations } from "./hooks/useTagOperations";

import {
  type Edge,
  attachClosestEdge,
  extractClosestEdge,
} from "@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import {
  getTagData,
  getTagDropTargetData,
  isTagData,
  isDraggingATag,
  TTag,
} from "./data";
import { Ellipsis, Hash, Pencil, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

// 检查是否为Safari浏览器
function isSafari(): boolean {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

// 浅比较两个对象是否相等
function isShallowEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) {
    return true;
  }

  if (
    typeof obj1 !== "object" ||
    obj1 === null ||
    typeof obj2 !== "object" ||
    obj2 === null
  ) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let key of keys1) {
    if (!keys2.includes(key) || obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
}

type TTagState =
  | {
      type: "idle";
    }
  | {
      type: "is-dragging";
    }
  | {
      type: "is-dragging-and-left-self";
    }
  | {
      type: "is-over";
      dragging: DOMRect;
      closestEdge: Edge;
    }
  | {
      type: "preview";
      container: HTMLElement;
      dragging: DOMRect;
    };

const idle: TTagState = { type: "idle" };

const innerStyles: { [Key in TTagState["type"]]?: string } = {
  idle: "hover:outline outline-2 outline-neutral-50 cursor-grab",
  "is-dragging": "opacity-40",
};

const outerStyles: { [Key in TTagState["type"]]?: string } = {
  // We no longer render the draggable item after we have left it
  // as it's space will be taken up by a shadow on adjacent items.
  // Using `display:none` rather than returning `null` so we can always
  // return refs from this component.
  // Keeping the refs allows us to continue to receive events during the drag.
  "is-dragging-and-left-self": "hidden",
};

export function TagShadow({ dragging }: { dragging: DOMRect }) {
  return (
    <div
      className="flex-shrink-0 rounded-2xl bg-[rgba(110,107,238,0.2)] border border-[#6E6BEE] opacity-60"
      style={{
        width: dragging.width,
        height: dragging.height - 8,
        minWidth: "60px", // 确保有最小宽度
      }}
    />
  );
}

export function TagDisplay({
  tag,
  state,
  outerRef,
  innerRef,
  dropdownOpen,
  setDropdownOpen,
  onEdit,
  onDelete,
}: {
  tag: TTag;
  state: TTagState;
  outerRef?: React.MutableRefObject<HTMLDivElement | null>;
  innerRef?: MutableRefObject<HTMLDivElement | null>;
  dropdownOpen?: boolean;
  setDropdownOpen?: (open: boolean) => void;
  onEdit?: () => void;
  onDelete?: () => void;
}) {
  return (
    <div
      ref={outerRef}
      className={`flex flex-shrink-0 flex-row gap-2 px-1 ${
        outerStyles[state.type]
      }`}
    >
      {/* Put a shadow before the item if closer to the left edge */}
      {state.type === "is-over" && state.closestEdge === "left" ? (
        <TagShadow dragging={state.dragging} />
      ) : null}
      <div
        className={`group rounded-2xl py-2 px-3 text-[#13123C] bg-[rgba(110,107,238,0.1)] border-[#6E6BEE] hover:bg-[rgba(110,107,238,0.15)] border text-sm ${
          innerStyles[state.type]
        }`}
        ref={innerRef}
        style={
          state.type === "preview"
            ? {
                width: state.dragging.width,
                height: state.dragging.height,
                // transform: !isSafari() ? "rotate(4deg)" : undefined,
              }
            : undefined
        }
      >
        <div className="flex flex-row items-center justify-between gap-1">
          <Hash size={16} className="text-[#6E6BEE]"/>
          <div className="text-[#13123C] font-medium">{tag.name}</div>
          <div>
            <DropdownMenu
              open={dropdownOpen || false}
              onOpenChange={setDropdownOpen || (() => {})}
            >
              <DropdownMenuTrigger
                className={`cursor-pointer rounded-full p-1 hover:bg-[rgba(110,107,238,0.2)] transition-all duration-200 ${
                  dropdownOpen
                    ? "opacity-100 bg-[rgba(110,107,238,0.2)] hover:bg-[rgba(110,107,238,0.3)]"
                    : "opacity-0 group-hover:opacity-100"
                }`}
              >
                <Ellipsis size={12} className="text-[#979797]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="z-[9999] w-[90px] min-w-[90px]">
                <DropdownMenuItem
                  onClick={() => {
                    onEdit?.();
                    setDropdownOpen?.(false);
                  }}
                >
                  <Pencil size={12} /> 编辑
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-500"
                  onClick={() => {
                    onDelete?.();
                    setDropdownOpen?.(false);
                  }}
                >
                  <Trash2 /> 删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      {/* Put a shadow after the item if closer to the right edge */}
      {state.type === "is-over" && state.closestEdge === "right" ? (
        <TagShadow dragging={state.dragging} />
      ) : null}
    </div>
  );
}

export function Tag({ tag, columnId }: { tag: TTag; columnId: string }) {
  const outerRef = useRef<HTMLDivElement | null>(null);
  const innerRef = useRef<HTMLDivElement | null>(null);
  const [state, setState] = useState<TTagState>(idle);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // 使用标签操作 hook
  const { editTag, deleteTag } = useTagOperations();

  // 编辑弹窗状态
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editTagName, setEditTagName] = useState("");

  // 删除确认弹窗状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // 处理编辑
  const handleEdit = () => {
    setEditTagName(tag.name);
    setShowEditDialog(true);
  };

  // 处理删除
  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  // 确认编辑
  const handleConfirmEdit = async () => {
    if (editTagName.trim() && editTagName !== tag.name) {
      try {
        await editTag(columnId, tag.id, editTagName.trim());
        setShowEditDialog(false);
        setEditTagName("");
      } catch (error) {
        console.error("更新标签失败:", error);
      }
    } else {
      setShowEditDialog(false);
      setEditTagName("");
    }
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    try {
      await deleteTag(columnId, tag.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("删除标签失败:", error);
    }
  };

  // 处理编辑弹窗的回车键
  const handleEditKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && editTagName.trim()) {
      handleConfirmEdit();
    }
  };

  useEffect(() => {
    const outer = outerRef.current;
    const inner = innerRef.current;
    invariant(outer && inner);

    // Tag 拖拽组件初始化
    return combine(
      draggable({
        element: inner,
        getInitialData: ({ element }) =>
          getTagData({
            tag,
            columnId,
            rect: element.getBoundingClientRect(),
          }),
        onGenerateDragPreview({ nativeSetDragImage, location, source }) {
          const data = source.data;
          invariant(isTagData(data));
          setCustomNativeDragPreview({
            nativeSetDragImage,
            getOffset: preserveOffsetOnSource({
              element: inner,
              input: location.current.input,
            }),
            render({ container }) {
              // Demonstrating using a react portal to generate a preview
              setState({
                type: "preview",
                container,
                dragging: inner.getBoundingClientRect(),
              });
            },
          });
        },
        onDragStart() {
          setState({ type: "is-dragging" });
        },
        onDrop() {
          setState(idle);
        },
      }),
      dropTargetForElements({
        element: outer,
        getIsSticky: () => true,
        canDrop: isDraggingATag,
        getData: ({ element, input }) => {
          const data = getTagDropTargetData({ tag, columnId });
          return attachClosestEdge(data, {
            element,
            input,
            allowedEdges: ["left", "right"],
          });
        },
        onDragEnter({ source, self }) {
          if (!isTagData(source.data)) {
            return;
          }
          if (source.data.tag.id === tag.id) {
            return;
          }
          const closestEdge = extractClosestEdge(self.data);
          if (!closestEdge) {
            return;
          }

          setState({
            type: "is-over",
            dragging: source.data.rect,
            closestEdge,
          });
        },
        onDrag({ source, self }) {
          if (!isTagData(source.data)) {
            return;
          }
          if (source.data.tag.id === tag.id) {
            return;
          }
          const closestEdge = extractClosestEdge(self.data);
          if (!closestEdge) {
            return;
          }
          // optimization - Don't update react state if we don't need to.
          const proposed: TTagState = {
            type: "is-over",
            dragging: source.data.rect,
            closestEdge,
          };
          setState((current) => {
            if (isShallowEqual(proposed, current)) {
              return current;
            }
            return proposed;
          });
        },
        onDragLeave({ source }) {
          if (!isTagData(source.data)) {
            return;
          }
          if (source.data.tag.id === tag.id) {
            setState({ type: "is-dragging-and-left-self" });
            return;
          }
          setState(idle);
        },
        onDrop() {
          setState(idle);
        },
      })
    );
  }, [tag, columnId]);

  return (
    <>
      <TagDisplay
        outerRef={outerRef}
        innerRef={innerRef}
        state={state}
        tag={tag}
        dropdownOpen={dropdownOpen}
        setDropdownOpen={setDropdownOpen}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* 编辑弹窗 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="z-[9999] w-[400px] rounded-lg overflow-hidden">
          <DialogHeader>
            <DialogTitle>编辑标签</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <Input
              placeholder="请输入标签名称"
              value={editTagName}
              onChange={(e) => setEditTagName(e.target.value)}
              onKeyDown={handleEditKeyDown}
              autoFocus
            />
          </div>

          <DialogFooter>
            <Button
              size={"sm"}
              variant="outline"
              onClick={() => {
                setShowEditDialog(false);
                setEditTagName("");
              }}
            >
              取消
            </Button>
            <Button
              size={"sm"}
              onClick={handleConfirmEdit}
              disabled={!editTagName.trim() || editTagName === tag.name}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认弹窗 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="z-[9999] w-[400px] rounded-lg overflow-hidden">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              你确定要删除标签 "{tag.name}" 吗？此操作无法撤销。
            </p>
          </div>

          <DialogFooter>
            <Button
              size={"sm"}
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              取消
            </Button>
            <Button
              size={"sm"}
              onClick={handleConfirmDelete}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {state.type === "preview"
        ? createPortal(
            <TagDisplay
              state={state}
              tag={tag}
              dropdownOpen={dropdownOpen}
              setDropdownOpen={setDropdownOpen}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />,
            state.container
          )
        : null}
    </>
  );
}
