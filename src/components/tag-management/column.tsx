import {
  draggable,
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import {
  GripVertical,
  PencilLine,
  Plus,
  Tag,
  Trash2,
} from "lucide-react";
import { memo, useEffect, useRef, useState } from "react";
import invariant from "tiny-invariant";
import { useTagOperations } from "./hooks/useTagOperations";

import { autoScrollForElements } from "@atlaskit/pragmatic-drag-and-drop-auto-scroll/element";
import { unsafeOverflowAutoScrollForElements } from "@atlaskit/pragmatic-drag-and-drop-auto-scroll/unsafe-overflow/element";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import { DragLocationHistory } from "@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types";
import { preserveOffsetOnSource } from "@atlaskit/pragmatic-drag-and-drop/element/preserve-offset-on-source";
import { setCustomNativeDragPreview } from "@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview";
import { Tag as TagComponent, TagShadow } from "./card";
import {
  getColumnData,
  isTagData,
  isTagDropTargetData,
  isColumnData,
  isDraggingATag,
  isDraggingAColumn,
  TTagData,
  TColumn,
  TTag,
} from "./data";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
  DialogOverlay,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

// 简化的工具函数
const blockBoardPanningAttr = "data-block-board-panning";

const isSafari = () => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

const isShallowEqual = (obj1: any, obj2: any) => {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
};

type TColumnState =
  | {
      type: "is-tag-over";
      isOverChildTag: boolean;
      dragging: DOMRect;
    }
  | {
      type: "is-column-over";
    }
  | {
      type: "idle";
    }
  | {
      type: "is-dragging";
    };

const stateStyles: { [Key in TColumnState["type"]]: string } = {
  idle: "cursor-grab",
  "is-tag-over": "ring-2 ring-primary/20 ring-offset-2",
  "is-dragging": "opacity-50",
  "is-column-over": "bg-accent",
};

const idle = { type: "idle" } satisfies TColumnState;

/**
 * A memoized component for rendering out the tag.
 *
 * Created so that state changes to the column don't require all tags to be rendered
 */
const TagList = memo(function TagList({ column }: { column: TColumn }) {
  return column.tags.map((tag: TTag) => (
    <TagComponent key={tag.id} tag={tag} columnId={column.id} />
  ));
});

export function Column({
  column,
  canDelete,
}: {
  column: TColumn;
  canDelete: boolean;
}) {
  const scrollableRef = useRef<HTMLDivElement | null>(null);
  const outerFullHeightRef = useRef<HTMLDivElement | null>(null);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const innerRef = useRef<HTMLDivElement | null>(null);
  const [state, setState] = useState<TColumnState>(idle);

  // 使用标签操作 hook
  const { createTag, editGroup, deleteGroup } = useTagOperations();

  // 新建标签弹窗状态
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTagName, setNewTagName] = useState("");

  // 编辑标签组弹窗状态
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editGroupName, setEditGroupName] = useState("");

  // 删除确认弹窗状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // IME 输入法合成态（中文拼音等）
  const composingRef = useRef(false);

  // 处理创建新标签
  const handleCreateTag = async () => {
    if (newTagName.trim()) {
      try {
        await createTag(column.id, newTagName.trim());
        setNewTagName("");
        setShowCreateDialog(false);
      } catch (error) {
        console.error("创建标签失败:", error);
      }
    }
  };

  // 处理编辑标签组
  const handleEditGroup = async () => {
    if (editGroupName.trim()) {
      try {
        await editGroup(column.id, editGroupName.trim());
        setEditGroupName("");
        setShowEditDialog(false);
      } catch (error) {
        console.error("编辑标签组失败:", error);
      }
    }
  };

  // 处理删除标签组
  const handleDeleteColumn = async () => {
    try {
      await deleteGroup(column.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("删除标签组失败:", error);
    }
  };

  // 处理键盘事件（避免输入法合成中回车触发提交）
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // 兼容性判断：合成中 / isComposing 为 true / keyCode 229
    const isComposing = composingRef.current || (e.nativeEvent as any)?.isComposing || (e as any)?.keyCode === 229;
    if (isComposing) return;
    if (e.key === "Enter") {
      handleCreateTag();
    }
  };

  // 处理编辑标签组键盘事件（同样避免合成中回车触发）
  const handleEditKeyDown = (e: React.KeyboardEvent) => {
    const isComposing = composingRef.current || (e.nativeEvent as any)?.isComposing || (e as any)?.keyCode === 229;
    if (isComposing) return;
    if (e.key === "Enter") {
      handleEditGroup();
    }
  };

  useEffect(() => {
    const outer = outerFullHeightRef.current;
    const scrollable = scrollableRef.current;
    const header = headerRef.current;
    const inner = innerRef.current;
    invariant(outer);
    invariant(scrollable);
    invariant(header);
    invariant(inner);

    const data = getColumnData({ column });

    function setIsTagOver({
      data,
      location,
    }: {
      data: TTagData;
      location: DragLocationHistory;
    }) {
      const innerMost = location.current.dropTargets[0];
      const isOverChildTag = Boolean(
        innerMost && isTagDropTargetData(innerMost.data)
      );

      const proposed: TColumnState = {
        type: "is-tag-over",
        dragging: data.rect,
        isOverChildTag,
      };
      // optimization - don't update state if we don't need to.
      setState((current) => {
        if (isShallowEqual(proposed, current)) {
          return current;
        }
        return proposed;
      });
    }

    // Column 拖拽组件初始化
    return combine(
      draggable({
        element: header,
        getInitialData: () => data,
        onGenerateDragPreview({ source, location, nativeSetDragImage }) {
          const data = source.data;
          invariant(isColumnData(data));
          setCustomNativeDragPreview({
            nativeSetDragImage,
            getOffset: preserveOffsetOnSource({
              element: header,
              input: location.current.input,
            }),
            render({ container }) {
              // Clone the outer container to get the complete styling including rounded corners
              const outerRect = outerFullHeightRef.current?.getBoundingClientRect();
              const preview = outerFullHeightRef.current?.cloneNode(true);
              invariant(preview instanceof HTMLElement);
              invariant(outerRect);
              
              preview.style.width = `${outerRect.width}px`;
              preview.style.height = `${outerRect.height}px`;

              // rotation of native drag previews does not work in safari
              if (!isSafari()) {
                // preview.style.transform = "rotate(4deg)";
              }

              container.appendChild(preview);
            },
          });
        },
        onDragStart() {
          setState({ type: "is-dragging" });
        },
        onDrop() {
          setState(idle);
        },
      }),
      dropTargetForElements({
        element: outer,
        getData: () => data,
        canDrop({ source }) {
          return isDraggingATag({ source }) || isDraggingAColumn({ source });
        },
        getIsSticky: () => true,
        onDragStart({ source, location }) {
          if (isTagData(source.data)) {
            setIsTagOver({ data: source.data, location });
          }
        },
        onDragEnter({ source, location }) {
          if (isTagData(source.data)) {
            setIsTagOver({ data: source.data, location });
            return;
          }
          if (
            isColumnData(source.data) &&
            source.data.column.id !== column.id
          ) {
            setState({ type: "is-column-over" });
          }
        },
        onDropTargetChange({ source, location }) {
          if (isTagData(source.data)) {
            setIsTagOver({ data: source.data, location });
            return;
          }
        },
        onDragLeave({ source }) {
          if (
            isColumnData(source.data) &&
            source.data.column.id === column.id
          ) {
            return;
          }
          setState(idle);
        },
        onDrop() {
          setState(idle);
        },
      }),
      autoScrollForElements({
        canScroll({ source }) {
          return isDraggingATag({ source });
        },
        getConfiguration: () => ({
          maxScrollSpeed: "standard" as const,
        }),
        element: scrollable,
      }),
      unsafeOverflowAutoScrollForElements({
        element: scrollable,
        getConfiguration: () => ({
          maxScrollSpeed: "standard" as const,
        }),
        canScroll({ source }) {
          return isDraggingATag({ source });
        },
        getOverflow() {
          return {
            forTopEdge: {
              top: 1000,
            },
            forBottomEdge: {
              bottom: 1000,
            },
          };
        },
      })
    );
  }, [column]);

  return (
    <div
      className="flex w-full flex-shrink-0 select-none flex-col bg-white border border-[rgba(216,216,216,0.8)] rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-shadow"
      ref={outerFullHeightRef}
    >
      <div
        className={`flex max-h-full flex-col text-neutral-50 ${
          stateStyles[state.type]
        }`}
        ref={innerRef}
        {...{ [blockBoardPanningAttr]: true }}
      >
        {/* Extra wrapping element to make it easy to toggle visibility of content when a column is dragging over */}
        <div
          className={`flex max-h-full flex-col ${
            state.type === "is-column-over" ? "invisible" : ""
          }`}
        >
          <div
            className="flex flex-row items-center justify-between py-3 px-4 bg-[#F0F0FA] text-[#13123C] rounded-t-2xl"
            ref={headerRef}
          >
            <div className="text-base font-normal flex items-center gap-2 pl-0">
              <Tag size={16} className="text-[#13123C]" />
              {column.title}
              <div
                className="cursor-pointer rounded-full p-1 text-[#979797] hover:text-[#13123C] transition-colors"
                onClick={() => {
                  setEditGroupName(column.title);
                  setShowEditDialog(true);
                }}
              >
                <PencilLine size={16} />
              </div>
            </div>
            <div className="flex flex-row items-center gap-2 pr-0">
              <div
                style={{
                  display: canDelete ? "block" : "none",
                }}
                className="cursor-pointer rounded-full p-1 text-[#979797] hover:text-red-500 transition-colors"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash2 size={16} />
              </div>
              <div className="cursor-pointer rounded-full p-1 text-[#979797] hover:text-[#13123C] transition-colors">
                <GripVertical size={16} />
              </div>
            </div>
          </div>
          <div
            className="flex flex-row items-center px-4 py-8 gap-2 flex-wrap bg-white overflow-y-auto max-h-96 [overflow-anchor:none] [scrollbar-color:theme(colors.slate.600)_theme(colors.slate.700)] [scrollbar-width:thin] min-h-[120px]"
            ref={scrollableRef}
          >
            <TagList column={column} />
            {state.type === "is-tag-over" && !state.isOverChildTag ? (
              <div className="flex-shrink-0 px-3 py-1">
                <TagShadow dragging={state.dragging} />
              </div>
            ) : null}
            <div
              className="text-sm text-[#979797] text-nowrap flex items-center gap-1.5 hover:text-[#13123C] cursor-pointer"
              onClick={() => setShowCreateDialog(true)}
            >
              <Plus size={16} /> 新建标签
            </div>
          </div>
        </div>
      </div>

      {/* 新建标签弹窗 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogOverlay className="bg-transparent" />

        <DialogContent className="z-[9999] w-[400px] rounded-lg overflow-hidden">
          <DialogHeader>
            <DialogTitle>新建标签</DialogTitle>
            {/* <DialogDescription>
              请输入新标签的名称，标签名称用于对内容进行分类和组织。
            </DialogDescription> */}
          </DialogHeader>

          <div className="space-y-4">
            <Input
              placeholder="请输入标签名称"
              value={newTagName}
              onChange={(e) => setNewTagName(e.target.value)}
              onCompositionStart={() => (composingRef.current = true)}
              onCompositionEnd={() => (composingRef.current = false)}
              onKeyDown={handleKeyDown}
              autoFocus
            />
          </div>

          <DialogFooter>
            <Button
              size={"sm"}
              variant="outline"
              onClick={() => {
                setShowCreateDialog(false);
                setNewTagName("");
              }}
            >
              取消
            </Button>
            <Button
              size={"sm"}
              onClick={handleCreateTag}
              disabled={!newTagName.trim()}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑标签组弹窗 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogOverlay className="bg-transparent" />

        <DialogContent className="z-[9999] w-[400px] rounded-lg overflow-hidden">
          <DialogHeader>
            <DialogTitle>编辑标签组</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <Input
              placeholder="Tag Group"
              value={editGroupName}
              onChange={(e) => setEditGroupName(e.target.value)}
              onCompositionStart={() => (composingRef.current = true)}
              onCompositionEnd={() => (composingRef.current = false)}
              onKeyDown={handleEditKeyDown}
              autoFocus
            />
          </div>

          <DialogFooter>
            <Button
              size={"sm"}
              variant="outline"
              onClick={() => {
                setShowEditDialog(false);
                setEditGroupName("");
              }}
            >
              取消
            </Button>
            <Button
              size={"sm"}
              onClick={handleEditGroup}
              disabled={!editGroupName.trim()}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认弹窗 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogOverlay className="bg-transparent" />

        <DialogContent className="z-[9999] w-[400px] rounded-lg overflow-hidden">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定删除此标签组吗？删除后，所有嵌套标签将被删除。
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              size={"sm"}
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              取消
            </Button>
            <Button
              size={"sm"}
              variant="destructive"
              onClick={handleDeleteColumn}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
