import { Plus, Trash2 } from "lucide-react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useLLMConfigStore } from "@/store/llm-config-store";

interface LLMPanelProps {
  onClose: () => void;
  zIndex: number;
}

export const LLMPanel = ({ onClose, zIndex }: LLMPanelProps) => {
  // 从 store 获取状态和操作函数
  const {
    inputParams,
    outputParams,
    userPrompt,
    systemPrompt,
    addInputParam,
    removeInputParam,
    updateInputParam,
    addOutputParam,
    removeOutputParam,
    updateOutputParam,
    setUserPrompt,
    setSystemPrompt,
  } = useLLMConfigStore();

  return (
    <Sheet open={true} onOpenChange={(open) => !open && onClose()}>
      <SheetContent
        side="right"
        className="w-[400px] sm:w-[400px]"
        style={{ zIndex }}
      >
        <SheetHeader>
          <SheetTitle className="text-base">LLM 配置</SheetTitle>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto mt-4">
          {/* 输入模块 */}
          <div className="mb-5">
            <h3 className="text-sm font-medium mb-3">输入</h3>
            
            <div className="space-y-2">
              {inputParams.map((param) => (
                <div key={param.id} className="grid grid-cols-[1fr,1fr,auto] gap-2 items-end">
                  <div>
                    <Label htmlFor={`input-name-${param.id}`} className="text-xs">
                      参数名 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id={`input-name-${param.id}`}
                      value={param.name}
                      onChange={(e) =>
                        updateInputParam(param.id, "name", e.target.value)
                      }
                      placeholder="请输入参数名"
                      className="h-8 text-xs"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor={`input-value-${param.id}`} className="text-xs">参数值</Label>
                    <Input
                      id={`input-value-${param.id}`}
                      value={param.value}
                      onChange={(e) =>
                        updateInputParam(param.id, "value", e.target.value)
                      }
                      placeholder="请输入参数值（可选）"
                      className="h-8 text-xs"
                    />
                  </div>
                  <div className="flex justify-center">
                    {inputParams.length > 1 && (
                      <Button
                        onClick={() => removeInputParam(param.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <Button 
              onClick={addInputParam} 
              variant="outline" 
              className="w-full mt-3 h-7 text-xs flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              新增参数
            </Button>
          </div>

          <Separator className="my-4" />

          {/* Prompt 模块 */}
          <div className="mb-5">
            <div className="space-y-3">
              <div>
                <Label htmlFor="user-prompt" className="text-xs">
                  User Prompt
                </Label>
                <Textarea
                  id="user-prompt"
                  value={userPrompt}
                  onChange={(e) => setUserPrompt(e.target.value)}
                  placeholder="请输入用户提示词"
                  className="min-h-[60px] text-xs resize-none"
                />
              </div>
              
              <div>
                <Label htmlFor="system-prompt" className="text-xs">
                  System Prompt
                </Label>
                <Textarea
                  id="system-prompt"
                  value={systemPrompt}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                  placeholder="请输入系统提示词"
                  className="min-h-[60px] text-xs resize-none"
                />
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          {/* 输出模块 */}
          <div>
            <h3 className="text-sm font-medium mb-3">输出</h3>
            
            <div className="space-y-2">
              {outputParams.map((param) => (
                <div key={param.id} className="grid grid-cols-[1fr,1fr,auto] gap-2 items-end">
                  <div>
                    <Label htmlFor={`output-name-${param.id}`} className="text-xs">
                      输出名 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id={`output-name-${param.id}`}
                      value={param.outputName}
                      onChange={(e) =>
                        updateOutputParam(
                          param.id,
                          "outputName",
                          e.target.value
                        )
                      }
                      placeholder="请输入输出名"
                      className="h-8 text-xs"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor={`output-alias-${param.id}`} className="text-xs">别名</Label>
                    <Input
                      id={`output-alias-${param.id}`}
                      value={param.alias}
                      onChange={(e) =>
                        updateOutputParam(param.id, "alias", e.target.value)
                      }
                      placeholder="请输入别名（可选）"
                      className="h-8 text-xs"
                    />
                  </div>
                  <div className="flex justify-center">
                    {outputParams.length > 1 && (
                      <Button
                        onClick={() => removeOutputParam(param.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <Button 
              onClick={addOutputParam} 
              variant="outline" 
              className="w-full mt-3 h-7 text-xs flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              新增输出
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
