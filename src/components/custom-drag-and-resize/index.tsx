import {ResizeHandlers} from '@/components/ui/ResizeHandlers';
import {checkEdgeSnapByMousePosition, CustomResizableBox, PanelPosition, SnapPreview} from '@/pages/workspace/Panel';
import React, {FC, JSX, useRef, useState} from "react";
import Draggable, {DraggableData, DraggableEvent,} from "react-draggable";
import {ResizeCallbackData} from "react-resizable";

export const DragAndResize: FC<{
    panelPosition: PanelPosition,
    setPanelPosition: React.Dispatch<React.SetStateAction<PanelPosition>>,
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>,
    setDragPanel: (isDragging: boolean) => void,
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            id?: string;
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            id?: string;
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    },
    className: string,
    panelClassName: string,
    innerElement: JSX.Element,
    // PanelWrapper: JSX.Element,
    PanelWrapper: any,
    zIndex: number,
    extraEvent?: any,
    handlePanelClick?: () => void,
    onBumpTabPanel?: (panelId: string, dx: number) => void,
    otherPanels?: Array<{
        id?: string;
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>,
}> = (
    {
        panelPosition,
        className = '',
        panelClassName = '',
        innerElement = <></>,
        PanelWrapper,
        zIndex = 10,
        extraEvent = {},
        handlePanelClick = () => {
        },
        otherPanels = [],
        setPanelPosition,
        setPanelsPosition,
        setDragPanel,
        getAdjacentPanels,
        onBumpTabPanel
    }
) => {
    // const SIDEBAR_WIDTH = 64; // 侧边栏宽度常量（未使用，已移除）
    // 添加拖拽方向状态
    const dragDirectionRef = useRef<'left' | 'right' | null>(null);
    const lastMouseXRef = useRef<number>(0);
    const frameRequestedRef = useRef<boolean>(false);

    // 兜底：某些极端情况下 onStop 可能未触发，确保松手后恢复
    const didStopRef = useRef(false);
    const handleGlobalMouseUp = () => {
        // 让 react-draggable 先触发 onStop，再兜底
        setTimeout(() => {
            if (!didStopRef.current) {
                setSnapPreview(prev => ({ ...prev, visible: false }));
                setDragPanel(false);
            }
            window.removeEventListener('mouseup', handleGlobalMouseUp, true);
        }, 0);
    };

    // 面板拖拽开始处理函数（通用）
    const handlePanelDragStart = (e: DraggableEvent) => {
        e.preventDefault()
        handlePanelClick?.()

        // 记录初始鼠标位置
        const mouseX = (e as MouseEvent).clientX || 0;
        lastMouseXRef.current = mouseX;
        dragDirectionRef.current = null;

        setDragPanel(true);
        didStopRef.current = false;
        // 兜底监听，保证鼠标松开时一定复原（不干扰 onStop）
        window.addEventListener('mouseup', handleGlobalMouseUp, true);
    };

    // 处理调整大小开始
    const handleResizeStart = (
        _e: React.SyntheticEvent,
        _data: ResizeCallbackData
    ) => {
        if (panelPosition.isSnapped) {
            setPanelPosition({
                ...panelPosition,
                isSnapped: false,
                originalSize: undefined
            });
            // 不恢复原始大小，保持当前大小
        }
        handlePanelClick?.()
        extraEvent?.handlePdfResizeStart?.()
    };

    // 添加贴边预览状态
    const [snapPreview, setSnapPreview] = useState<SnapPreview>({
        visible: false,
        edge: null,
        previewPosition: {x: 0, y: 0, width: 0, height: 0},
        snapPosition: {x: 0, y: 0, width: 0, height: 0},
        opacity: 1
    });

    // 处理聊天面板拖拽
    const handlePanelDrag = (e: DraggableEvent, data: DraggableData) => {
        e.preventDefault();
        if (frameRequestedRef.current) return;
        frameRequestedRef.current = true;

        requestAnimationFrame(() => {
            frameRequestedRef.current = false;

            const {x, y} = data;
            const panelWidth = panelPosition.width;
            const panelHeight = panelPosition.height;
            const minVisibleWidth = 100;

            // 只限制水平方向，垂直方向由Draggable的bounds属性控制
            const boundedX = Math.max(-panelWidth + minVisibleWidth, Math.min(x, window.innerWidth - minVisibleWidth));

            // 获取鼠标位置
            const mouseX = (e as MouseEvent).clientX || 0;
            const mouseY = (e as MouseEvent).clientY || 0;

            // 检测拖拽方向（使用 ref 避免每帧触发重渲染）
            if (Math.abs(mouseX - lastMouseXRef.current) > 5) {
                dragDirectionRef.current = mouseX > lastMouseXRef.current ? 'right' : 'left';
                lastMouseXRef.current = mouseX;
            }

            const {leftAdjacentPanels, rightAdjacentPanels} = getAdjacentPanels(otherPanels);
            // 使用鼠标位置检查是否需要贴边
            const snapInfo = checkEdgeSnapByMousePosition(
                mouseX,
                mouseY,
                panelWidth,
                panelHeight,
                leftAdjacentPanels,
                rightAdjacentPanels,
                dragDirectionRef.current
            );

            if (snapInfo) {
                // 显示贴边预览（不再赋值 opacity，展示由 snapPreview.visible 控制）
                setSnapPreview(prev => ({
                    ...prev,
                    visible: true,
                    edge: snapInfo.edge as "left" | "right" | "top" | "bottom" | "panel-left" | "panel-right",
                    previewPosition: snapInfo.previewPosition,
                    snapPosition: snapInfo.snapPosition,
                }));
            } else {
                // 隐藏贴边预览
                setSnapPreview(prev => ({
                    ...prev,
                    visible: false,
                    edge: null,
                    previewPosition: {x: 0, y: 0, width: 0, height: 0},
                    snapPosition: {x: 0, y: 0, width: 0, height: 0},
                }));
            }

            setPanelPosition({
                ...panelPosition,
                x: boundedX,
                y: y // 使用原始y值，不做额外限制
            });
        });
    };

    // 处理聊天面板拖拽结束
    const handlePanelDragStop = () => {
        didStopRef.current = true;
        setDragPanel(false)
        window.removeEventListener('mouseup', handleGlobalMouseUp, true);
        
        const {leftAdjacentPanels, rightAdjacentPanels} = getAdjacentPanels(otherPanels)
        // 如果有贴边预览，应用贴边效果
        if (snapPreview.visible) {
            // eslint-disable-next-line no-console
            console.log('[Snap] apply', {
                edge: snapPreview.edge,
                snap: snapPreview.snapPosition,
                preview: snapPreview.previewPosition
            });
            // 保存原始大小
            const originalSize = {
                width: panelPosition.width,
                height: panelPosition.height,
            };

            // 移动panel
            if (snapPreview.edge === "panel-left") {
                for (const panel of leftAdjacentPanels) {
                    if (panel.x >= snapPreview.snapPosition.x) {
                        switch (panel.type) {
                            case 'chat':
                                // eslint-disable-next-line no-console
                                console.log('[Snap] push chat +dx', snapPreview.snapPosition.width);
                                setPanelsPosition.chat(prev => ({
                                    ...prev,
                                    x: prev.x + snapPreview.snapPosition.width,
                                    isSnapped: true,
                                }))
                                break
                            case 'pdf':
                                // eslint-disable-next-line no-console
                                console.log('[Snap] push pdf +dx', snapPreview.snapPosition.width);
                                setPanelsPosition.pdf(prev => ({
                                    ...prev,
                                    x: prev.x + snapPreview.snapPosition.width,
                                    isSnapped: true,
                                }))
                                break
                            case 'note':
                                // eslint-disable-next-line no-console
                                console.log('[Snap] push note +dx', snapPreview.snapPosition.width);
                                setPanelsPosition.note(prev => ({
                                    ...prev,
                                    x: prev.x + snapPreview.snapPosition.width,
                                    isSnapped: true,
                                }))
                                break
                            case 'tab':
                                if (panel.id && onBumpTabPanel) {
                                    // eslint-disable-next-line no-console
                                    console.log('[Snap] push tab +dx', { panelId: panel.id, dx: snapPreview.snapPosition.width });
                                    onBumpTabPanel(panel.id, +snapPreview.snapPosition.width);
                                }
                        }
                    }
                }
            } else {
                for (const panel of rightAdjacentPanels) {
                    if (panel.x + panel.width <= snapPreview.snapPosition.x + snapPreview.snapPosition.width) {
                        switch (panel.type) {
                            case 'chat':
                                // eslint-disable-next-line no-console
                                console.log('[Snap] push chat -dx', snapPreview.snapPosition.width);
                                setPanelsPosition.chat(prev => ({
                                    ...prev,
                                    x: prev.x - snapPreview.snapPosition.width,
                                    isSnapped: true,
                                }))
                                break
                            case 'pdf':
                                // eslint-disable-next-line no-console
                                console.log('[Snap] push pdf -dx', snapPreview.snapPosition.width);
                                setPanelsPosition.pdf(prev => ({
                                    ...prev,
                                    x: prev.x - snapPreview.snapPosition.width,
                                    isSnapped: true,
                                }))
                                break
                            case 'note':
                                // eslint-disable-next-line no-console
                                console.log('[Snap] push note -dx', snapPreview.snapPosition.width);
                                setPanelsPosition.note(prev => ({
                                    ...prev,
                                    x: prev.x - snapPreview.snapPosition.width,
                                    isSnapped: true,
                                }))
                                break
                            case 'tab':
                                if (panel.id && onBumpTabPanel) {
                                    // eslint-disable-next-line no-console
                                    console.log('[Snap] push tab -dx', { panelId: panel.id, dx: -snapPreview.snapPosition.width });
                                    onBumpTabPanel(panel.id, -snapPreview.snapPosition.width);
                                }
                        }
                    }
                }
            }

            // 应用贴边效果 - 使用snapPosition而不是previewPosition
            const snapPosition = {
                x: snapPreview.snapPosition.x,
                y: snapPreview.snapPosition.y,
                width: snapPreview.snapPosition.width,
                height: snapPreview.snapPosition.height,
                isSnapped: true,
                originalSize,
            };
            setPanelPosition(snapPosition);
            // 应用后立即隐藏预览（不修改 opacity）
            setSnapPreview((prev) => ({...prev, visible: false}));
            
            // 吸附后调用 onDragStop 回调，保存吸附后的位置
            if (extraEvent?.onDragStop) {
                extraEvent.onDragStop(snapPosition);
            }

        } else {
            // 没有贴边也要确保隐藏预览
            setSnapPreview((prev) => ({...prev, visible: false}));
            
            // 没有吸附时，使用当前位置调用 onDragStop
            if (extraEvent?.onDragStop) {
                extraEvent.onDragStop(panelPosition);
            }
        }
    };

    // 处理面板大小调整
    const handleResize = (
        _event: React.SyntheticEvent,
        {size, handle}: ResizeCallbackData
    ) => {
        const {width, height} = size;
        const newPosition = {...panelPosition};

        // 根据调整手柄的位置更新面板位置
        if (handle.includes("w")) {
            // 左侧调整
            const deltaWidth = panelPosition.width - width;
            newPosition.x = panelPosition.x + deltaWidth;
        }

        if (handle.includes("n")) {
            // 顶部调整
            const deltaHeight = panelPosition.height - height;
            newPosition.y = panelPosition.y + deltaHeight;
        }

        // 更新尺寸
        newPosition.width = width;
        newPosition.height = height;

        setPanelPosition(newPosition);
    };

    return (
        <>
            {/* 贴边预览蒙版 */}
            {snapPreview.visible && (
                <div
                    style={{
                        position: 'fixed',
                        left: snapPreview.previewPosition.x,
                        top: snapPreview.previewPosition.y,
                        width: snapPreview.previewPosition.width,
                        height: snapPreview.previewPosition.height,
                        background: 'rgba(24, 144, 255, 0.3)', // 半透明蓝色
                        zIndex: 99999, // 确保贴边预览在最顶层
                        pointerEvents: 'none',
                        borderRadius: '4px',
                        border: '2px solid #1890ff',
                    }}
                />
            )}

            <Draggable
                handle=".drag-handle"
                position={{x: panelPosition.x, y: panelPosition.y}}
                onStart={handlePanelDragStart}
                onDrag={handlePanelDrag}
                onStop={handlePanelDragStop}
                bounds={{top: 0, bottom: window.innerHeight - panelPosition.height}} // 只限制顶部不能超出视口
            >
                <PanelWrapper
                    style={{
                        ...panelStyle,
                        zIndex: zIndex,
                        // 仅在触发贴边预览时显示浅灰，否则不透明
                        opacity: snapPreview.visible ? 0.5 : 1,
                    }}
                    onClick={() => handlePanelClick?.()}
                >
                    <CustomResizableBox
                        width={panelPosition.width}
                        height={panelPosition.height}
                        onResize={handleResize}
                        onResizeStart={handleResizeStart}
                        onResizeStop={() => {
                            // 调整大小结束时，调用外部回调
                            if (extraEvent?.onResizeStop) {
                                extraEvent.onResizeStop(panelPosition);
                            }
                        }}
                        minConstraints={[300, 300]}
                        maxConstraints={[window.innerWidth, window.innerHeight]}
                        resizeHandles={panelPosition.resizeHandles ? panelPosition.resizeHandles.filter((handle) => ['se', 'sw', 'ne', 'nw'].includes(handle)) : ['se', 'sw', 'ne', 'nw']}
                        className={className}
                    >
                        <div
                            style={{
                                width: '100%',
                                height: '100%',
                                position: "relative",
                            }}
                        >
                            <div className="h-full">
                                {
                                    innerElement
                                }
                            </div>
                            <ResizeHandlers
                                panelClassName={panelClassName}
                                currentSize={{width: panelPosition.width, height: panelPosition.height}}
                                setSize={(updater) => {
                                    const sizePatch = (updater as (prev: any) => any)({
                                        width: panelPosition.width,
                                        height: panelPosition.height,
                                    });
                                    setPanelPosition({
                                        ...panelPosition,
                                        width: sizePatch.width ?? panelPosition.width,
                                        height: sizePatch.height ?? panelPosition.height,
                                    });
                                }}
                                position={panelPosition}
                                setPosition={(updater) => {
                                    if (typeof updater === 'function') {
                                        const next = (updater as (prev: PanelPosition) => PanelPosition)(panelPosition);
                                        setPanelPosition(next);
                                    } else {
                                        setPanelPosition(updater as PanelPosition);
                                    }
                                }}
                                minSize={{width: 300, height: 300}}
                                customRightEdge={true}
                                customBottomEdge={true}
                                customLeftEdge={true}
                                customTopEdge={true}
                                showTopEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('n') : true}
                                showBottomEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('s') : true}
                                showLeftEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('w') : true}
                                showRightEdge={panelPosition.resizeHandles ? panelPosition.resizeHandles.includes('e') : true}
                                onResizeStart={extraEvent?.onResizeStart}
                                onResizeStop={() => {
                                    // 调整大小结束时，传递最新的位置信息
                                    if (extraEvent?.onResizeStop) {
                                        extraEvent.onResizeStop(panelPosition);
                                    }
                                }}
                            />
                        </div>
                    </CustomResizableBox>
                </PanelWrapper>
            </Draggable>
        </>
    )
}

const panelStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
}