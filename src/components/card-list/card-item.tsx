import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "../ui/card";
import { Badge } from "../ui/badge";
import { Remark } from "react-remark";
import { CardItemProps } from "./types";

export const CardItem = ({ data, onClick }: CardItemProps) => {
  return (
    <Card
      className="relative aspect-square cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-200 border-border/50 hover:border-border group"
      onClick={() => onClick?.(data.id)}
    >
      <CardHeader className="pb-2 px-4 pt-4">
        <CardTitle className="text-sm font-semibold truncate leading-tight group-hover:text-primary transition-colors">
          {data.title || "暂无标题"}
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-2 h-full flex flex-col justify-between">
        <p className="text-xs text-muted-foreground line-clamp-4 leading-relaxed flex-1">
          <Remark>{data.content}</Remark>
        </p>
        {/* Badge 在右下角 */}
        <div className="flex justify-end mt-3">
          <Badge
            // variant={typeConfig.variant}
            className="text-xs px-2 py-1 font-medium"
          >
            {data.type}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
};