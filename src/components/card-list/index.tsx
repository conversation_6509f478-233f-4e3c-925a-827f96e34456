import { useState } from "react";
import { CardItem } from "./card-item";
import { CardListFilter } from "./card-list-filter";
import { useCardNodes, usePdfData, useCardFilters } from "./hooks";
import { CardFilters } from "./types";

const CardList = ({
  onClose,
  zIndex,
}: {
  onClose: () => void;
  zIndex: number;
}) => {
  const [filters, setFilters] = useState<CardFilters>({
    cardType: [],
    tag: [],
    search: "",
  });

  // 使用自定义钩子
  const { nodes, loading } = useCardNodes();
  const { pdfs } = usePdfData();
  const data_list = useCardFilters(nodes, pdfs, filters);

  const handleCardClick = (id: string) => {
    console.log("Card clicked:", id);
    // TODO: 处理卡片点击事件
  };

  return (
    <div
      className=" absolute left-0 right-0 top-0 bottom-0 z-[1001] bg-white p-6"
      style={{ zIndex }}
    >
      <div className="flex flex-col gap-6 h-full">
        {/* 筛选器顶部区域 */}
        <div className="flex-shrink-0">
          <CardListFilter onChange={setFilters} />
          {/* {data_list.length} */}
        </div>
        {/* 分割线 */}
        <div className="h-px bg-border" />
        {/* 卡片网格区域 */}
        <div className="flex-1 overflow-auto py-2">
          {loading ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                {data_list.map((item) => (
                  <CardItem
                    key={item.id}
                    data={item}
                    onClick={handleCardClick}
                  />
                ))}
              </div>

              {data_list.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">暂无内容</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CardList;
