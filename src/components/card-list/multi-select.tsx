import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

export interface MultiSelectOption {
  label: string;
  value: string;
}

interface MultiSelectProps {
  options: MultiSelectOption[];
  selected: string[];
  onSelectionChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  contentWidth?: string | number; // 下拉菜单内容宽度
  width?: string | number; // 按钮宽度
}

export function MultiSelect({
  options,
  selected,
  onSelectionChange,
  placeholder = "Select options...",
  className,
  disabled = false,
  contentWidth = "14rem", // 默认固定宽度 w-56 (14rem)
  width = "220px", // 按钮默认宽度
}: MultiSelectProps) {
  const handleCheckedChange = (value: string, checked: boolean | "indeterminate") => {
    if (checked === true) {
      onSelectionChange([...selected, value]);
    } else {
      onSelectionChange(selected.filter((item) => item !== value));
    }
  };

  const selectedOptions = options.filter((option) =>
    selected.includes(option.value)
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn("justify-between h-auto min-h-8", className)}
          disabled={disabled}
          style={{ width }}
        >
          <div className="flex gap-1 flex-1 text-left overflow-hidden">
            {selected.length === 0 ? (
              <span className="text-muted-foreground truncate">{placeholder}</span>
            ) : (
              <div className="flex gap-1 overflow-hidden">
                {selectedOptions.slice(0, 3).map((option) => (
                  <Badge
                    key={option.value}
                    variant="secondary"
                    className="text-xs px-2 py-0 h-5 shrink-0"
                  >
                    {option.label}
                  </Badge>
                ))}
                {selectedOptions.length > 3 && (
                  <Badge
                    variant="secondary"
                    className="text-xs px-2 py-0 h-5 shrink-0"
                  >
                    +{selectedOptions.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        className="z-[9999]" 
        align="start"
        style={{ width: contentWidth === "auto" ? "auto" : (contentWidth || width) }}
      >
        {options.length === 0 ? (
          <div className="px-2 py-1.5 text-sm text-muted-foreground">
            No options available
          </div>
        ) : (
          options.map((option) => (
            <DropdownMenuCheckboxItem
              key={option.value}
              checked={selected.includes(option.value)}
              onCheckedChange={(checked) =>
                handleCheckedChange(option.value, checked)
              }
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              {option.label}
            </DropdownMenuCheckboxItem>
          ))
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}