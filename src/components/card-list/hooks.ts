import { useState, useEffect, useMemo } from "react";
import { canvasService, workspaceService } from "@/local";
import { CustomHighlight, usePdfStore } from "@/store/pdf-store";
import { NODE_TYPE_MAPS } from "../canvas/components/node";
import { getWsId } from "@/tools/params";
import { includes, toLower } from "lodash";
import { CardItemData, CardFilters } from "./types";

// 获取节点数据钩子
export const useCardNodes = () => {
  const [nodes, setNodes] = useState<CardItemData[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchNodes = async () => {
    const wid = getWsId();
    try {
      const _allCanvas = (await canvasService.getNodeContentList({ wid: wid }))
        .list;
      const _files = await workspaceService.getFilesContentByWid(wid);
      const nodes: CardItemData[] = [
        ..._allCanvas
          .map((item: any) => ({
            id: item.id,
            title: item.data.title,
            content: item.data.content,
            color: item.data.color,
            type: item.type,
            tags: item.data.tags,
          }))
          .filter((item) =>
            [NODE_TYPE_MAPS.markdown, NODE_TYPE_MAPS.groupNode].includes(
              item.type
            )
          ),
        ..._files.map((item: any) => ({
          id: item.aid,
          title: item.title,
          content: item.content,
          type: "pdf",
          tags: [],
        })),
      ];

      setNodes(nodes || []);

      // 调试：查看节点数据
      console.log("获取到的节点数量:", nodes?.length || 0);
      if (nodes && nodes.length > 0) {
        console.log("节点示例:", nodes.slice(0, 2));
      }
    } catch (error) {
      console.error("获取节点列表失败：", error);
      setNodes([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNodes();
  }, []);

  return { nodes, loading, refetch: fetchNodes };
};

// PDF数据处理钩子
export const usePdfData = () => {
  const [pdfs, setPdfs] = useState<any[]>([]);
  const pdfStoreData = usePdfStore((state) => state.pdfs);

  const processPdfData = () => {
    const pdfArray = [];

    for (const [aid, pdfData] of pdfStoreData) {
      // 提取所有高亮文本
      const highlightTexts =
        pdfData.highlights
          ?.map((highlight: CustomHighlight) => highlight.content?.text || "")
          .filter((text) => text.trim() !== "")
          .join(" ") || "";

      pdfArray.push({
        aid,
        filename: pdfData.filename,
        url: pdfData.url,
        highlightTexts,
        highlightCount: pdfData.highlights?.length || 0,
      });
    }

    setPdfs(pdfArray);

    console.log("获取到的PDF数量:", pdfArray.length);
    if (pdfArray.length > 0) {
      console.log("PDF示例:", pdfArray.slice(0, 2));
    }
  };

  useEffect(() => {
    processPdfData();
  }, [pdfStoreData]);

  return { pdfs, processPdfData };
};

// 卡片筛选钩子
export const useCardFilters = (nodes: CardItemData[], pdfs: any[], filters: CardFilters) => {
  const filteredData = useMemo(() => {
    return [...nodes.map((node: any) => ({ ...node, ...node.data }))].filter(
      (item) => {
        let isMatch = true;
        
        // 卡片类型筛选 - 空数组表示显示所有类型
        if (filters.cardType.length > 0) {
          isMatch = isMatch && filters.cardType.includes(item.type);
        }
        
        // 标签筛选 - 空数组表示显示所有标签
        if (filters.tag.length > 0) {
          // 检查item是否包含任何选中的标签
          const hasSelectedTag = filters.tag.some(selectedTag => 
            item.tags && item.tags.includes(selectedTag)
          );
          isMatch = isMatch && hasSelectedTag;
        }
        
        // 搜索筛选
        if (filters.search) {
          console.log("filters.search", filters.search);
          console.log("item.title", item.title);
          console.log("item.content", item.content);
          isMatch = isMatch && (
            includes(toLower(item.title), toLower(filters.search)) ||
            includes(toLower(item.content), toLower(filters.search))
          );
        }
        
        return isMatch;
      }
    );
  }, [nodes, pdfs, filters]);

  return filteredData;
};