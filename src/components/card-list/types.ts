import { NODE_TYPE_MAPS } from "../canvas/components/node";

// 卡片数据类型
export interface CardItemData {
  id: string;
  title: string;
  content: string;
  type: string;
  color?: string;
  tags?: string[];
}

export interface CardItemProps {
  data: CardItemData;
  onClick?: (id: string) => void;
}

// 筛选器相关类型
export interface FilterOption {
  label: string;
  value: string;
}

export interface CardFilters {
  cardType: string[];
  tag: string[];
  search: string;
}

// 卡片类型选项常量
export const CARD_TYPE_OPTIONS: FilterOption[] = [
  {
    label: "Markdown",
    value: NODE_TYPE_MAPS.markdown,
  },
  {
    label: "PDF",
    value: "pdf",
  },
  {
    label: "Group",
    value: NODE_TYPE_MAPS.groupNode,
  },
];