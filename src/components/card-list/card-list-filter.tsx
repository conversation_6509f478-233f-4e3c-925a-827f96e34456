import { Layers, Search, Tag } from "lucide-react";
import { Input } from "../ui/input";
import { useState, useEffect } from "react";
import {
  useTagGroups,
  useTagStore,
} from "../tag-management/stores/useTagStore";
import { CARD_TYPE_OPTIONS, CardFilters } from "./types";
import { MultiSelect } from "./multi-select";

interface CardListFilterProps {
  onChange: (filters: CardFilters) => void;
}

export const CardListFilter = ({ onChange }: CardListFilterProps) => {
  const [filters, setFilters] = useState<CardFilters>({
    cardType: [],
    tag: [],
    search: "",
  });

  const tagGroups = useTagGroups();
  const { loadTagGroups } = useTagStore();

  useEffect(() => {
    loadTagGroups();
  }, [loadTagGroups]);

  const handleFilterChange = (filterKey: string, values: string[]) => {
    const newFilters = {
      ...filters,
      [filterKey]: values,
    };
    setFilters(newFilters);
    console.log("Filter changed:", filterKey, values);
    onChange(newFilters);
  };

  // 动态生成标签选项 (移除 "All" 选项，因为多选中空数组表示全选)
  const tagOptions = tagGroups.flatMap((group) =>
    group.tags.map((tag) => ({
      label: `${group.title}: ${tag.name}`,
      value: tag.id,
    }))
  );

  // 卡片类型选项
  const cardTypeOptions = CARD_TYPE_OPTIONS;

  const onSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = {
      ...filters,
      search: e.currentTarget.value,
    };
    setFilters(newFilters);
    onChange(newFilters);
  };

  return (
    <div className="bg-card rounded-lg">
      <h2 className="text-lg my-4 font-semibold text-foreground flex items-center gap-2">
        <Layers size={24} />
        Card List
      </h2>
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-end">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          {/* Card Type 多选 */}
          <div className="space-y-2 min-w-[160px]">
            <label className="flex gap-2 items-center text-sm font-medium text-muted-foreground">
              <Layers size={18} /> Card Type
            </label>
            <MultiSelect
              options={cardTypeOptions}
              selected={filters.cardType}
              onSelectionChange={(values) => handleFilterChange("cardType", values)}
              placeholder="Select card types..."
            />
          </div>

          {/* Tag 多选 */}
          <div className="space-y-2 min-w-[160px]">
            <label className="flex gap-2 items-center text-sm font-medium text-muted-foreground">
              <Tag size={18} /> Tag
            </label>
            <MultiSelect
              options={tagOptions}
              selected={filters.tag}
              onSelectionChange={(values) => handleFilterChange("tag", values)}
              placeholder="Select tags..."
            />
          </div>
          <div className="flex items-center gap-2 flex-col">
            <label className="text-sm font-medium text-muted-foreground invisible">
              关键词搜索
            </label>
            <div className="relative w-full">
              <Search
                className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                size={16}
              />
              <Input
                onChange={onSearch}
                className="w-full h-8 pl-8"
                placeholder="Search"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};