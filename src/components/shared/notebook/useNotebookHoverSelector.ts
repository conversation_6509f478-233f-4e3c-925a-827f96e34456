import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { notebookService } from '@/local/services/notebook.service';
import { AttachmentDocType } from '@/local/db/schemas/attachment';
import { useWorkerSpaceStore } from '@/store/workerspace-store/store';
import { _tempInsertContent } from '@/components/lexicalEditor/Editor';
import { ResourceEditor } from '@/components/lexicalEditor/App';
import { useFileSelectionStore } from '@/store/file-selection-store';
import { EditorEvents, EDITOR_EVENTS } from '@/local/services/editor-events';
import { usePanelOpenStore } from '@/store/panel-open-store.tsx';
import { usePdfStore } from '@/store/pdf-store';
import React from 'react';

export interface UseNotebookHoverSelectorOptions {
  /** 内容生成函数 */
  generateContent: () => string;
  /** 成功复制后的回调 */
  onSuccess?: (notebook: AttachmentDocType) => void;
  /** 失败时的回调 */
  onError?: (error: any) => void;
  /** 位置偏移配置 */
  positionOffset?: {
    top?: number;
    left?: number;
  };
}

export const useNotebookHoverSelector = (options: UseNotebookHoverSelectorOptions) => {
  const [selectorVisible, setSelectorVisible] = useState(false);
  const [selectorPosition, setSelectorPosition] = useState<{top: number, left: number, width: number} | null>(null);
  const [currentContent, setCurrentContent] = useState<string>('');
  const [lastSelectedNotebook, setLastSelectedNotebook] = useState<AttachmentDocType | null>(null);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  
  const wid = useWorkerSpaceStore(state => state.wid) as string;
  
  // PDF Store for tab management
  const { setActiveAid, setTabItems } = usePdfStore((state) => ({
    setActiveAid: state.setActiveAid,
    setTabItems: state.setTabItems
  }));
  
  // File selection store for tree selection
  const { selectFile, triggerRefresh } = useFileSelectionStore();
  
  // Panel control
  const { togglePdfPanel } = usePanelOpenStore((state) => ({
    togglePdfPanel: state.togglePdfPanel,
  }));

  // 打开笔记本预览
  const openNotebookPreview = useCallback((notebook: AttachmentDocType) => {
    const state = usePdfStore.getState();
    const aid = notebook.id;
    
    // 切换到 PDF 面板（资源模块）
    togglePdfPanel(true);
    
    // 设置活动文件
    setActiveAid(aid);
    
    // 检查是否已有该文件的tab
    if (state.tabItems?.find((item) => item.key === aid)) {
      // 如果 tab 已存在，只需要激活它
      selectFile(aid);
      // 通知对应编辑器刷新
      EditorEvents.emit(EDITOR_EVENTS.RELOAD_REQUEST, aid);
      return;
    }
    
    // 创建新的预览tab
    const items = [...(state.tabItems || []), {
      key: aid,
      label: notebook.file_name || '笔记本',
      children: React.createElement(ResourceEditor, {
        id: aid,
        onChange: (v: string) => {
          console.log('笔记本内容变化:', v);
        }
      })
    }];
    setTabItems(items);
    
    // 通知文件树选中这个文件
    selectFile(aid);
  }, [setActiveAid, setTabItems, togglePdfPanel, selectFile]);

  // 显示选择器
  const showSelector = useCallback((event: React.MouseEvent<HTMLElement>) => {
    const content = options.generateContent();
    if (!content || !content.trim()) {
      toast.error('内容为空，无法复制到笔记本');
      return;
    }

    setCurrentContent(content);

    // 计算位置
    const rect = event.currentTarget.getBoundingClientRect();
    const offset = options.positionOffset || {};
    
    const position = {
      top: rect.bottom + 4 + (offset.top || 0),
      left: rect.left + (offset.left || 0),
      width: rect.width
    };
    
    setSelectorPosition(position);
    setSelectorVisible(true);
  }, [options.generateContent, options.positionOffset]);

  // 立即隐藏选择器
  const hideSelector = useCallback(() => {
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
    setSelectorVisible(false);
    setSelectorPosition(null);
    setCurrentContent('');
  }, [hideTimeout]);

  // 延迟隐藏选择器
  const startHideSelector = useCallback(() => {
    if (hideTimeout) {
      clearTimeout(hideTimeout);
    }
    const timeout = setTimeout(() => {
      setSelectorVisible(false);
      setSelectorPosition(null);
      setCurrentContent('');
      setHideTimeout(null);
    }, 300);
    setHideTimeout(timeout);
  }, [hideTimeout]);

  // 取消延迟隐藏
  const cancelHideSelector = useCallback(() => {
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
  }, [hideTimeout]);

  // 获取上次选择的笔记本
  const getLastSelectedNotebook = useCallback(async (): Promise<AttachmentDocType | null> => {
    try {
      const savedNotebookId = localStorage.getItem(`lastSelectedNotebook_${wid}`);
      if (!savedNotebookId) return null;

      const notebooks = await notebookService.getAllNotebooks(wid);
      const lastNotebook = notebooks.find(nb => nb.id === savedNotebookId);
      return lastNotebook || null;
    } catch (error) {
      console.error('获取上次选择的笔记本失败:', error);
      return null;
    }
  }, [wid]);

  // 复制到指定笔记本（通用方法）
  const copyToNotebook = useCallback(async (notebook: AttachmentDocType, content: string) => {
    try {
      // 更新笔记本内容
      const updatedContent = notebook.content ? 
        notebook.content + '\n\n' + content : 
        content;

      await notebookService.updateNotebook({
        id: notebook.id,
        content: updatedContent
      });

      // 记录选择
      setLastSelectedNotebook(notebook);
      localStorage.setItem(`lastSelectedNotebook_${wid}`, notebook.id);

      toast.success(`内容已复制到笔记本"${notebook.file_name}"`);
      
      // 打开笔记本预览
      openNotebookPreview(notebook);
      
      // 触发文件树刷新（延迟执行，避免与预览冲突）
      setTimeout(() => triggerRefresh(), 100);
      
      // 插入内容到编辑器
      setTimeout(() => {
        _tempInsertContent(content);
      }, 50);

      // 执行成功回调
      options.onSuccess?.(notebook);

    } catch (error) {
      console.error('复制到笔记本失败:', error);
      toast.error('复制到笔记本失败');
      options.onError?.(error);
      throw error;
    }
  }, [wid, setLastSelectedNotebook, openNotebookPreview, triggerRefresh, options]);

  // 选择笔记本并复制内容
  const selectNotebook = useCallback(async (notebook: AttachmentDocType) => {
    if (!currentContent) return;
    await copyToNotebook(notebook, currentContent);
  }, [currentContent, copyToNotebook]);

  // 快速复制到上次选择的笔记本
  const quickCopyToLastNotebook = useCallback(async () => {
    const content = options.generateContent();
    if (!content || !content.trim()) {
      toast.error('内容为空，无法复制到笔记本');
      return false;
    }

    try {
      const lastNotebook = await getLastSelectedNotebook();
      if (!lastNotebook) {
        toast.info('没有找到上次选择的笔记本，请先从列表中选择');
        return false;
      }

      await copyToNotebook(lastNotebook, content);
      return true;
    } catch (error) {
      return false;
    }
  }, [options.generateContent, getLastSelectedNotebook, copyToNotebook]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [hideTimeout]);

  return {
    selectorVisible,
    selectorPosition,
    showSelector,
    hideSelector,
    startHideSelector,
    cancelHideSelector,
    selectNotebook,
    quickCopyToLastNotebook,
    lastSelectedNotebook
  };
};