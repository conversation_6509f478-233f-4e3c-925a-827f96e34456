import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { BookOpen, Plus } from 'lucide-react';
import { toast } from 'sonner';
import { notebookService } from '@/local/services/notebook.service';
import { AttachmentDocType } from '@/local/db/schemas/attachment';
import { useWorkerSpaceStore } from '@/store/workerspace-store/store';
import { cn } from '@/lib/utils';

interface NotebookHoverSelectorProps {
  /** 触发元素的位置信息 */
  position: {top: number, left: number, width: number} | null;
  /** 是否显示选择器 */
  visible: boolean;
  /** 选择笔记本的回调 */
  onSelect: (notebook: AttachmentDocType) => void;
  /** 关闭选择器的回调 */
  onClose: () => void;
  /** 开始延迟关闭的回调 */
  onStartHide?: () => void;
  /** 取消延迟关闭的回调 */
  onCancelHide?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

export const NotebookHoverSelector: React.FC<NotebookHoverSelectorProps> = ({
  position,
  visible,
  onSelect,
  onClose,
  onStartHide,
  onCancelHide,
  className = ''
}) => {
  const [notebooks, setNotebooks] = useState<AttachmentDocType[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastSelectedNotebook, setLastSelectedNotebook] = useState<string | null>(null);
  
  const wid = useWorkerSpaceStore(state => state.wid) as string;

  // 加载笔记本列表
  const loadNotebooks = useCallback(async () => {
    if (!wid || !visible) return;
    
    setLoading(true);
    try {
      const notebookList = await notebookService.getAllNotebooks(wid);
      setNotebooks(notebookList);
      
      // 读取上次选择的笔记本
      const savedNotebook = localStorage.getItem(`lastSelectedNotebook_${wid}`);
      if (savedNotebook) {
        setLastSelectedNotebook(savedNotebook);
      }
    } catch (error) {
      console.error('加载笔记本列表失败:', error);
      toast.error('加载笔记本列表失败');
    } finally {
      setLoading(false);
    }
  }, [wid, visible]);

  useEffect(() => {
    if (visible) {
      loadNotebooks();
    }
  }, [visible, loadNotebooks]);


  // 选择笔记本
  const handleSelectNotebook = useCallback((notebook: AttachmentDocType) => {
    // 记录用户最后选择的笔记本
    setLastSelectedNotebook(notebook.id);
    localStorage.setItem(`lastSelectedNotebook_${wid}`, notebook.id);
    
    // 执行回调
    onSelect(notebook);
    onClose();
  }, [onSelect, onClose, wid]);

  if (!visible || !position) {
    return null;
  }

  const selectorContent = (
    <div
      className={cn(
        "fixed z-[10000] bg-popover border border-border rounded-md shadow-lg",
        "animate-in fade-in-0 zoom-in-95 duration-100",
        className
      )}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        minWidth: Math.max(position.width, 200),
        maxWidth: 300,
        transform: 'none' // 确保不受任何变换影响
      }}
      onMouseEnter={() => onCancelHide?.()}
      onMouseLeave={() => onStartHide?.()}
    >
      {/* 标题 */}
      <div className="px-3 py-2 border-b border-border">
        <div className="text-sm font-medium text-foreground flex items-center gap-2">
          <BookOpen className="w-4 h-4" />
          选择笔记本
        </div>
      </div>

      {/* 笔记本列表 */}
      <div className="max-h-48 overflow-y-auto">
        {loading ? (
          <div className="px-3 py-4 text-center">
            <div className="text-xs text-muted-foreground">加载中...</div>
          </div>
        ) : notebooks.length === 0 ? (
          <div className="px-3 py-4 text-center">
            <div className="text-xs text-muted-foreground">暂无笔记本</div>
          </div>
        ) : (
          // 将上次选择的笔记本排到前面
          [...notebooks]
            .sort((a, b) => {
              if (lastSelectedNotebook === a.id) return -1;
              if (lastSelectedNotebook === b.id) return 1;
              return 0;
            })
            .map((notebook) => {
              const isSelected = lastSelectedNotebook === notebook.id;
              return (
                <div
                  key={notebook.id}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 hover:bg-accent rounded-none cursor-pointer text-sm transition-colors",
                    isSelected && "bg-accent/50"
                  )}
                  onClick={() => handleSelectNotebook(notebook)}
                >
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <BookOpen className="w-3 h-3 text-muted-foreground" />
                    {isSelected && (
                      <div className="w-1 h-1 rounded-full bg-primary"></div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className={cn(
                      "truncate",
                      isSelected ? "font-semibold text-primary" : "font-medium"
                    )}>
                      {notebook.file_name || '未命名笔记本'}
                    </div>
                    {notebook.content && (
                      <div className="text-xs text-muted-foreground truncate">
                        {notebook.content.substring(0, 30)}
                        {notebook.content.length > 30 ? '...' : ''}
                      </div>
                    )}
                  </div>
                </div>
              );
            })
        )}
      </div>

      {/* 创建新笔记本 */}
      {/* <div className="border-t border-border">
        <div
          className="flex items-center gap-2 px-3 py-2 hover:bg-accent cursor-pointer text-sm transition-colors"
          onClick={() => {
            // TODO: 实现创建新笔记本功能
            toast.info('创建新笔记本功能即将推出');
            onClose();
          }}
        >
          <Plus className="w-3 h-3 text-muted-foreground" />
          <span className="text-muted-foreground">创建新笔记本</span>
        </div>
      </div> */}
    </div>
  );

  // 使用 Portal 渲染到 body，避免被画布变换影响
  return createPortal(selectorContent, document.body);
};