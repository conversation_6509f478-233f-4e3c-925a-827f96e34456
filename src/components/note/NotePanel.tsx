import {DragAndResize} from "@/components/custom-drag-and-resize";
import {useFetchData} from "@/components/note/hooks/fetch-data.ts";
import {useNoteFocus} from "@/components/note/hooks/note-focus.ts";
import {useNoteSave} from "@/components/note/hooks/note-save.ts";
import {PanelPosition, ResizableContainer,} from '@/pages/workspace/Panel';
import {useNoteStore} from "@/store/note-store.ts";
import {convertImageToBase64} from "@/tools/image.ts";
import {CloseOutlined} from "@ant-design/icons";
import {Button, Tooltip} from "antd";
import React, {FC, useEffect, useState} from "react";
import styled from "styled-components";
import Vditor from "vditor";
import "vditor/dist/index.css";
import "./style/vditor.css";

import { GlobalEditor } from "@/components/lexicalEditor/App";

const LexicalEditor = () => {

    return <div className="w-full h-full flex flex-col">
        <div
            className="w-full h- cursor-pointer drag-handle text-center leading-8 flex items-center justify-between px-4"
            style={{
                position: 'relative',
                paddingRight: 48,
            }}
        >
            <span style={{ flex: 1, textAlign: 'center' }}>记事本</span>
            <Tooltip title="关闭">
                <Button
                    type="text"
                    icon={<CloseOutlined />}
                    style={{ position: 'absolute', right: 28, top: 0, color: '#666', background: 'transparent' }}
                    onClick={() => {
                        // if (vd) {
                        //     saveNote(vd.getValue());
                        // }
                        window.dispatchEvent(new CustomEvent('notepad-close'));
                    }}
                    onMouseDown={e => e.stopPropagation()}
                />
            </Tooltip>
        </div>
        <div className="hide-scrollbar box-border" style={{ flex: 1, overflow: 'hidden', position: 'relative' }}>
            <GlobalEditor />
        </div>
    </div>
}


export const NotePanelMain: FC<{
    zIndex: number,
    panelPosition: PanelPosition,
    setPanelPosition: React.Dispatch<React.SetStateAction<PanelPosition>>,
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>,
    setDragPanel: (isDragging: boolean) => void,
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    },
    handlePanelClick?: () => void,
    onBumpTabPanel?: (panelId: string, dx: number) => void,
    otherPanels?: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>,
}> = ({
          panelPosition,
          zIndex = 10,
          otherPanels = [],
          ...rest
      }) => {

    return <DragAndResize
        panelPosition={panelPosition}
        className="note-panel-resizable"
        panelClassName="note-panel-resizable"
        zIndex={zIndex}
        // innerElement={<NotePanel/>}
        innerElement={<LexicalEditor/>}
        PanelWrapper={NotePanelWrapper}
        otherPanels={otherPanels}
        {...rest}
    />
}

// 为每个面板设置初始位置
const NotePanelWrapper = styled(ResizableContainer)`
    top: 20px;
    left: 20px;
    border-radius: 30px 30px 0 0;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    background-color: #E0E2F4;
`;