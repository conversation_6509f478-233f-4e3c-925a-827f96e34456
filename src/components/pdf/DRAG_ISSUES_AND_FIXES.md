# Tab 拖拽系统问题分析与修复方案

## 当前实现的问题

### 1. TabPanel 初始化重复
**问题描述**: 
- `createTabPanel` 和 `TabPanel` 组件都会初始化 Panel 状态
- 导致状态可能被覆盖

**修复方案**:
```typescript
// TabPanel.tsx - 修改初始化逻辑
useEffect(() => {
    const panel = panels.get(tabId);
    if (!panel) {
        // 只在 Panel 真的不存在时才创建
        // 如果是从 createTabPanel 创建的，这里不应该执行
        console.warn(`Panel ${tabId} 不存在，可能是初始化问题`);
    }
}, [tabId]);
```

### 2. 只支持单 Tab 拖拽
**问题描述**:
- 当前只能拖拽单个 tab
- 无法拖拽整个 Panel 或多选 tabs

**修复方案**:
```typescript
// drag-tab.ts - 支持多 tab 拖拽
const createTabPanel = useCallback((items: DragItem | DragItem[], clientOffset) => {
    const tabItems = Array.isArray(items) 
        ? items.map(i => i.tabItem)
        : [items.tabItem];
    
    // 创建包含多个 tabs 的 Panel
    createPanel(panelId, tabItems, tabItems[0].key);
});
```

### 3. 状态清理优化
**问题描述**:
- `dragHandled` 标志未自动重置
- 可能影响下次拖拽

**修复方案**:
```typescript
// drag-tab.ts - dragEnd 函数
const dragEnd = useCallback((item, result) => {
    // ... 现有逻辑
    
    // 清理拖拽状态
    setTimeout(() => {
        usePdfStore.setState({ 
            dragHandled: false,
            dragHoverWindowId: null,
            dragTabWindowId: null 
        });
    }, 100);
});
```

### 4. 拖拽边界检测优化
**当前逻辑**:
- 使用固定的边界值判断是否在容器外
- 可能在不同分辨率下表现不一致

**优化方案**:
```typescript
const checkIfOutsideContainer = (item, offset, container?) => {
    // 使用相对位置而不是绝对位置
    const rect = container?.getBoundingClientRect();
    // 更精确的边界检测
};
```

## 建议的重构方向

### 1. 统一状态管理
- 将所有 Panel 状态集中在 `pdf-store`
- `panel-open-store` 只管理位置和可见性

### 2. 拖拽行为分层
- **Tab 级别**: 单个 tab 的拖拽
- **Panel 级别**: 整个 Panel 的拖拽（未来功能）
- **批量操作**: 多选 tabs 的拖拽（未来功能）

### 3. 生命周期管理
```
创建 Panel:
1. createTabPanel (拖拽触发)
2. Panel 状态初始化 (pdf-store)
3. TabPanel 组件挂载
4. 内容渲染

销毁 Panel:
1. removeTabPanel (用户操作)
2. 清理 pdf-store 状态
3. 清理 panel-open-store 记录
4. 组件卸载
```

### 4. 性能优化建议
- 使用 `React.memo` 优化 TabPanel 渲染
- 拖拽时节流位置更新
- 大量 tabs 时使用虚拟滚动

## 测试用例建议

1. **基础拖拽**
   - [ ] 从主 Panel 拖出单个 tab
   - [ ] 拖回主 Panel
   - [ ] Panel 之间互相拖拽

2. **状态保持**
   - [ ] 隐藏/显示 Panel 后状态保持
   - [ ] 多个 tabs 的 Panel 隐藏后恢复

3. **边界情况**
   - [ ] 快速连续拖拽
   - [ ] 拖拽时切换工作区
   - [ ] 拖拽最后一个 tab

4. **性能测试**
   - [ ] 大量 tabs（20+）的拖拽性能
   - [ ] 多个 Panel 同时存在时的性能