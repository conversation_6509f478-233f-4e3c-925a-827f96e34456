# Panel 架构说明

## 概述
本系统支持多 Panel 管理，每个 Panel 可以独立显示和管理 PDF 文件。

## 架构设计

### 1. Panel 类型
- **主 Panel (PdfPanel)**：包含文件管理侧边栏的完整 PDF 面板
- **TabPanel**：不含文件管理侧边栏的独立 PDF 面板，通过拖拽标签页创建

### 2. 状态管理

#### Panel 独立状态
每个 Panel 维护独立的：
- `tabItems`：标签页列表
- `activeAid`：当前激活的文件 ID

#### 共享状态
所有 Panel 共享：
- PDF 文件实例（`pdfs` Map）
- 高亮数据（`highlights`）
- 操作模式（`mode`）

### 3. 拖拽功能

#### 支持的拖拽操作
1. **Tab 拖出成 Panel**：将标签页拖出窗口区域，创建新的 TabPanel
2. **Panel 间 Tab 拖拽**：在不同 Panel 之间拖拽标签页
3. **Tab 拖回主 Panel**：将 TabPanel 的标签拖回主 Panel

#### 拖拽实现
- 使用 `react-dnd` 实现拖拽功能
- 每个 Panel 有唯一的 `panelId` 或 `windowId` 标识
- 拖拽时自动识别源容器和目标容器

### 4. 同文件多开处理

#### 当前策略：共享实例
- 同一文件在多个 Panel 中打开时，共享同一个 PDF 实例
- 高亮等标注在所有 Panel 中实时同步
- 优点：节省内存，保持数据一致性
- 缺点：不能在不同 Panel 中有独立的标注

#### 实现细节
```typescript
// Panel 状态结构
type PanelState = {
    panelId: string;      // Panel 唯一标识
    tabItems: TabItem[];  // 标签页列表
    activeAid: string;    // 当前激活文件
}

// PDF 实例（全局共享）
type pdfSingleState = {
    aid: string;          // 文件 ID
    filename: string;     // 文件名
    url: string;         // 文件地址
    highlights: CustomHighlight[]; // 高亮（共享）
    pdfHighlighterUtils: PdfHighlighterUtils | null;
}
```

### 5. 使用示例

```typescript
// 创建新 TabPanel
const createTabPanel = (tabItem: TabItem, position: {x, y}) => {
    const panelId = nanoid();
    // 添加到 Panel 系统
    addTabPanel({
        id: panelId,
        label: tabItem.label,
        aid: tabItem.key,
        position
    });
    // 创建 Panel 状态
    createPanel(panelId, [tabItem], tabItem.key);
}

// Panel 间拖拽
const handleTabDrop = (sourcePanel: string, targetPanel: string, tabItem: TabItem) => {
    // 从源 Panel 移除
    removePanelTabItem(sourcePanel, tabItem.key);
    // 添加到目标 Panel
    addPanelTabItem(targetPanel, tabItem);
    setPanelActiveAid(targetPanel, tabItem.key);
}
```

### 6. 注意事项

1. **性能优化**
   - 避免重复创建 PDF 实例
   - 使用 React.memo 优化渲染

2. **状态同步**
   - 高亮变更需要触发所有相关 Panel 的重新渲染
   - 使用 Zustand 的 shallow 比较优化

3. **内存管理**
   - Panel 关闭时清理相关状态
   - PDF 实例在所有 Panel 都关闭后才释放

### 7. 未来改进

1. **独立标注模式**（可选功能）
   - 允许用户选择是否在不同 Panel 中使用独立的标注
   - 需要复制 PDF 实例，增加内存开销

2. **Panel 布局保存**
   - 支持保存和恢复 Panel 布局
   - 记录 Panel 位置、大小和打开的文件

3. **协作功能**
   - 基于共享状态实现实时协作
   - 不同用户可以在不同 Panel 中查看同一文件