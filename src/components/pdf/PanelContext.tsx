import React, { createContext, useContext, ReactNode } from 'react';
import { TabItem } from '@/components/pdf/components/draggable-tabs/types';

interface PanelContextType {
  panelId?: string;
  activeAid: string;
  tabItems: TabItem[];
}

const PanelContext = createContext<PanelContextType | null>(null);

export const PanelProvider: React.FC<{
  panelId?: string;
  activeAid: string;
  tabItems: TabItem[];
  children: ReactNode;
}> = ({ panelId, activeAid, tabItems, children }) => {
  return (
    <PanelContext.Provider value={{ panelId, activeAid, tabItems }}>
      {children}
    </PanelContext.Provider>
  );
};

export const usePanelContext = () => {
  const context = useContext(PanelContext);
  if (!context) {
    // 如果没有 Context，返回全局状态
    const { activeAid, tabItems } = usePdfStore.getState();
    return { panelId: undefined, activeAid, tabItems };
  }
  return context;
};

// 导入必要的依赖
import { usePdfStore } from '@/store/pdf-store';