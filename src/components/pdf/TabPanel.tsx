import {DragAndResize} from "@/components/custom-drag-and-resize";
import {PanelPosition, ResizableContainer} from '@/pages/workspace/Panel';
import React, {FC, useEffect} from "react";
import {useTabPanelVisibility} from "@/components/flow/TabPanelControls";
import styled from "styled-components";
import "./style/pdf.css";
import {Header} from "./components/layout/Header";
import {Main} from "./components/layout/Main";
import {PanelProvider} from "./PanelContext";
import {useTabPanelStore} from "@/store/tab-panel-store";
import {usePdfStore} from "@/store/pdf-store";
import {WorkspaceService} from "@/local/services/workspace-service";
import {ResourceEditor} from "@/components/lexicalEditor/App";
import {PdfViewer} from "@/components/pdf/components/highlight/PdfViewer.tsx";

// 懒加载 PdfViewer 组件 - 提取到组件外，避免重复创建
const LazyPdfViewer = React.lazy(() => import('./components/highlight/PdfViewer').then(module => ({
    default: module.PdfViewer
})));

// 使用通用的 ResizableContainer，确保外层容器尺寸与可调整区域一致，避免内容被裁剪
const PdfPanelWrapper = styled(ResizableContainer)`
    background: white;
`;

// TabPanel的内部结构 - 带独立状态的Panel
export const TabPanelContent = ({panelId, panelPosition, setPanelPosition}: {
    panelId: string;
    panelPosition?: PanelPosition;
    setPanelPosition?: React.Dispatch<React.SetStateAction<PanelPosition>>
}) => {
    const {getPanelContent} = useTabPanelStore();
    const {togglePanelVisibility} = useTabPanelVisibility();
    const panel = getPanelContent(panelId);

    // 如果Panel不存在，返回空
    if (!panel) {
        return <div className="flex items-center justify-center h-full text-gray-400">Panel不存在</div>;
    }

    return (
        <PanelProvider
            panelId={panelId}
            activeAid={panel.activeAid}
            tabItems={panel.tabItems}
        >
            <div className="relative flex flex-col h-full">
                {/* 左上角隐藏按钮 */}
                <button
                    onClick={() => {
                        // 若处于贴边态，隐藏时同时退出贴边
                        if (panelPosition?.isSnapped && setPanelPosition) {
                            setPanelPosition(prev => ({
                                ...prev,
                                isSnapped: false,
                                originalSize: undefined,
                            }));
                        }
                        togglePanelVisibility(panelId);
                    }}
                    className="absolute top-3 left-6 z-10 px-2 py-1 text-xs rounded bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300"
                    title="隐藏此面板"
                >
                    隐藏
                </button>
                {/*顶部*/}
                <div>
                    <Header panelId={panelId} hideMenuToggle/>
                </div>
                <div
                    className="flex-1 min-h-0 overflow-hidden">
                    {/* 只有Main，没有Menu */}
                    <Main panelId={panelId}/>
                </div>
            </div>
        </PanelProvider>
    );
}

interface TabPanelProps {
    tabId: string;
    tabLabel: string;
    aid: string;
    zIndex: number;
    panelPosition: PanelPosition;
    setPanelPosition: React.Dispatch<React.SetStateAction<PanelPosition>>;
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>;
    setDragPanel: (isDragging: boolean) => void;
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    };
    handlePanelClick?: () => void;
    onBumpTabPanel?: (panelId: string, dx: number) => void;
    otherPanels?: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string;
    }>;
}

export const TabPanelMain: FC<TabPanelProps> = (props) => {
    const {
        tabId,
        tabLabel,
        aid,
        zIndex,
        panelPosition,
        setPanelPosition,
        handlePanelClick,
        onBumpTabPanel,
        ...rest
    } = props;

    const {addPdfs} = usePdfStore();
    const {
        getPanelContent,
        createPanelContent,
        loadPanelTabs,
        updateTabPanelPosition
    } = useTabPanelStore();

    // 初始化Panel状态（如果不存在）
    useEffect(() => {
        const panel = getPanelContent(tabId);

        // 如果panel存在但children缺失，需要重新创建children
        if (panel && panel.tabItems.some((item: any) => !item.children)) {
            const restoredTabsWithChildren = panel.tabItems.map((tab: any) => {
                if (tab.children) {
                    return tab; // 已有children，保持不变
                }

                // 重新创建children
                const pdfInfo = usePdfStore.getState().pdfs.get(tab.key);
                const labelStr = String(tab.label);
                const nameLower = labelStr.toLowerCase();

                // 改进文件类型判断：优先根据文件名判断，其次根据URL判断
                const isPdfByName = nameLower.endsWith('.pdf');
                const urlLower = String(pdfInfo?.url || '').toLowerCase();
                const isPdfByUrl = urlLower.includes('.pdf');
                const hasUrl = Boolean(pdfInfo?.url);

                // 如果文件名明确是 .pdf 结尾，或者URL包含.pdf，则认为是PDF
                const seemsPdf = isPdfByName || (hasUrl && isPdfByUrl);

                return {
                    ...tab,
                    label: labelStr,
                    children: seemsPdf ? (
                        <PdfViewer aid={tab.key} panelId={tabId}/>
                    ) : (
                        <ResourceEditor id={tab.key} fileId={tab.key} onChange={() => {
                        }}/>
                    )
                };
            });

            // 更新panel内容
            createPanelContent(tabId, restoredTabsWithChildren, panel.activeAid, panel.mode);
            return; // 提前返回，避免执行后续逻辑
        }

        if (!panel) {
            // 先尝试从持久化存储恢复
            const savedTabs = loadPanelTabs(tabId);
            if (savedTabs && savedTabs.tabItems.length > 0) {
                // 异步加载 PDF 数据并恢复面板
                const restorePanel = async () => {
                    const workspaceService = new WorkspaceService();
                    const pdfDataMap = new Map();

                    // 收集需要加载的 PDF 文件 ID
                    const pdfIds = savedTabs.tabItems.map(tab => tab.key);

                    // 批量加载 PDF 数据
                    for (const fileId of pdfIds) {
                        // 检查是否已经在 store 中
                        const {pdfs} = usePdfStore.getState();
                        const existingPdf = pdfs.get(fileId);
                        if (!existingPdf || !existingPdf.url) {
                            try {
                                // 从数据库加载文件数据
                                const url = await workspaceService.getFileContent(fileId);
                                const fileInfo = savedTabs.tabItems.find(tab => tab.key === fileId);
                                if (url && fileInfo) {
                                    pdfDataMap.set(fileId, {
                                        aid: fileId,
                                        filename: fileInfo.label || 'Unknown',
                                        url: url
                                    });
                                }
                            } catch (error) {
                                // 忽略加载错误，文件可能已被删除
                            }
                        }
                    }

                    // 添加 PDF 数据到 store
                    if (pdfDataMap.size > 0) {
                        addPdfs(pdfDataMap);
                    }

                    // 恢复持久化的文件列表
                    const restoredTabs = savedTabs.tabItems.map(tab => {
                        // 使用与menu.tsx相同的文件类型判断逻辑
                        const pdfInfo = usePdfStore.getState().pdfs.get(tab.key);
                        const labelStr = String(tab.label);
                        const nameLower = labelStr.toLowerCase();

                        // 改进文件类型判断：优先根据文件名判断，其次根据URL判断
                        const isPdfByName = nameLower.endsWith('.pdf');
                        const urlLower = String(pdfInfo?.url || '').toLowerCase();
                        const isPdfByUrl = urlLower.includes('.pdf');
                        const hasUrl = Boolean(pdfInfo?.url);

                        // 如果文件名明确是 .pdf 结尾，或者URL包含.pdf，则认为是PDF
                        const seemsPdf = isPdfByName || (hasUrl && isPdfByUrl);

                        return {
                            key: tab.key,
                            label: labelStr, // 确保label是字符串
                            closable: tab.closable !== false,
                            children: seemsPdf ? (
                                <PdfViewer aid={tab.key} panelId={tabId}/>
                            ) : (
                                <ResourceEditor id={tab.key} fileId={tab.key} onChange={() => {
                                }}/>
                            )
                        };
                    });

                    createPanelContent(tabId, restoredTabs, savedTabs.activeAid);
                };

                restorePanel();
            } else {
                // 如果没有持久化数据，使用原有的兜底逻辑
                const pdfInfo = usePdfStore.getState().pdfs.get(aid);
                const labelStr = String(tabLabel);
                const nameLower = labelStr.toLowerCase();

                // 改进文件类型判断：优先根据文件名判断，其次根据URL判断
                const isPdfByName = nameLower.endsWith('.pdf');
                const urlLower = String(pdfInfo?.url || '').toLowerCase();
                const isPdfByUrl = urlLower.includes('.pdf');
                const hasUrl = Boolean(pdfInfo?.url);

                // 如果文件名明确是 .pdf 结尾，或者URL包含.pdf，则认为是PDF
                const seemsPdf = isPdfByName || (hasUrl && isPdfByUrl);

                createPanelContent(tabId, [{
                    key: aid,
                    label: labelStr, // 确保label是字符串
                    closable: true,
                    children: seemsPdf ? (
                        <PdfViewer aid={aid} panelId={tabId}/>
                    ) : (
                        <ResourceEditor id={aid} fileId={aid} onChange={() => {
                        }}/>
                    )
                }], aid);
            }
        }
    }, [tabId, createPanelContent, addPdfs, getPanelContent, loadPanelTabs]); // 只依赖 tabId，避免 aid 或 tabLabel 变化时重新初始化

    // 处理PDF resize - 与PdfPanelMain相同的逻辑
    const handlePdfResizeStart = () => {
        const panel = getPanelContent(tabId);
        if (panel?.activeAid) {
            const pdfHighlighterUtils = usePdfStore.getState().pdfs.get(panel.activeAid)?.pdfHighlighterUtils;
            if (pdfHighlighterUtils) {
                pdfHighlighterUtils.getViewer()?.viewer!.classList.toggle("PdfHighlighter--disable-selection", true);
            }
        }
    }

    const handlePdfResizeStop = (position?: PanelPosition) => {
        const panel = getPanelContent(tabId);
        if (panel?.activeAid) {
            const pdfHighlighterUtils = usePdfStore.getState().pdfs.get(panel.activeAid)?.pdfHighlighterUtils;
            if (pdfHighlighterUtils) {
                pdfHighlighterUtils.getViewer()?.viewer!.classList.toggle("PdfHighlighter--disable-selection", false);
            }
        }
        // 调整大小结束后，更新 store 中的位置信息
        // 使用传递过来的最新位置，如果没有则使用当前的 panelPosition
        updateTabPanelPosition(tabId, position || panelPosition);
    }

    // 处理拖拽结束
    const handleDragStop = (position: PanelPosition) => {
        // 拖拽结束后，更新 store 中的位置信息
        updateTabPanelPosition(tabId, position);
    }

    // 注意：不在这里清理Panel状态，因为隐藏时组件会卸载
    // Panel状态应该只在真正关闭时才清理（通过removeTabPanel）

    return (
        <DragAndResize
            panelPosition={panelPosition}
            setPanelPosition={setPanelPosition}
            className="pdf-panel-resizable"
            panelClassName="pdf-panel-resizable"
            zIndex={zIndex}
            innerElement={<TabPanelContent panelId={tabId} panelPosition={panelPosition}
                                           setPanelPosition={setPanelPosition}/>}
            PanelWrapper={PdfPanelWrapper}
            extraEvent={{
                onResizeStart: handlePdfResizeStart,
                onResizeStop: handlePdfResizeStop,
                onDragStop: handleDragStop
            }}
            handlePanelClick={handlePanelClick}
            onBumpTabPanel={onBumpTabPanel}
            {...rest}
        />
    );
};

export default TabPanelMain;