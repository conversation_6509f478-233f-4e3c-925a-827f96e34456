# 文件唯一性测试指南

## 功能说明
系统现已实现了**同一文件只能在一个 Panel 中打开**的限制，确保数据一致性和避免用户困惑。

## 实现机制

### 1. 文件位置追踪
- `findFileLocation(fileId)` 方法会查找文件在系统中的位置
- 返回值包含：
  - `type`: 'main' | 'panel' | 'window' | null
  - `id`: Panel/窗口的唯一标识

### 2. 打开文件时的检查
当尝试打开文件时，系统会：
1. 检查文件是否已在其他地方打开
2. 如果已打开，自动激活已有的标签，不创建新标签
3. 控制台会输出提示信息

### 3. 拖拽时的处理
- **拖出创建 Panel**：先从源位置移除，再创建新 Panel
- **Panel 间拖拽**：如果目标已有该文件，只激活不添加
- **确保唯一性**：文件移动后只存在于新位置

## 测试步骤

### 测试场景 1：基础唯一性测试
1. 在主 Panel 中打开文件 A
2. 尝试在主 Panel 中再次打开文件 A
3. **预期结果**：激活已有标签，不创建新标签

### 测试场景 2：Panel 间唯一性
1. 在主 Panel 中打开文件 A
2. 将文件 A 拖出创建 TabPanel
3. **预期结果**：
   - 文件 A 从主 Panel 移除
   - 新 TabPanel 中显示文件 A
   - 主 Panel 中不再有文件 A

### 测试场景 3：拖拽到已有文件的 Panel
1. 在 TabPanel 1 中打开文件 A
2. 在主 Panel 中打开文件 B
3. 尝试将文件 A 从其他地方拖到主 Panel
4. **预期结果**：
   - 如果主 Panel 已有文件 A，只激活不添加
   - 控制台显示：`文件已在目标Panel中打开，切换到该标签`

### 测试场景 4：多 TabPanel 测试
1. 创建 TabPanel 1，包含文件 A
2. 创建 TabPanel 2，包含文件 B
3. 尝试将文件 A 拖到 TabPanel 2
4. **预期结果**：
   - 文件 A 从 TabPanel 1 移除
   - 文件 A 添加到 TabPanel 2
   - 任何时候文件 A 只存在于一个位置

### 测试场景 5：文件管理器打开测试
1. TabPanel 1 中已有文件 A
2. 从文件管理器尝试打开文件 A
3. **预期结果**：
   - 激活 TabPanel 1 中的文件 A
   - 不在主 Panel 创建新标签
   - 控制台显示文件位置信息

## 调试信息

### 控制台输出
开发者工具的控制台会输出以下信息：
- `文件 [fileId] 已在 [type]([id]) 中打开`
- `文件已在目标Panel中打开，切换到该标签`
- `Created TabPanel after removing from source`
- `没有可以添加的标签，取消创建 Panel`

### 状态检查
可以在控制台执行以下命令检查状态：
```javascript
// 查看所有 Panel 的文件列表
const state = usePdfStore.getState();
console.log('主 Panel 文件:', state.tabItems?.map(t => t.key));
state.panels.forEach((panel, id) => {
  console.log(`Panel ${id} 文件:`, panel.tabItems.map(t => t.key));
});

// 查找特定文件的位置
const location = usePdfStore.getState().findFileLocation('file-id');
console.log('文件位置:', location);
```

## 已知限制
1. 文件 ID 必须唯一，系统通过 `key` 属性识别文件
2. 同一文件的不同版本会被视为不同文件
3. 关闭 Panel 时，其中的文件不会自动转移到其他 Panel

## 未来改进
- [ ] 添加视觉提示，显示文件已在其他 Panel 打开
- [ ] 支持强制移动文件到新位置的选项
- [ ] 添加 Panel 焦点切换快捷键