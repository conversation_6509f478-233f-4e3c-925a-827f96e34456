import {useCallback} from "react";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {scaledPositionToViewport, Tip} from "@/components/pdf/components/highlight/src";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import {nodeService} from "@/local";
import {NodeEvents} from "@/local/services/node-service";
import {getWsId} from "@/tools/params.ts";
import {useReactFlowInstanceStore} from "@/store/reactflow-instance-store";
import {calculateNodeHeight, NODE_DEFAULTS} from "@/components/flow/constants/node-defaults";
import type {Node} from "@xyflow/react";
import {toast} from "sonner";

// 计算新节点位置的辅助函数
const calculateNodePosition = (nodes: Node[]) => {
    const DEFAULT_WIDTH = NODE_DEFAULTS.WIDTH;
    const DEFAULT_MIN_HEIGHT = NODE_DEFAULTS.MIN_HEIGHT;
    const DEFAULT_MAX_HEIGHT = NODE_DEFAULTS.MAX_HEIGHT;
    
    if (nodes.length === 0) {
        return { x: 0, y: 0 };
    }
    
    // 找到最右边的节点
    const rightmostNode = nodes.reduce((max, node) => {
        return node.position.x > max.position.x ? node : max;
    }, nodes[0]);
    
    // 找到最右边一列的所有节点
    const rightColumn = nodes.filter(
        (node) => (rightmostNode.position.x - node.position.x) < DEFAULT_WIDTH
    );
    
    // 找出该列最下方的节点
    const bottomNode = rightColumn.reduce((max, node) => {
        return node.position.y > max.position.y ? node : max;
    }, rightColumn[0]);
    
    // 计算该列占据的高度
    const columnHeight = bottomNode.position.y + (bottomNode.measured?.height || bottomNode.height || DEFAULT_MIN_HEIGHT);
    
    // 判断是否需要开始新的一列
    const shouldStartNewColumn = window.innerHeight < columnHeight + DEFAULT_MIN_HEIGHT + 100;
    
    return {
        x: shouldStartNewColumn ? rightmostNode.position.x + DEFAULT_WIDTH + 30 : rightmostNode.position.x,
        y: shouldStartNewColumn ? 0 : bottomNode.position.y + (bottomNode.measured?.height || bottomNode.height || DEFAULT_MAX_HEIGHT) + 15,
    };
};

export const useHighlightAdd = () => {
    const {instance} = useReactFlowInstanceStore();
    const {addHighlights} = usePdfStore((state) => ({
        addHighlights: state.addHighlights
    }));
    const wid = getWsId();
    const addHighlight = useCallback(async (highlight: CustomHighlight) => {
        // 调试日志移除
        let content = highlight.content?.text || "";
        // "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHc...";
        if (highlight.type == "area") {
            content = `![pdf截图-${new Date().toLocaleString()}](${highlight.content?.image})`
        }

        // 创建包含完整信息的mark对象
        const completeMarkInfo = {
            ...highlight.position,
            type: highlight.type,
            content: highlight.content
        }
        const state = usePdfStore.getState()
        const highlighterUtils = state.pdfs.get(highlight.aid)?.pdfHighlighterUtils
        try {
            // 新建节点 - 启用自动高度计算
            const currentColor = (highlight.color ?? state.defaultColor)
            
            // 计算新节点位置
            let nodePosition = { x: 0, y: 0 };
            if (instance) {
                const currentNodes = instance.getNodes();
                nodePosition = calculateNodePosition(currentNodes);
            }
            
            // 先调用后端API创建节点和mark
            const response = await nodeService.create({
                wid: wid!,
                content: content,
                aid: highlight.aid,
                color: currentColor.slice(1), // 去掉#号
                mark: JSON.stringify(completeMarkInfo),
                mid: highlight.id,
                x: nodePosition.x,
                y: nodePosition.y,
                pids: [],
                type: 'default',
            });
            const res = (response as any).data || response;  // 处理可能的包装
            
            // 根据高亮类型计算节点尺寸
            let nodeWidth = NODE_DEFAULTS.WIDTH;
            let nodeHeight = calculateNodeHeight(content);
            
            if (highlight.type === "area") {
                // 截图类型：使用实际截图尺寸，保持原始宽高比
                const boundingRect = highlight.position.boundingRect;
                console.log('🖼️ 截图尺寸信息:', {
                    boundingRect,
                    rawWidth: boundingRect?.width,
                    rawHeight: boundingRect?.height,
                    x1: boundingRect?.x1,
                    y1: boundingRect?.y1,
                    x2: boundingRect?.x2,
                    y2: boundingRect?.y2
                });
                
                if (boundingRect) {
                    // 从坐标计算实际宽高
                    const calculatedWidth = Math.abs((boundingRect.x2 || 0) - (boundingRect.x1 || 0));
                    const calculatedHeight = Math.abs((boundingRect.y2 || 0) - (boundingRect.y1 || 0));
                    
                    // 使用计算出的宽高，如果没有计算结果则使用boundingRect的width/height
                    const actualWidth = calculatedWidth > 0 ? calculatedWidth : (boundingRect.width || 0);
                    const actualHeight = calculatedHeight > 0 ? calculatedHeight : (boundingRect.height || 0);
                    
                    console.log('📏 计算结果:', {
                        calculatedWidth,
                        calculatedHeight,
                        actualWidth,
                        actualHeight
                    });
                    
                    if (actualWidth > 0 && actualHeight > 0) {
                        // 智能缩放：根据宽高比和目标尺寸计算合适的缩放比例
                        const aspectRatio = actualWidth / actualHeight;
                        const targetMaxWidth = 600; // 目标最大宽度
                        const targetMaxHeight = 400; // 目标最大高度
                        const minWidth = 250; // 最小宽度
                        const minHeight = 150; // 最小高度
                        
                        let finalWidth, finalHeight;
                        
                        if (aspectRatio > 1) {
                            // 横长截图：限制宽度，按比例计算高度
                            finalWidth = Math.min(targetMaxWidth, Math.max(minWidth, actualWidth * 2));
                            finalHeight = Math.max(minHeight, finalWidth / aspectRatio);
                        } else {
                            // 竖长截图：限制高度，按比例计算宽度
                            finalHeight = Math.min(targetMaxHeight, Math.max(minHeight, actualHeight * 2));
                            finalWidth = Math.max(minWidth, finalHeight * aspectRatio);
                        }
                        
                        nodeWidth = Math.round(finalWidth) as any;
                        nodeHeight = Math.round(finalHeight) as any;
                        
                        console.log('🎯 智能缩放结果:', { 
                            actualWidth, 
                            actualHeight, 
                            aspectRatio: aspectRatio.toFixed(2), 
                            nodeWidth, 
                            nodeHeight,
                            type: aspectRatio > 1 ? '横长' : '竖长'
                        });
                    } else {
                        console.warn('⚠️ 截图尺寸计算结果无效，使用默认尺寸');
                    }
                } else {
                    console.warn('⚠️ boundingRect 不存在，使用默认尺寸');
                }
            }
            
            // 使用后端返回的数据创建前端节点
            const newNode = {
                id: res.nid,
                type: 'markdown' as const,  // 使用Canvas中定义的节点类型
                position: nodePosition,
                width: nodeWidth as any,
                height: nodeHeight as any,
                measured: {
                    width: nodeWidth,
                    height: nodeHeight,
                },
                data: {
                    content: content,
                    color: currentColor,
                    title: "",
                    // 额外的数据存储在data中
                    aid: highlight.aid,
                    mark: JSON.stringify(completeMarkInfo),
                    mid: res.mid,
                }
            };
            
            // 使用事件系统通知Canvas添加节点
            NodeEvents.emit('nodeAdded', newNode);
            
            // 如果有ReactFlow实例，将视图居中到新节点并选中它
            if (instance) {
                const zoom = instance.getViewport().zoom;
                instance.setCenter(newNode.position.x, newNode.position.y, { zoom });
                
                // 选中新创建的节点
                setTimeout(() => {
                    const allNodes = instance.getNodes();
                    const updatedNodes = allNodes.map(node => ({
                        ...node,
                        selected: node.id === newNode.id,
                        selectedFromHighlight: node.id === newNode.id ? true : false
                    }));
                    instance.setNodes(updatedNodes);
                }, 0); // 稍微延迟以确保节点已添加到实例中
            }
           
            // 自定义高亮
            const customHighlight = {
                ...highlight,
                color: highlight.color ?? state.defaultColor,
                id: res.mid, // 使用后端返回的mid
                aid: highlight.aid,
                nid: res.nid, // 使用后端返回的nid
            }
            // 添加高亮
            addHighlights(highlight.aid, [customHighlight])
            highlighterUtils?.removeGhostHighlight()
            // 工具栏
            const highlightTip: Tip = {
                position: scaledPositionToViewport(highlight.position, highlighterUtils?.getViewer()!),
                content: <HighlightToolbar customHighlight={customHighlight}/>,
            }
            highlighterUtils?.setTip(highlightTip)
        } catch (e) {
            // console.error('创建高亮节点失败:', e);
            // 可以添加用户提示
            toast.error('创建高亮失败，请重试');
        }
    }, [instance, addHighlights, wid])
    return {
        addHighlight
    }
}