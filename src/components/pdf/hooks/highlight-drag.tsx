import {CustomHighlight, getNodeHighlight, modeType, usePdfStore} from "@/store/pdf-store.ts";
import {useCallback} from "react";
import {updateHighlight} from "@/api/pdf";
import {getWsId} from "@/tools/params.ts";
import {scaledPositionToViewport, Tip} from "@/components/pdf/components/highlight/src";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import { useReactFlow } from "@xyflow/react";
import { updateNode } from "@/api/canvas";
import { useReactFlowInstanceStore } from "@/store/reactflow-instance-store";
// import {useFlowStore} from "@/store/flow-store.ts";

export const useHighlightDrag = () => {
    const {updateHighlights} = usePdfStore((state) => ({
        updateHighlights: state.updateHighlights
    }))
    // const {updateNodes, setCenter} = useFlowStore((state) => ({
    //     updateNodes: state.updateNodes,
    //     setCenter: state.setCenter
    // }))
    const {instance} = useReactFlowInstanceStore()
    const {updateNode, setCenter} = instance!;
    const onDragStop = useCallback(async (highlight: CustomHighlight) => {
        const pdfState = usePdfStore.getState()
        const highlighterUtils = pdfState.pdfs.get(highlight.aid)?.pdfHighlighterUtils!
        const wid = getWsId()
        const isTextInsert = highlight.type === 'text-insert';
        // 为 text-insert 提供与截图一致（mode=2风格）的工具栏：改色、删除、隐藏新增
        const onColorChange = async (color: string) => {
            try {
                const wid = getWsId();
                // 本地更新
                usePdfStore.getState().updateHighlights(highlight.aid, { id: highlight.id, color });
                // 同步后端
                const completeMarkInfo = {
                    ...highlight.position,
                    type: highlight.type,
                    content: highlight.content,
                };
                await updateHighlight({
                    wid: wid!,
                    mid: highlight.id,
                    mark: JSON.stringify(completeMarkInfo),
                    color: color.slice(1),
                });
            } catch (e) {
                // ignore
            }
        };
        const onDelete = () => {
            usePdfStore.getState().removeHighlights(highlight.aid, highlight.id);
            highlighterUtils.setTip(null as any);
        };
        const highlightTip: Tip = {
            position: scaledPositionToViewport(highlight.position, highlighterUtils.getViewer()!),
            content: (
                <HighlightToolbar
                    customHighlight={highlight}
                    onColorChange={isTextInsert ? onColorChange : undefined}
                    hideAddButton={isTextInsert}
                    onDelete={isTextInsert ? onDelete : undefined}
                />
            ),
        }
        highlighterUtils.setTip(highlightTip)
        // 节点
        // TODO: 处理报错
        // updateNodes([{id: highlight.nid, selected: true, selectedFromHighlight: true}], true)
        updateNode(highlight.id,{selected: true})
        // setCenter(highlight.nid)
        // 不更新
        if (JSON.stringify(highlight.position.boundingRect) === JSON.stringify(getNodeHighlight([highlight.nid])[0].position.boundingRect)) {
            return
        }
        try {
            // 创建包含完整信息的mark对象
            const completeMarkInfo = {
                ...highlight.position,
                type: highlight.type,
                content: highlight.content
            }
            // 更新高亮
            await updateHighlight({
                wid: wid,
                mid: highlight.id,
                mark: JSON.stringify(completeMarkInfo),
                color: highlight?.color?.slice(1) || ''
            })
        } catch (e) {
            console.log(e)
            return
        }
        // 高亮
        updateHighlights(highlight.aid, {
            id: highlight.id,
            position: highlight.position,
            content: highlight.content
        })

        // 结束编辑状态
        highlighterUtils.toggleEditInProgress(false)
    }, [updateHighlight, updateHighlights, updateNode, setCenter])
    return {
        onDragStop
    }
}