import {useCallback} from "react";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {Tip, ViewportHighlight, viewportPositionToScaled} from "@/components/pdf/components/highlight/src";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import { useReactFlowInstanceStore } from "@/store/reactflow-instance-store";
import { updateHighlight } from "@/api/pdf";
import { getWsId } from "@/tools/params";
// import { useReactFlow } from "@xyflow/react";
// import {useFlowStore} from "@/store/flow-store.ts";

export const useHighlightClick = () => {
    // const {updateNodes, setCenter} = useFlowStore((state) => ({
    //     updateNodes: state.updateNodes,
    //     setCenter: state.setCenter
    // }))
    // const {updateNode,setCenter} = useReactFlow()
    const {instance} = useReactFlowInstanceStore()
    const {updateNode, setCenter} = instance!;
    
    // 文本高亮点击
    const handleTextClick = useCallback((
        highlight: ViewportHighlight<CustomHighlight>,
    ) => {
        const pdfState = usePdfStore.getState()
        const highlighterUtils = pdfState.pdfs.get(highlight.aid)?.pdfHighlighterUtils!
        // 自定义高亮
        const customHighlight = {
            ...highlight,
            position: viewportPositionToScaled(highlight.position, highlighterUtils.getViewer()!)
        }
        // 针对 text-insert：显示与截图一致（mode=2风格）的toolbar：改色、删除，不出现“新增节点”
        const isTextInsert = customHighlight.type === 'text-insert';
        const onColorChange = async (color: string) => {
            try {
                const wid = getWsId();
                // 先更新本地 store
                usePdfStore.getState().updateHighlights(customHighlight.aid, {
                    id: customHighlight.id,
                    color,
                });
                // 同步到后端（带上完整 mark）
                const completeMarkInfo = {
                    ...customHighlight.position,
                    type: customHighlight.type,
                    content: customHighlight.content,
                };
                await updateHighlight({
                    wid: wid!,
                    mid: customHighlight.id,
                    mark: JSON.stringify(completeMarkInfo),
                    color: color.slice(1),
                });
            } catch (e) {
                // ignore
            }
        };
        const onDelete = () => {
            // 仅删除高亮（不涉及节点）
            usePdfStore.getState().removeHighlights(customHighlight.aid, customHighlight.id);
            // 关闭工具栏
            highlighterUtils.setTip(null as any);
        };
        // 工具栏
        const highlightTip: Tip = {
            position: highlight.position,
            content: (
                <HighlightToolbar
                    customHighlight={customHighlight}
                    onColorChange={isTextInsert ? onColorChange : undefined}
                    hideAddButton={isTextInsert}
                    onDelete={isTextInsert ? onDelete : undefined}
                />
            ),
        };
        // 展示工具栏
        highlighterUtils.setTip(highlightTip);
        
        // 先取消所有节点的选中状态，然后选中当前高亮对应的节点
        const allNodes = instance?.getNodes() || [];
        const updatedNodes = allNodes.map(node => ({
            ...node,
            selected: node.id === highlight.nid,
            selectedFromHighlight: node.id === highlight.nid ? true : false
        }));
        
        // 批量更新节点状态
        instance?.setNodes(updatedNodes);
        
        const node = instance?.getNode(highlight.nid)
        const zoom = instance?.getViewport().zoom;
        setCenter(node?.position.x!,node?.position.y!,{
            zoom: zoom,
            duration: 500
        })
    }, [instance])
    return {
        handleTextClick
    }
}