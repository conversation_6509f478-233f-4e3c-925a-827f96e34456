import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {useTabPanelStore} from "@/store/tab-panel-store";
import {GhostHighlight, scaledPositionToViewport, Tip} from "@/components/pdf/components/highlight/src";
import {useCallback} from "react";
import {HighlightToolbar} from "@/components/pdf/components/highlight/HighlightToolbar.tsx";
import {useHighlightAdd} from "@/components/pdf/hooks/highlight-add.tsx";

export const usePdfSelection = (panelId?: string) => {
    const {addHighlight} = useHighlightAdd()
    const { getPanelContent } = useTabPanelStore();
    const onSelection = useCallback((aid: string, highlight: GhostHighlight) => {
        if (highlight.type === "text" && highlight.content.text?.trim().length === 0) {
            return
        }
        const state = usePdfStore.getState()
        const customHighlight: CustomHighlight = {
            id: crypto.randomUUID(),
            aid: aid,
            type: highlight.type,
            content: highlight.content,
            position: highlight.position,
            color: state.defaultColor,
            nid: "" // 不关联节点
        }
        const highlighterUtils = state.pdfs.get(aid)?.pdfHighlighterUtils!
        
        // 根据panelId获取对应的mode，如果没有panelId或Panel不存在，使用全局mode
        const panel = panelId ? getPanelContent(panelId) : null;
        const mode = panel?.mode ?? state.mode;
        
        
        // 根据模式分支处理
        if (mode === 1) {
            // 工具栏（只有鼠标模式显示工具栏）
            const highlightTip: Tip = {
                position: scaledPositionToViewport(highlight.position, highlighterUtils.getViewer()!),
                content: <HighlightToolbar customHighlight={customHighlight}/>,
            }
            highlighterUtils.setTip(highlightTip)
            return
        }
        // 笔记模式和截图模式都直接添加高亮
        addHighlight(customHighlight)
    }, [addHighlight, panelId, getPanelContent])
    return {
        onSelection
    }
}