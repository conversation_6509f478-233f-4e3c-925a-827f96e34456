import { useState, useRef, useCallback, useEffect } from 'react';

interface UseResizeOptions {
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
}

export function useResize({
  initialWidth = 250,
  minWidth = 150,
  maxWidth = 500
}: UseResizeOptions = {}) {
  const [width, setWidth] = useState(initialWidth);
  const [isDragging, setIsDragging] = useState(false);
  const startResizeXRef = useRef(0);
  const initialWidthRef = useRef(0);
  
  // 拖拽开始处理
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    startResizeXRef.current = e.clientX;
    initialWidthRef.current = width;
    setIsDragging(true);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [width]);

  // 拖拽过程处理
  const handleMouseMove = useCallback((e: MouseEvent) => {
    const deltaX = e.clientX - startResizeXRef.current;
    let newWidth = initialWidthRef.current + deltaX;
    newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
    setWidth(newWidth);
  }, [minWidth, maxWidth]);

  // 拖拽结束处理
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove, setIsDragging]);

  // 组件卸载时清理事件监听器
  useEffect(() => {
    return () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  return {
    width,
    handleMouseDown,
    isDragging
  };
} 