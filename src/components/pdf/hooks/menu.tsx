import {useCallback, useEffect} from "react";
import {usePdfStore} from "@/store/pdf-store.ts";
import {deletePdf} from "@/api/pdf";
import {PdfViewer} from "@/components/pdf/components/highlight/PdfViewer.tsx";
import {getWsId} from "@/tools/params.ts";
import {ResourceEditor} from "@/components/lexicalEditor/App";
import {useTabPanelVisibility} from "@/components/flow/TabPanelControls";
import {usePanelOpenStore} from "@/store/panel-open-store";
import {useTabPanelStore} from "@/store/tab-panel-store";

export const useMenu = () => {
    const {hiddenPanels, togglePanelVisibility} = useTabPanelVisibility();
    const {togglePdfPanel} = usePanelOpenStore();
    const {updateTabPanelPosition, tabPanels, setPanelActiveAid} = useTabPanelStore();
    const {setActiveAid, setTabItems, removePdfs, addPdfs} = usePdfStore((state) => ({
        setActiveAid: state.setActiveAid,
        setTabItems: state.setTabItems,
        removePdfs: state.removePdfs,
        addPdfs: state.addPdfs
    }))
    const onMenuClick = useCallback((file: any) => {

        const state = usePdfStore.getState()
        const aid = file?.id
        // 唯一性检查：如果该文件已在任一容器打开，则激活并返回（不在主 Panel 重复添加）
        const loc = state.findFileLocation?.(aid)
        // 如果该文件同时存在于浮窗中，则优先激活浮窗（避免 TabPanel 被隐藏时看不到）
        const findWindowId = () => {
            for (const [wid, win] of state.windows) {
                if (win.tabs.some((t) => t.key === aid)) return wid
            }
            return null
        }
        if (loc && loc.type) {
            if (loc.type === 'panel') {
                const winId = findWindowId()
                if (winId) {
                    state.updateWindow(winId, {activeTabKey: aid, zIndex: Math.floor(Date.now() / 1000)})
                    return
                }
                const isHidden = hiddenPanels?.has(loc.id as string)
                if (isHidden) {
                    togglePanelVisibility(loc.id as string)
                }
                const panelInfo = tabPanels.get(loc.id as string)
                if (panelInfo) {
                    updateTabPanelPosition(loc.id as string, {...panelInfo.position, zIndex: Date.now()})
                }
            }
            switch (loc.type) {
                case 'main':
                    // 若主 Panel 被关闭，先打开
                    togglePdfPanel(true)
                    state.setActiveAid(aid)
                    break
                case 'panel':
                    setPanelActiveAid(loc.id as string, aid)
                    break
                case 'window':
                    state.updateWindow(loc.id as string, {activeTabKey: aid, zIndex: Math.floor(Date.now() / 1000)})
                    break
            }
            return
        }
        if (state.activeAid === aid) {
            return
        }
        // 默认打开到主 Panel：若主 Panel 关闭则先打开
        togglePdfPanel(true)
        setActiveAid(aid)
        if (state.tabItems?.find((item) => item.key === aid)) {
            return
        }
        // 根据是否有可用 url 判定是否为 PDF，若无 url 则以文本（notebook/lexical）方式打开
        const pdfInfo = state.pdfs.get(aid)
        const nameLower = String(file?.file_name || '').toLowerCase()
        const urlLower = String(pdfInfo?.url || '').toLowerCase()
        const hasUrl = Boolean(pdfInfo?.url)
        const seemsPdf = hasUrl && (nameLower.endsWith('.pdf') || urlLower.endsWith('.pdf'))

        const items = [...(state.tabItems || []), {
            key: aid,
            label: file?.file_name,
            children: seemsPdf
                ? <PdfViewer aid={aid} panelId={undefined}/>
                : <ResourceEditor id={aid} fileId={aid} onChange={() => {
                }}/>
        }]
        setTabItems(items)
    }, [setActiveAid, setTabItems, hiddenPanels, togglePanelVisibility, tabPanels, updateTabPanelPosition, togglePdfPanel])
    const onMenuDelete = useCallback(async (aid: string, deleteNode: boolean) => {
        try {
            await deletePdf({
                aid,
                type: deleteNode ? 2 : 1
            })
            const state = usePdfStore.getState()
            const tabItems = state.tabItems || []
            const newTabItems = tabItems.filter((item) => item.key !== aid)
            // 删除附件
            removePdfs(aid)
            setTabItems(newTabItems)
            if (state.activeAid === aid) {
                const index = tabItems.findIndex((item) => item.key === aid)
                if (newTabItems.length > 0) {
                    setActiveAid(newTabItems[index === tabItems.length - 1 ? index - 1 : index].key)
                } else {
                    setActiveAid("")
                }
            }
        } catch (e) {
            console.log("删除附件失败", e)
        }
    }, [deletePdf, removePdfs, setTabItems, setActiveAid])
    // 处理消息
    const handleMessage = useCallback(async (event: MessageEvent) => {
        try {
            let data = {}
            const wsId = getWsId()
            if (!event.data.data || !event.data.data.wid) {
                return
            }
            if (wsId !== event.data.data.wid) {
                return
            }
            switch (event.data.type) {
                // 更新菜单状态
                case 'UPDATE_PDFS_STORE_REQUEST':
                    addPdfs(event.data.data.attach.reduce((map: any, item: any) => {
                        map.set(item.aid, {
                            aid: item.aid,
                            filename: item.fname,
                            url: item.url,
                        })
                        return map
                    }, new Map()))
                    break
                default:
                    return
            }
            console.log(event.data.type, event, data)
            window.postMessage({
                type: event.data.type.replace('_REQUEST', '_RESPONSE'),
                messageId: event.data.messageId,
                code: 200,
                data: data
            }, '*')
        } catch (e) {
            console.log(event.data.type, event, e)
            window.postMessage({
                type: event.data.type.replace('_REQUEST', '_RESPONSE'),
                messageId: event.data.messageId,
                code: 500,
                message: e
            }, '*')
        }
    }, [])
    useEffect(() => {
        window.addEventListener('message', handleMessage)
        return () => {
            window.removeEventListener('message', handleMessage)
        }
    }, []);

    const onResourceClick = useCallback(async (file: any) => {
        const info = file?.data
        const aid = file?.data?.id
        console.log("file", file)
        if (info?.mime_type?.endsWith('pdf')) {
            onMenuClick(info)
        }

        if (info?.mime_type?.endsWith('notebook')) {
            const state = usePdfStore.getState()
            // 唯一性检查：若已在任一容器打开，则直接激活并返回
            const loc = state.findFileLocation?.(aid)
            const findWindowId = () => {
                for (const [wid, win] of state.windows) {
                    if (win.tabs.some((t) => t.key === aid)) return wid
                }
                return null
            }

            if (loc && loc.type) {
                if (loc.type === 'panel') {
                    const winId = findWindowId()
                    if (winId) {
                        state.updateWindow(winId, {activeTabKey: aid, zIndex: Math.floor(Date.now() / 1000)})
                        return
                    }
                    const isHidden = hiddenPanels?.has(loc.id as string)
                    if (isHidden) {
                        togglePanelVisibility(loc.id as string)
                    }
                    const panelInfo = tabPanels.get(loc.id as string)
                    if (panelInfo) {
                        updateTabPanelPosition(loc.id as string, {...panelInfo.position, zIndex: Date.now()})
                    }
                }
                switch (loc.type) {
                    case 'main':
                        state.setActiveAid(aid)
                        break
                    case 'panel':
                        setPanelActiveAid(loc.id as string, aid)
                        break
                    case 'window':
                        state.updateWindow(loc.id as string, {activeTabKey: aid, zIndex: Math.floor(Date.now() / 1000)})
                        break
                }
                return
            }
            if (state.activeAid === aid) {
                return
            }
            setActiveAid(aid)
            if (state.tabItems?.find((item) => item.key === aid)) {
                return
            }

            // 预览
            // 获取
            const items = [...(state.tabItems || []), {
                key: aid,
                label: info?.file_name,
                children: <ResourceEditor
                    id={aid}
                    heightLightToNode={true}
                    fileId={aid}
                    onChange={(v: string) => {
                        // onChange
                    }}/>
            }]
            setTabItems(items)
        }
    }, [setActiveAid, setTabItems, onMenuClick, hiddenPanels, togglePanelVisibility, tabPanels, updateTabPanelPosition])

    return {
        onMenuClick,
        onMenuDelete,
        onResourceClick,
    }
}