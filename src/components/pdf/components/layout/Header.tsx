import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { CloseOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import { usePdfStore } from "@/store/pdf-store.ts";
import "../../style/pdf.css";
import { useTabPanelStore } from "@/store/tab-panel-store";
import { usePanelOpenStore } from "@/store/panel-open-store.tsx";
import { CutIcon, HightIcon, MouseIcon, OperateButton, TextIcon } from "../icons";

export const Header = ({ panelId, hideMenuToggle }: { panelId?: string; hideMenuToggle?: boolean }) => {
    const { mode: globalMode, showMenu: globalShowMenu, setMode: globalSetMode, setShowMenu } = usePdfStore((state) => ({
        mode: state.mode,
        showMenu: state.showMenu,
        setMode: state.setMode,
        setShowMenu: state.setShowMenu,
    }));
    const { getPanelContent, setPanelMode, removeTabPanel } = useTabPanelStore();
    const { togglePdfPanel } = usePanelOpenStore((state) => ({
        togglePdfPanel: state.togglePdfPanel
    }))

    // 如果有panelId，使用Panel的独立状态；否则使用全局状态
    const panel = panelId ? getPanelContent(panelId) : null;
    const mode = panel?.mode || globalMode; // 优先使用Panel独立模式，否则使用全局模式
    const showMenu = panel ? (panel.showMenu ?? false) : globalShowMenu; // TabPanel使用独立菜单状态

    // 设置模式的函数：根据是否有panelId决定更新哪个状态
    const setMode = (newMode: number) => {
        if (panelId && panel) {
            setPanelMode(panelId, newMode); // 更新Panel独立模式
        } else {
            globalSetMode(newMode); // 更新全局模式
        }
    }

    return (
        <Flex
            justify="space-between"
            align="center"
            className="relative w-full h-12 px-8 drag-handle cursor-move bg-[#E0E2F4]">
            {!hideMenuToggle && (
                <div 
                    className="relative z-10"
                    onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setShowMenu(!showMenu);
                    }}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                    }}>
                    <Tooltip title={showMenu ? "折叠目录" : "展示目录"} className="cursor-pointer">
                        {showMenu ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
                    </Tooltip>
                </div>
            )}
            <Flex
                justify="space-between"
                align="center"
                className="absolute left-1/2 -translate-x-1/2 w-52 h-10 px-6 rounded-[10px] bg-[#CDD2E9] opacity-[0.54] z-10"
                onMouseDown={(e) => e.stopPropagation()}>
                <Tooltip title="鼠标模式" placement="bottom">
                    <OperateButton
                        variant={mode === 1 ? "default" : "secondary"}
                        onClick={() => setMode(1)}>
                        <MouseIcon />
                    </OperateButton>
                </Tooltip>
                <Tooltip title="笔记模式" placement="bottom" className="cursor-pointer ">
                    <OperateButton
                        variant={mode === 2 ? "default" : "secondary"}
                        onClick={() => setMode(2)}>
                        <HightIcon />
                    </OperateButton>
                </Tooltip>
                <Tooltip title="添加文字" placement="bottom">
                    <OperateButton
                        variant={mode === 3 ? "default" : "secondary"}
                        onClick={() => setMode(3)}>
                        <TextIcon />
                    </OperateButton>
                </Tooltip>
                <Tooltip title="截图模式" placement="bottom">
                    <OperateButton
                        variant={mode === 4 ? "default" : "secondary"}
                        onClick={() => setMode(4)}
                    >
                        <CutIcon />
                    </OperateButton>
                </Tooltip>
            </Flex>
            <CloseOutlined 
                className="cursor-pointer ml-auto relative z-10" 
                onMouseDown={(e) => e.stopPropagation()}
                onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                if (panelId) {
                    // 关闭TabPanel
                    removeTabPanel(panelId);
                } else {
                    // 关闭主PdfPanel
                    togglePdfPanel(false);
                }
                }} />
        </Flex>
    )
};
