import React from "react";
import {usePdfStore} from "@/store/pdf-store.ts";
import {useTabPanelStore} from "@/store/tab-panel-store";
import {DraggableTabSystem} from "@/components/pdf/components/draggable-tabs/DraggableTabSystem.tsx";

export const Main = ({panelId}: { panelId?: string }) => {
    const {tabItems: globalTabItems, activeAid: globalActiveAid} = usePdfStore((state) => ({
        tabItems: state.tabItems,
        activeAid: state.activeAid
    }));
    const {getPanelContent} = useTabPanelStore();

    // 如果有panelId，使用Panel的独立状态；否则使用全局状态
    const panel = panelId ? getPanelContent(panelId) : null;
    const tabItems = panel ? panel.tabItems : globalTabItems;
    const activeAid = panel ? panel.activeAid : globalActiveAid;

    // 获取当前活动标签的内容
    const activeTabContent = tabItems?.find(item => item?.key === activeAid)?.children;

    return (
        <PdfMainLayout>
            <div className="h-full flex flex-col flex-1 min-w-0 pdf-main">
                <DraggableTabSystem panelId={panelId}>
                    {activeTabContent}
                </DraggableTabSystem>
            </div>
        </PdfMainLayout>

    )
}

const PdfMainLayout = (props: { children: React.ReactNode }) => {
    return (
        <div
            className="h-full w-full flex flex-col overflow-hidden"
        >
            <div className="h-full w-full border-box overflow-hidden">
                {props.children}
            </div>
        </div>
    )
}