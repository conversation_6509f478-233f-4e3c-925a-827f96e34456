import { IconFontPdf } from "@/common/constant.ts";
import { usePdfUpload } from "@/components/pdf/hooks/pdf-upload.ts";
import { usePdfStore } from "@/store/pdf-store.ts";
import { EditOutlined, FolderAddOutlined, SearchOutlined } from "@ant-design/icons";
import { useCallback, useRef, useState } from "react";

// 导入自定义 hooks
// import { useFileManager } from "@/components/pdf/hooks/use-file-manager";
import { useResize } from "@/components/pdf/hooks/use-resize";
// import { AttachmentTree } from "@/components/file-tree/files";
import { ArbTree } from "@/components/file-tree/files/arbtree";
import { FilesContext, useGetAllFiles } from "@/components/file-tree/hooks/file.manages.hook";
import { Input } from "antd";
export const Menu = () => {
    // const pdfs = usePdfStore(selectPdfBasicInfo, comparePdfBasicInfo);
    const { activeAid, showMenu, setActiveAid } = usePdfStore((state) => ({
        activeAid: state.activeAid,
        showMenu: state.showMenu,
        setActiveAid: state.setActiveAid,
    }));

    const treeRef = useRef<any>(null)
    // 搜索状态
    const [searchString, setSearchString] = useState('');
    const [filteredFiles, setFilteredFiles] = useState<any>(null);
    const [searchExpandedItems, setSearchExpandedItems] = useState<string[]>([]);
    const [searchHighlightMap, setSearchHighlightMap] = useState<Record<string, string>>({});


    const {
        updateOrAdd,
        allFiles,
        setFiles,
        getAllFiles,
        updateOnMove,
        deleteNode,
    } = useGetAllFiles()

    // 使用拖拽调整宽度的 hook
    const { width: menuWidth, handleMouseDown: handleMouseDownResize, isDragging } = useResize();

    // 上传相关
    const { uploadFiles } = usePdfUpload();
    const inputRef = useRef<HTMLInputElement>(null);

    const handleUpload = useCallback(() => {
        if (inputRef.current) {
            inputRef.current.click();
        }
    }, []);

    // 搜索处理函数
    const handleSearch = useCallback((value: string) => {
        setSearchString(value);
        
        if (!value.trim()) {
            setFilteredFiles(null);
            setSearchExpandedItems([]);
            setSearchHighlightMap({});
            return;
        }

        const searchTerm = value.toLowerCase();
        const expandedItems: string[] = [];
        const highlightMap: Record<string, string> = {};
        
        // 递归搜索函数
        const searchInFiles = (files: any[], parentMatched: boolean = false): any[] => {
            return files.map(file => {
                const nameMatches = file.name.toLowerCase().includes(searchTerm);
                let childrenResults: any[] = [];
                let hasMatchingChildren = false;
                
                if (file.children && file.children.length > 0) {
                    childrenResults = searchInFiles(file.children, nameMatches);
                    hasMatchingChildren = childrenResults.some(child => 
                        child.name.toLowerCase().includes(searchTerm) || 
                        (child.children && child.children.some((c: any) => c))
                    );
                }
                
                // 如果文件夹内的文件匹配，展开该文件夹
                if (hasMatchingChildren && file.type === 'folder') {
                    expandedItems.push(file.id);
                }
                
                // 如果当前项匹配或有匹配的子项，记录高亮
                if (nameMatches || hasMatchingChildren) {
                    if (nameMatches) {
                        highlightMap[file.id] = file.name;
                    }
                    return {
                        ...file,
                        children: childrenResults
                    };
                }
                
                // 如果父级匹配，显示所有子项
                if (parentMatched) {
                    return file;
                }
                
                return null;
            }).filter(Boolean);
        };
        
        const results = searchInFiles(allFiles);
        setFilteredFiles(results);
        setSearchExpandedItems(expandedItems);
        setSearchHighlightMap(highlightMap);
    }, [allFiles]);

    return (
        <FilesContext.Provider value={{
            updateOrAdd,
            data: allFiles,
            setData: setFiles,
            getAllFiles,
            updateOnMove,
            deleteNodeFromHook: deleteNode,
        }}>
            <div className="bg-[#E0E2F4] h-[100%] relative "
                style={{
                    width: `${menuWidth}px`,
                    display: showMenu ? "block" : "none",
                }}
                onClick={(e) => {
                    e?.stopPropagation()
                }}
            >
                <div className="h-full flex flex-col">
                    <div className="w-full p-3 flex justify-between items-center border-b border-gray-300">
                        <span className="font-bold">PDF文件管理器</span>
                    </div>

                    {/* 搜索框 */}
                    <div className="px-3 py-2 border-b border-gray-200">
                        <div className="relative">
                            <Input
                                placeholder="搜索文件..."
                                value={searchString}
                                onChange={(e) => handleSearch(e.target.value)}
                                prefix={<SearchOutlined className="text-gray-400" />}
                                allowClear
                                onClear={() => handleSearch('')}
                            />
                        </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="px-3 py-2 flex justify-end border-b border-gray-200">
                        <div className="flex">
                            <button
                                className={`p-1 rounded-md transition-colors ${
                                    searchString 
                                        ? 'cursor-not-allowed opacity-50' 
                                        : 'hover:bg-white hover:bg-opacity-50'
                                }`}
                                onClick={async (e) => {
                                    if (!searchString) {
                                        treeRef.current?.createFolder()
                                    }
                                    e?.stopPropagation()
                                }}
                                title={searchString ? "搜索模式下不可用" : "新建文件夹"}
                                disabled={!!searchString}
                            >
                                <FolderAddOutlined className={searchString ? "text-gray-400" : "text-[#3D56BA]"} />
                            </button>
                            <button
                                className={`p-1 ml-1 rounded-md transition-colors ${
                                    searchString 
                                        ? 'cursor-not-allowed opacity-50' 
                                        : 'hover:bg-white hover:bg-opacity-50'
                                }`}
                                onClick={async (e) => {
                                    if (!searchString) {
                                        treeRef.current?.createFile()
                                    }
                                    e?.stopPropagation()
                                }}
                                title={searchString ? "搜索模式下不可用" : "新建记事本"}
                                disabled={!!searchString}
                            >
                                <EditOutlined className={searchString ? "text-gray-400" : "text-[#3D56BA]"} />
                            </button>
                        </div>
                    </div>

                    <div className="flex-1 overflow-auto overscroll-y-none hide-scrollbar">
                        {/* 搜索结果提示 */}
                        {searchString && (
                            <div className="px-3 py-2 text-sm text-gray-600 bg-blue-50 border-b border-blue-200">
                                {filteredFiles && filteredFiles.length > 0 ?
                                    `找到 ${Object.keys(searchHighlightMap).length} 个匹配项` :
                                    '未找到匹配的文件'
                                }
                            </div>
                        )}
                        <ArbTree 
                            ref={treeRef} 
                            searchString={searchString}
                            filteredData={filteredFiles}
                            expandedItems={searchExpandedItems}
                            highlightMap={searchHighlightMap}
                        />
                    </div>

                    <div className="border-t border-gray-300 p-3">
                        <div className={`w-full flex px-3 py-2 gap-2 items-center rounded transition-colors ${
                            searchString 
                                ? 'cursor-not-allowed opacity-50 text-gray-400' 
                                : 'cursor-pointer text-[#3D56BA] hover:bg-white hover:bg-opacity-50'
                        }`}
                            onClick={searchString ? undefined : handleUpload}
                            title={searchString ? "搜索模式下不可用" : "上传PDF文件"}>
                            <IconFontPdf type="icon-plus" className={searchString ? "text-gray-400" : ""} />
                            {/* <span>上传PDF文件 {targetFolderId !== 'root' && `到 "${selectedFolderName()}"`}</span> */}
                            <span>上传PDF文件 </span>
                        </div>
                        <input
                            ref={inputRef}
                            type="file"
                            className="hidden"
                            accept="application/pdf"
                            onChange={async (e) => {
                                if (e.target.files && e.target.files.length > 0) {
                                    try {

                                        const pid = treeRef.current?.getCurrentSelectId()

                                        // 使用增量更新方式上传文件
                                        const result = await uploadFiles(e.target.files, pid, () => { }, 'resource');

                                        if (result.success) {
                                            // 上传成功后，由于使用了增量更新，不需要强制刷新
                                            // 文件树会自动重新渲染
                                            console.log('文件上传成功，文件树将自动更新');
                                            getAllFiles()
                                        } else {
                                            alert(`上传失败: ${result.error}`);
                                        }
                                    } catch (error) {
                                        console.error('上传失败:', error);
                                        alert('上传失败，请重试');
                                    } finally {
                                        // setDataLoading(false);
                                        e.target.value = '';
                                    }
                                }
                            }}
                            multiple
                        />
                    </div>
                </div>

                <div
                    className="w-1 absolute top-0 bottom-0 cursor-col-resize bg-transparent z-[100] hover:bg-[#1890ff] active:bg-opacity-25"
                    style={{ 
                        right: -2,
                        ...(isDragging && {backgroundColor: '#1890ff'})
                    }}
                    onMouseDown={handleMouseDownResize}
                />

                {/* <DeletePdf 
                isModalOpen={openConfirmModal} 
                onChange={setDeleteNode}
                loading={deleting}
                handleCancel={() => {
                    if (!deleting) {
                        setOpenConfirmModal(false);
                    }
                }}
                handleOk={async () => {
                    if (deleting) return;
                    
                    setDeleting(true);
                    try {
                        // 从PDF store中移除文件
                        usePdfStore.getState().removePdfs(deleteAid);
                        
                        // 从文件列表中移除
                        removeFile(deleteAid);
                        
                        // 调用API删除
                        await onMenuDelete(deleteAid, deleteNode);
                        
                        setOpenConfirmModal(false);
                        setDeleteAid("");
                        setDeleteNode(false);
                    } catch (error) {
                        console.error('删除失败:', error);
                    } finally {
                        setDeleting(false);
                    }
                }}
            /> */}
            </div>
        </FilesContext.Provider >
    );
};