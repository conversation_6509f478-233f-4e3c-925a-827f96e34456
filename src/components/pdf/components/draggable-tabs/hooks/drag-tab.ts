import React, {useCallback} from "react";
import {usePdfStore} from "@/store/pdf-store.ts";
import {DragItem, DropResult} from "@/components/pdf/components/draggable-tabs/types.ts";
import {TabPanelInfo, useTabPanelStore} from "@/store/tab-panel-store";
import {nanoid} from "nanoid";
// 统一懒加载组件，避免在函数内重复创建
const LazyPdfViewer = React.lazy(() => import("../../highlight/PdfViewer").then(module => ({default: module.PdfViewer})));

// 辅助函数：从 label 中提取文本（处理 React 元素的情况）
export const extractLabelText = (label: any): string => {
    // 如果是字符串，直接返回
    if (typeof label === 'string') {
        return label;
    }

    // 如果是 React 元素，尝试提取其 children 或 props
    if (React.isValidElement(label)) {
        const props = label.props as any;
        // 如果有 children 且是字符串
        if (typeof props?.children === 'string') {
            return props.children;
        }
        // 如果有 title 属性
        if (typeof props?.title === 'string') {
            return props.title;
        }
        // 递归处理 children
        if (props?.children) {
            return extractLabelText(props.children);
        }
    }

    // 如果是数组，提取第一个元素
    if (Array.isArray(label)) {
        return label.map(item => extractLabelText(item)).filter(Boolean).join('');
    }

    // 如果是数字，转换为字符串
    if (typeof label === 'number') {
        return String(label);
    }

    // 其他情况，尝试转换为字符串
    return String(label || '');
};

const useDragTab = () => {
    const {
        setTabItems,
        setActiveAid,
        removeTabFromWindow,
        updateWindow,
        closeWindow,
        addTabToWindow,
        setDragTabWindowId,
        setDragTabSourceWindowId,
        setDragHoverWindowId
    } = usePdfStore((state) => ({
        setTabItems: state.setTabItems,
        setActiveAid: state.setActiveAid,
        removeTabFromWindow: state.removeTabFromWindow,
        updateWindow: state.updateWindow,
        closeWindow: state.closeWindow,
        addTabToWindow: state.addTabToWindow,
        setDragTabWindowId: state.setDragTabWindowId,
        setDragTabSourceWindowId: state.setDragTabSourceWindowId,
        setDragHoverWindowId: state.setDragHoverWindowId,
    }));

    const {
        tabPanels: panels,
        addTabPanel,
        setPanelActiveAid,
        addPanelTabItem,
        removePanelTabItem,
        createPanelContent,
        removeTabPanel,
        getPanelContent
    } = useTabPanelStore();
    // 检查是否脱离了容器区域
    const checkIfOutsideContainer = useCallback((item: DragItem, clientOffset: {
        x: number;
        y: number
    } | null, newContainerRect?: DOMRect | null, options?: { margin?: number }) => {
        const containerRect = newContainerRect || usePdfStore.getState().windowsContainer.get(item?.windowId);
        if (!item || !containerRect || !clientOffset) {
            return false
        }

        const tabWidth = item.width ?? 160; // 兜底宽度
        const tabHeight = item.height ?? 50; // 兜底高度
        const tabLeft = clientOffset.x;
        const tabTop = clientOffset.y;
        const tabRight = tabLeft + tabWidth;
        const tabBottom = tabTop + tabHeight;

        const margin = options?.margin ?? 20; // 默认放宽阈值（像素）

        // 收缩容器判定区域（需要更深入地“进入”容器才算在容器内）
        const innerLeft = containerRect.left + margin;
        const innerTop = containerRect.top + margin;
        const innerRight = containerRect.left + containerRect.width - margin;
        const innerBottom = containerRect.top + containerRect.height - margin;

        // 判定矩形是否重叠（与收缩后的容器判定区域）
        const overlapsInner = !(
            tabRight < innerLeft ||
            tabLeft > innerRight ||
            tabBottom < innerTop ||
            tabTop > innerBottom
        );

        // 不重叠 → 认为在容器外（更容易触发“外部”判定）
        if (!overlapsInner) return true;

        // 兜底：使用中心点校验（进一步放宽）
        const cx = tabLeft + tabWidth / 2;
        const cy = tabTop + tabHeight / 2;
        const centerInside = (
            cx >= innerLeft && cx <= innerRight &&
            cy >= innerTop && cy <= innerBottom
        );
        return !centerInside;
    }, []);
    const createTabPanel = useCallback((item: DragItem, clientOffset: { x: number; y: number } | null) => {
        if (!clientOffset) return;

        // 直接从pdfStore获取最新状态
        const state = usePdfStore.getState();
        const currentPanels = panels;
        const currentWindows = state.windows;

        // 直接使用拖拽开始时记录的来源容器，避免因目标也含有相同 key 而误判
        const sourceWindowId = item.windowId;

        // Note: 移动语义在创建新 Panel 后执行移除

        // 创建新的 TabPanel
        const panelId = nanoid();
        // 绑定创建时的项目（workspace id）
        const currentWid = (window as any).__CURRENT_WORKSPACE_ID__ || null;
        const newPanel: TabPanelInfo = {
            id: panelId,
            label: extractLabelText(item.tabItem.label),
            aid: item.id,
            projectId: currentWid ?? undefined,
            position: {
                x: clientOffset.x,
                y: clientOffset.y,
                width: 800,
                height: 600,
                isSnapped: false,
                resizeHandles: ['n', 's', 'e', 'w', 'ne', 'nw', 'se', 'sw']
            }
        };

        // 添加到Panel系统
        addTabPanel(newPanel);

        // 在pdf-store中创建独立的Panel状态，使用当前全局mode作为初始独立mode
        // 使用 tab-panel-store 的 createPanelContent 方法
        const currentMode = state.mode; // 获取当前全局mode作为初始值
        // 总是创建 Panel，并把标签加入 Panel；
        // 对 PDF：重建 children 注入 panelId；对非 PDF：保持原 children
        const pdfInfo = state.pdfs.get(item.id);
        const labelText = extractLabelText(item.tabItem.label);
        const nameLower = labelText.toLowerCase();

        // 改进文件类型判断：优先根据文件名判断，其次根据URL判断
        const isPdfByName = nameLower.endsWith('.pdf');
        const urlLower = String(pdfInfo?.url || '').toLowerCase();
        const isPdfByUrl = urlLower.includes('.pdf');
        const hasUrl = Boolean(pdfInfo?.url);

        // 如果文件名明确是 .pdf 结尾，或者URL包含.pdf，则认为是PDF
        const isPdf = isPdfByName || (hasUrl && isPdfByUrl);
        const tabItemForPanel = item.tabItem;

        createPanelContent(panelId, [tabItemForPanel], item.id, currentMode);

        // 从源容器移除该标签（关闭旧位置）
        try {
            const stateNow = usePdfStore.getState();
            if (sourceWindowId === 'main') {
                const currentTabs = stateNow.tabItems || [];
                const newTabs = currentTabs.filter(t => t.key !== item.id);
                if (newTabs.length !== currentTabs.length) {
                    stateNow.setTabItems(newTabs);
                    if (stateNow.activeAid === item.id) {
                        const idx = currentTabs.findIndex(t => t.key === item.id);
                        const nextKey = newTabs[idx === currentTabs.length - 1 ? idx - 1 : idx]?.key || '';
                        stateNow.setActiveAid(nextKey);
                    }
                }
            } else if (currentPanels.get(sourceWindowId)) {
                // 使用内置API移除，并维护激活态
                const srcPanelContent = getPanelContent(sourceWindowId);
                if (srcPanelContent) {
                    const newTabs = srcPanelContent.tabItems.filter((t: any) => t.key !== item.id);
                    removePanelTabItem(sourceWindowId, item.id);
                    if (srcPanelContent.activeAid === item.id) {
                        const idx = srcPanelContent.tabItems.findIndex((t: any) => t.key === item.id);
                        const nextKey = newTabs[idx === srcPanelContent.tabItems.length - 1 ? idx - 1 : idx]?.key || '';
                        setPanelActiveAid(sourceWindowId, nextKey);
                    }
                    // 如果源 TabPanel 仅有一个标签被拖出，自动关闭该 TabPanel
                    if (newTabs.length === 0) {
                        try {
                            removeTabPanel(sourceWindowId);
                        } catch (e) {
                            // 忽略错误
                        }
                    }
                }
            } else if (currentWindows.get(sourceWindowId)) {
                removeTabFromWindow(sourceWindowId, item.id);
            }
        } catch (e) {
            // 忽略错误
        }
    }, [addTabPanel, setTabItems, setActiveAid, removeTabFromWindow, removePanelTabItem, setPanelActiveAid]);

    const dragEnd = useCallback((item: DragItem, result: DropResult | null) => {
        const dragTabWindowId = usePdfStore.getState().dragTabWindowId;

        // drag end

        // 始终关闭拖拽时创建的临时窗口
        if (dragTabWindowId) {
            // close temp window
            closeWindow(dragTabWindowId);
        }

        // 如果有drop结果，说明拖拽到了某个容器，设置一个标志
        if (result) {
            // 在store中设置一个标志，表示拖拽已经被处理
            usePdfStore.setState({dragHandled: true});

            // 延迟清理拖拽状态，确保TabDragMonitor能读取到
            setTimeout(() => {
                usePdfStore.setState({
                    dragHandled: false,
                    dragHoverWindowId: undefined,
                    dragTabWindowId: undefined,
                    dragTabSourceWindowId: undefined
                });
            }, 200);
        } else {
            // 没有drop结果时也要清理状态
            setTimeout(() => {
                usePdfStore.setState({
                    dragHoverWindowId: undefined,
                    dragTabWindowId: undefined,
                    dragTabSourceWindowId: undefined
                });
            }, 200);
        }

        // 延迟清理拖拽状态，让TabDragMonitor先执行
        setTimeout(() => {
            setDragTabWindowId("");
            setDragTabSourceWindowId("");
            setDragHoverWindowId("");
            usePdfStore.setState({dragHandled: false});
        }, 150);

        // 如果没有 drop 结果，说明拖拽到容器外，TabDragMonitor会处理创建Panel
        if (!result) {
            // no drop
            return;
        }

        // 处理拖拽到其他容器的情况
        if (result.type === 'merge') {
            // add tab to window/panel（移动语义：稍后从源容器移除）
            const targetPanelState = panels.get(result.targetContainer!);
            // 使用拖拽开始时记录的来源容器
            const sourceWindowId = item.windowId;

            // 检查目标容器是否已经有该文件（只激活，不阻止添加）
            const checkFileAlreadyOpen = () => {
                if (result.targetContainer === 'main') {
                    const currentTabItems = usePdfStore.getState().tabItems || [];
                    return currentTabItems.some(tab => tab.key === item.id);
                } else if (targetPanelState) {
                    const targetPanelContent = getPanelContent(result.targetContainer!);
                    return targetPanelContent?.tabItems.some((tab: any) => tab.key === item.id) || false;
                } else {
                    const targetWindow = usePdfStore.getState().windows.get(result.targetContainer!);
                    return targetWindow?.tabs.some(tab => tab.key === item.id) || false;
                }
            };

            // 如果目标容器已经有该文件：只激活目标，并从源容器移除，避免重复添加
            if (checkFileAlreadyOpen()) {
                // already opened; just activate
                if (result.targetContainer === 'main') {
                    setActiveAid(item.id);
                } else if (targetPanelState) {
                    setPanelActiveAid(result.targetContainer!, item.id);
                } else {
                    const targetWindow = usePdfStore.getState().windows.get(result.targetContainer!);
                    if (targetWindow) {
                        updateWindow(result.targetContainer!, {activeTabKey: item.id});
                    }
                }
                // 从源容器移除，防止重复
                if (sourceWindowId && sourceWindowId !== result.targetContainer) {
                    try {
                        const stateNow = usePdfStore.getState();
                        if (sourceWindowId === 'main') {
                            const currentTabs = stateNow.tabItems || [];
                            const newTabs = currentTabs.filter(t => t.key !== item.id);
                            if (newTabs.length !== currentTabs.length) {
                                stateNow.setTabItems(newTabs);
                                if (stateNow.activeAid === item.id) {
                                    const idx = currentTabs.findIndex(t => t.key === item.id);
                                    const nextKey = newTabs[idx === currentTabs.length - 1 ? idx - 1 : idx]?.key || '';
                                    stateNow.setActiveAid(nextKey);
                                }
                            }
                        } else if (panels.get(sourceWindowId)) {
                            const srcPanelContent = getPanelContent(sourceWindowId);
                            if (srcPanelContent) {
                                const newTabs = srcPanelContent.tabItems.filter((t: any) => t.key !== item.id);
                                removePanelTabItem(sourceWindowId, item.id);
                                if (srcPanelContent.activeAid === item.id) {
                                    const idx = srcPanelContent.tabItems.findIndex((t: any) => t.key === item.id);
                                    const nextKey = newTabs[idx === srcPanelContent.tabItems.length - 1 ? idx - 1 : idx]?.key || '';
                                    setPanelActiveAid(sourceWindowId, nextKey);
                                }
                                // 源 TabPanel 被拖空时自动关闭
                                if (newTabs.length === 0) {
                                    try {
                                        removeTabPanel(sourceWindowId);
                                    } catch (e) {
                                        // 忽略错误
                                    }
                                }
                            }
                        } else if (usePdfStore.getState().windows.get(sourceWindowId)) {
                            removeTabFromWindow(sourceWindowId, item.id);
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }
                return; // 不再向目标添加，避免重复
            }

            // 添加到目标容器
            if (result.targetContainer === 'main') {
                // 添加到主 Panel
                const currentTabItems = usePdfStore.getState().tabItems || [];
                const newTabItems = [...currentTabItems, item.tabItem];
                setTabItems(newTabItems);
                setActiveAid(item.id);
            } else if (targetPanelState) {
                // 添加到 TabPanel：对 PDF 重建 children 注入 panelId；非 PDF 保持原 children
                const labelText = extractLabelText(item.tabItem.label);
                const nameLower = labelText.toLowerCase();
                const isPdfByName = nameLower.endsWith('.pdf');
                const pdfInfo = usePdfStore.getState().pdfs.get(item.id);
                const urlLower = String(pdfInfo?.url || '').toLowerCase();
                const isPdfByUrl = urlLower.includes('.pdf');
                const hasUrl = Boolean(pdfInfo?.url);

                // 如果文件名明确是 .pdf 结尾，或者URL包含.pdf，则认为是PDF
                const isPdf = isPdfByName || (hasUrl && isPdfByUrl);
                const tabItemForPanel = item.tabItem;

                addPanelTabItem(result.targetContainer!, tabItemForPanel);
                setPanelActiveAid(result.targetContainer!, item.id);
            } else {
                // 添加到浮动窗口
                addTabToWindow(result.targetContainer!, item.tabItem);
            }

            // 从源容器移除该标签（如果源与目标不同）
            if (sourceWindowId && sourceWindowId !== result.targetContainer) {
                try {
                    const stateNow = usePdfStore.getState();
                    if (sourceWindowId === 'main') {
                        const currentTabs = stateNow.tabItems || [];
                        const newTabs = currentTabs.filter(t => t.key !== item.id);
                        if (newTabs.length !== currentTabs.length) {
                            stateNow.setTabItems(newTabs);
                            if (stateNow.activeAid === item.id) {
                                const idx = currentTabs.findIndex(t => t.key === item.id);
                                const nextKey = newTabs[idx === currentTabs.length - 1 ? idx - 1 : idx]?.key || '';
                                stateNow.setActiveAid(nextKey);
                            }
                        }
                    } else if (panels.get(sourceWindowId)) {
                        const srcPanelContent = getPanelContent(sourceWindowId);
                        if (srcPanelContent) {
                            const newTabs = srcPanelContent.tabItems.filter((t: any) => t.key !== item.id);
                            removePanelTabItem(sourceWindowId, item.id);
                            if (srcPanelContent.activeAid === item.id) {
                                const idx = srcPanelContent.tabItems.findIndex((t: any) => t.key === item.id);
                                const nextKey = newTabs[idx === srcPanelContent.tabItems.length - 1 ? idx - 1 : idx]?.key || '';
                                setPanelActiveAid(sourceWindowId, nextKey);
                            }
                            // 源 TabPanel 被拖空时自动关闭
                            if (newTabs.length === 0) {
                                try {
                                    removeTabPanel(sourceWindowId);
                                } catch (e) {
                                    // 忽略错误
                                }
                            }
                        }
                    } else if (usePdfStore.getState().windows.get(sourceWindowId)) {
                        removeTabFromWindow(sourceWindowId, item.id);
                    }
                } catch (e) {
                    // 忽略错误
                }
            }
        }
    }, [removeTabFromWindow, setTabItems, setActiveAid, closeWindow, addTabToWindow, setDragTabWindowId, setDragTabSourceWindowId, setDragHoverWindowId, panels, removePanelTabItem, setPanelActiveAid, addPanelTabItem, updateWindow])
    return {
        checkIfOutsideContainer,
        dragEnd,
        createTabPanel,
    }
}

export default useDragTab;