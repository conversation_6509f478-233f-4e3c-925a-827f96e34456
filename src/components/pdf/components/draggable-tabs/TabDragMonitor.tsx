import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";
import { usePdfStore } from '@/store/pdf-store';
import { useTabPanelStore } from '@/store/tab-panel-store';
import { CloseOutlined } from "@ant-design/icons";
import { useEffect, useRef } from 'react';
import { useDragLayer } from 'react-dnd';
import { DragItem, DragTypes } from './types';

// 内部组件，只在非标签管理状态下渲染
const TabDragMonitorContent = ({}) => {
    const createdWindowIdRef = useRef<string | null>(null);
    const lastDragItemRef = useRef<DragItem | null>(null);
    const lastOffsetRef = useRef<{ x: number; y: number } | null>(null);
    const {
        createWindow,
        updateWindow,
        closeWindow,
        removeTabFromWindow,
        setDragTabWindowId,
        setDragTabSourceWindowId,
        setDragHoverWindowId,
        setTabItems,
        setActiveAid
    } = usePdfStore((state) => ({
        createWindow: state.createWindow,
        updateWindow: state.updateWindow,
        closeWindow: state.closeWindow,
        removeTabFromWindow: state.removeTabFromWindow,
        setDragTabWindowId: state.setDragTabWindowId,
        setDragTabSourceWindowId: state.setDragTabSourceWindowId,
        setDragHoverWindowId: state.setDragHoverWindowId,
        setTabItems: state.setTabItems,
        setActiveAid: state.setActiveAid,
    }));

    const {
        isDragging,
        item,
        currentOffset,
    } = useDragLayer((monitor) => ({
        isDragging: monitor.isDragging(),
        item: monitor.getItem() as DragItem,
        currentOffset: monitor.getSourceClientOffset(),
    }));

    const {checkIfOutsideContainer, createTabPanel} = useDragTab()
    const {getPanelContent} = useTabPanelStore();
    // 直接使用拖拽开始时记录的来源容器
    const sourceWindowId = item ? item.windowId : null
    const sourceWindow = sourceWindowId ? usePdfStore.getState().windows.get(sourceWindowId) : null
    const sourcePanel = sourceWindowId ? getPanelContent(sourceWindowId) : null
    const isFromMainTabBar = sourceWindowId === 'main';
    const isFromTabPanel = sourcePanel !== null && sourcePanel !== undefined;
    const isSingleTabWindow = sourceWindow && sourceWindow.tabs.length === 1;
    const isSingleTabPanel = sourcePanel && sourcePanel.tabItems.length === 1;
    const shouldHideWindow = !isFromMainTabBar && !isFromTabPanel && isSingleTabWindow;
    // 拖拽中：判定是否离开源容器。拖拽中使用较保守的阈值，避免误判
    let isOutsideTabBar = checkIfOutsideContainer(item, currentOffset, undefined, {margin: 12})
    // console.log("TabDragMonitor shouldHideWindow isOutsideTabBar", shouldHideWindow, isOutsideTabBar)

    useEffect(() => {
        // 保存拖拽状态，以便拖拽结束时使用
        if (isDragging && item && currentOffset) {
            lastDragItemRef.current = item;
            lastOffsetRef.current = currentOffset;
        }

        if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB) {
            if (!isDragging && createdWindowIdRef.current) {
                // 拖拽结束时先不重置，让下面的useEffect处理
                console.log("TabDragMonitor drag ended, waiting for TabPanel creation check");
            } else {
                console.log("TabDragMonitor reset");
                createdWindowIdRef.current = null;
            }
            return;
        }

        const windowPosition = {
            x: currentOffset.x,
            y: currentOffset.y + 49
        };

        if ((isOutsideTabBar || shouldHideWindow) && !createdWindowIdRef.current) {
            // 始终创建临时窗口用于预览，在dragEnd时根据情况决定是创建Panel还是Window
            const windowId = createWindow([item.tabItem], windowPosition);
            console.log('Created temporary window for dragged tab:', windowId);
            createdWindowIdRef.current = windowId;
            setDragTabWindowId(windowId);
            // sourceWindowId 可能为 null，这里做兜底为空字符串
            setDragTabSourceWindowId(sourceWindowId ?? '');
        }

        if (createdWindowIdRef.current) {
            updateWindow(createdWindowIdRef.current!, {
                position: windowPosition,
                zIndex: Math.floor(Date.now() / 1000)
            });
        }
    }, [isDragging, item?.id, currentOffset?.x, currentOffset?.y]);

    const dragHoverWindowId = usePdfStore.getState().dragHoverWindowId
    // 是否需要显示标签
    let shouldShowTab = isOutsideTabBar || shouldHideWindow
    if (dragHoverWindowId) {
        const dragHoverContainer = usePdfStore.getState().windowsContainer.get(dragHoverWindowId)
        // 悬停判定：扩大容器命中区域，便于“回去”
        shouldShowTab = checkIfOutsideContainer(item, currentOffset, dragHoverContainer, {margin: -16})
    }

    useEffect(() => {
        setTimeout(() => {
            if (shouldShowTab && dragHoverWindowId && dragHoverWindowId !== sourceWindowId) {
                console.log("TabDragMonitor removeTab", dragHoverWindowId)
                if (dragHoverWindowId === "main") {
                    const state = usePdfStore.getState();
                    const currentTabItems = state.tabItems || [];
                    const newTabItems = currentTabItems.filter((tab) => tab?.key !== item.id);
                    setTabItems(newTabItems);

                    if (state.activeAid === item.id) {
                        const index = currentTabItems.findIndex((tab) => tab?.key === item.id);
                        if (newTabItems.length > 0) {
                            const newIndex = index === currentTabItems.length - 1 ? index - 1 : index;
                            setActiveAid(newTabItems[newIndex]?.key || '');
                        } else {
                            setActiveAid('');
                        }
                    }
                } else {
                    removeTabFromWindow(dragHoverWindowId, item.id)
                }
            }
        }, 0)
    }, [shouldShowTab, dragHoverWindowId, sourceWindowId, item?.id, setTabItems, setActiveAid, removeTabFromWindow]);

    // 监听拖拽结束，决定是否创建TabPanel
    useEffect(() => {
        if (!isDragging && lastDragItemRef.current && lastOffsetRef.current && createdWindowIdRef.current) {
            // 立即获取状态，在被清除之前
            const state = usePdfStore.getState();
            const dragHoverWindowId = state.dragHoverWindowId;
            const dragHandled = state.dragHandled;
            const finalItem = lastDragItemRef.current;
            const finalOffset = lastOffsetRef.current;

            // 检查是否在容器外
            // 拖拽结束：使用更宽松的阈值，增强“回到容器外”的识别
            const isOutside = checkIfOutsideContainer(finalItem, finalOffset, undefined, {margin: 24});

            console.log('TabDragMonitor - 拖拽结束:', {
                dragHoverWindowId,
                dragHandled,
                isOutside,
                item: finalItem.tabItem.label
            });

            // 如果拖拽已经被处理（有drop结果），不创建Panel
            if (dragHandled) {
                console.log('拖拽已被处理，不创建新Panel');
                createdWindowIdRef.current = null;
                lastDragItemRef.current = null;
                lastOffsetRef.current = null;
                return;
            }

            // 如果拖拽结束时没有悬停在任何窗口上，且在容器外，说明要创建独立Panel
            if (!dragHoverWindowId && isOutside) {
                console.log('创建新的 TabPanel:', finalItem.tabItem.label);
                // 延迟创建，确保dragEnd先执行清理
                setTimeout(() => {
                    createTabPanel(finalItem, {
                        x: finalOffset.x,
                        y: finalOffset.y - 49 // 减去窗口标题栏高度
                    });
                    // 清理引用
                    createdWindowIdRef.current = null;
                    lastDragItemRef.current = null;
                    lastOffsetRef.current = null;
                }, 100);
            } else {
                console.log('不创建 TabPanel，因为:',
                    dragHoverWindowId ? `悬停在窗口 ${dragHoverWindowId}` : '不在容器外');
                // 如果不需要创建Panel，清理引用
                createdWindowIdRef.current = null;
                lastDragItemRef.current = null;
                lastOffsetRef.current = null;
            }
        }
    }, [isDragging, checkIfOutsideContainer, createTabPanel]);

    if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB || !shouldShowTab) {
        return null
    }

    // console.log("TabDragMonitor dragHoverWindowId shouldShowTab", dragHoverWindowId, shouldShowTab)
    return (
        <div className="bg-gray-50 border-b border border-gray-200 rounded-tl-lg rounded-tr-lg" style={{
            position: 'fixed',
            pointerEvents: 'none',
            zIndex: Math.floor(Date.now() / 1000),
            left: currentOffset.x,
            top: currentOffset.y,
            width: shouldShowTab ? '800px' : 'auto',
            height: '50px',
            // opacity: shouldShowTab ? 1 : 0,
        }}>
            <div className="flex items-center min-w-0 flex-1">
                <div className="group">
                    <div className="
    group relative flex items-center px-3 py-2 cursor-pointer select-none
    border-r border-gray-200 min-w-0 max-w-48
    transition-all duration-200 ease-in-out
    bg-white border-b-2 border-b-blue-500 text-blue-600">
                        <div className="flex items-center min-w-0 flex-1"><span
                            className="truncate text-sm font-medium">{item.tabItem.label}</span></div>
                        <button className="
        ml-2 p-1 rounded-full opacity-0 group-hover:opacity-100
        hover:bg-gray-200 transition-opacity duration-200
        opacity-70 hover:opacity-100
      ">
                            <CloseOutlined className="text-xs"/>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );

};

// 简化的导出组件，直接使用内部组件
export const TabDragMonitor = TabDragMonitorContent;
