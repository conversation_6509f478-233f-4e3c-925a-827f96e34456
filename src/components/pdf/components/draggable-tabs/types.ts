// 拖拽相关的类型定义

import React from "react";

export interface TabItem {
    key: string;
    label: string;
    children?: React.ReactNode;
    closable?: boolean;
}

export interface DragItem {
    type: string;
    id: string;
    index: number;
    tabItem: TabItem;
    width: number | undefined;
    height: number | undefined;
    containerRect?: DOMRect | null;
    windowId: string;
}

export interface DropResult {
    type: 'reorder' | 'new-window' | 'merge';
    targetIndex?: number;
    windowId?: string;
    targetContainer?: string;
    mousePosition?: { x: number; y: number };
}

export interface WindowState {
    id: string;
    title: string;
    tabs: TabItem[];
    activeTabKey: string;
    position: { x: number; y: number };
    size: { width: number; height: number };
    isMinimized: boolean;
    zIndex: number;
}

// 拖拽类型常量
export const DragTypes = {
    TAB: 'tab',
    WINDOW: 'window',
} as const;

// 拖拽区域类型
export const DropZoneTypes = {
    TAB_CONTAINER: 'tab-container',
    NEW_WINDOW_ZONE: 'new-window-zone',
    WINDOW_MERGE_ZONE: 'window-merge-zone',
} as const;
