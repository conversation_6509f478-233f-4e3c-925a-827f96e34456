import React, {useCallback, useRef} from 'react';
import {usePdfStore} from '@/store/pdf-store';
import {useTabPanelStore} from '@/store/tab-panel-store';
import {DroppableTabContainer} from './DroppableTabContainer';
import {DragItem, DropResult, TabItem} from './types';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";

interface DraggableTabSystemProps {
    children?: React.ReactNode;
    panelId?: string;
}

export const DraggableTabSystem: React.FC<DraggableTabSystemProps> = ({children, panelId}) => {
    const {
        tabItems: globalTabItems,
        activeAid: globalActiveAid,
        windows,
        setActiveAid: setGlobalActiveAid,
        setTabItems: setGlobalTabItems,
        createWindow,
        addTabToWindow,
        closeWindow,
        removeTabFromWindow,
    } = usePdfStore((state) => ({
        tabItems: state.tabItems,
        activeAid: state.activeAid,
        windows: state.windows,
        setActiveAid: state.setActiveAid,
        setTabItems: state.setTabItems,
        createWindow: state.createWindow,
        addTabToWindow: state.addTabToWindow,
        closeWindow: state.closeWindow,
        removeTabFromWindow: state.removeTabFromWindow,
    }));
    
    const {
        tabPanels,
        setPanelActiveAid,
        setPanelTabItems,
        removePanelTabItem,
        getPanelContent
    } = useTabPanelStore();
    
    // 如果有panelId，使用Panel的独立状态；否则使用全局状态
    const panel = panelId ? getPanelContent(panelId) : null;
    const tabItems = panel ? panel.tabItems : globalTabItems;
    const activeAid = panel ? panel.activeAid : globalActiveAid;
    const setActiveAid = panelId ? (aid: string) => setPanelActiveAid(panelId, aid) : setGlobalActiveAid;
    const setTabItems = panelId ? (items: TabItem[]) => setPanelTabItems(panelId, items) : setGlobalTabItems;
    const {dragEnd} = useDragTab()
    const containerRef = useRef<HTMLDivElement>(null);

    // 主标签页点击
    const handleMainTabClick = useCallback((key: string) => {
        setActiveAid(key);
    }, [setActiveAid]);

    // 主标签页关闭
    const handleMainTabClose = useCallback((key: string) => {
        if (panelId) {
            // TabPanel的标签关闭
            removePanelTabItem(panelId, key);
            const panel = getPanelContent(panelId);
            if (panel && panel.activeAid === key) {
                const newTabItems = panel.tabItems.filter((item: any) => item?.key !== key);
                if (newTabItems.length > 0) {
                    const index = panel.tabItems.findIndex((item: any) => item?.key === key);
                    setPanelActiveAid(panelId, newTabItems[index === panel.tabItems.length - 1 ? index - 1 : index].key);
                }
            }
        } else {
            // 主Panel的标签关闭
            const state = usePdfStore.getState();
            const currentTabItems = state.tabItems || [];
            const newTabItems = currentTabItems.filter((item) => item?.key !== key);
            state.setTabItems(newTabItems);

            if (state.activeAid === key) {
                const index = currentTabItems.findIndex((item) => item?.key === key);
                if (newTabItems.length > 0) {
                    const newIndex = index === currentTabItems.length - 1 ? index - 1 : index;
                    setActiveAid(newTabItems[newIndex]?.key || '');
                } else {
                    setActiveAid('');
                }
            }
        }
    }, [setActiveAid, panelId, removePanelTabItem, setPanelActiveAid, getPanelContent]);

    // 主标签页重排序
    const handleMainTabsReorder = useCallback((tabs: TabItem[]) => {
        const newTabItems = tabs.map(tab => ({
            key: tab.key,
            label: typeof tab.label === 'string' ? tab.label : String(tab.label || ''), // 确保label是字符串
            children: tab.children,
            closable: tab.closable,
        }));
        setTabItems(newTabItems);
    }, [setTabItems]);

    // 主标签页拖拽结束
    const handleMainTabDragEnd = useCallback((item: DragItem, result: DropResult | null) => {
        dragEnd(item, result)
    }, [dragEnd]);

    return (
        <div ref={containerRef} className="h-full flex flex-col pdf-main">
            {/* 主标签容器 */}
            <DroppableTabContainer
                tabs={tabItems}
                windowId={panelId || "main"}  // 使用 panelId 或 "main" 作为容器标识
                activeTabKey={activeAid}
                dragTabWindowId={""}
                onTabClick={handleMainTabClick}
                onTabClose={handleMainTabClose}
                onTabsReorder={handleMainTabsReorder}
                onTabDragEnd={handleMainTabDragEnd}
                className="flex-shrink-0"
            />

            {/* 主内容区域 */}
            <div className="flex-1 min-h-0 overflow-auto">
                {children}
            </div>
        </div>
    );
};
