import React, {useCallback, useRef, useState} from 'react';
import {useDrag} from 'react-dnd';
import {DragItem, DragTypes, DropResult, TabItem, WindowState} from './types';
import {DroppableTabContainer} from './DroppableTabContainer';

interface FloatingWindowProps {
    window: WindowState;
    dragTabWindowId: string;
    dragTabSourceWindowId: string;
    onWindowUpdate: (windowId: string, updates: Partial<WindowState>) => void;
    onWindowClose: (windowId: string) => void;
    onTabClick: (windowId: string, tabKey: string) => void;
    onTabClose: (windowId: string, tabKey: string) => void;
    onTabsReorder: (windowId: string, tabs: TabItem[]) => void;
    onTabDragEnd: (windowId: string, item: DragItem, result: DropResult | null, monitor: any) => void;
    onTabMerge: (sourceWindowId: string, targetWindowId: string, tabItem: TabItem) => void;
    children?: React.ReactNode;
}

export const FloatingWindow: React.FC<FloatingWindowProps> = ({
                                                                  window,
                                                                  dragTabWindowId,
                                                                  dragTabSourceWindowId,
                                                                  onWindowUpdate,
                                                                  onWindowClose,
                                                                  onTabClick,
                                                                  onTabClose,
                                                                  onTabsReorder,
                                                                  onTabDragEnd,
                                                                  onTabMerge,
                                                                  children,
                                                              }) => {
    const windowRef = useRef<HTMLDivElement>(null);
    const [dragOffset, setDragOffset] = useState({x: 0, y: 0});

    // 窗口拖拽
    const [{isDraggingWindow}, dragWindow] = useDrag({
        type: DragTypes.WINDOW,
        item: () => {
            return {type: DragTypes.WINDOW, windowId: window.id};
        },
        collect: (monitor) => ({
            isDraggingWindow: monitor.isDragging(),
        })
    });

    // 处理鼠标拖拽窗口
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        if (e.target !== e.currentTarget) return;

        const rect = windowRef.current?.getBoundingClientRect();
        if (!rect) return;

        setDragOffset({
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
        });

        const handleMouseMove = (e: MouseEvent) => {
            onWindowUpdate(window.id, {
                position: {
                    x: e.clientX - dragOffset.x,
                    y: e.clientY - dragOffset.y,
                },
            });
        };

        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }, [window.id, dragOffset, onWindowUpdate]);

    // 最小化/恢复
    const handleMinimize = () => {
        onWindowUpdate(window.id, {isMinimized: !window.isMinimized});
    };

    // 关闭窗口
    const handleClose = () => {
        onWindowClose(window.id);
    };

    // 激活窗口（提升z-index）
    const handleActivate = () => {
        onWindowUpdate(window.id, {zIndex: Math.floor(Date.now() / 1000)});
    };
    return (
        <div
            ref={(node) => {
                windowRef.current = node;
            }}
            data-window-id={window.id}
            className={`
        fixed bg-white border border-gray-300 shadow-xl
        flex flex-col overflow-hidden
        ${isDraggingWindow ? 'opacity-80' : ''}
        ${dragTabWindowId === window.id ? 'rounded-bl-lg rounded-br-lg' : 'rounded-lg'}
        ${dragTabSourceWindowId === window.id && window.tabs.length === 1 ? 'opacity-0' : ''}
      `}
            style={{
                left: window.position.x,
                top: window.position.y,
                width: window.size.width,
                height: window.size.height,
                zIndex: window.zIndex,
            }}
            onClick={handleActivate}
        >
            {/* 标签栏 - 根据拖拽状态控制显示 */}
            <DroppableTabContainer
                tabs={window.tabs}
                windowId={window.id}
                activeTabKey={window.activeTabKey}
                dragTabWindowId={dragTabWindowId}
                onTabClick={(key) => onTabClick(window.id, key)}
                onTabClose={(key) => onTabClose(window.id, key)}
                onTabsReorder={(tabs) => onTabsReorder(window.id, tabs)}
                onTabDragEnd={(item, result, monitor) => onTabDragEnd(window.id, item, result, monitor)}
            />

            {/* 窗口内容 */}
            <div className="flex-1 overflow-hidden">
                {children}
            </div>
        </div>
    );
};