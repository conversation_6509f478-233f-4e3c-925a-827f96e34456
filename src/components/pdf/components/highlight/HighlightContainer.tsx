import {
    AreaHighlight,
    TextHighlight,
    useHighlightContainerContext,
    usePdfHighlighterContext
} from "@/components/pdf/components/highlight/src";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {useHighlightClick} from "@/components/pdf/hooks/highlight-click.tsx";
import {useHighlightDrag} from "@/components/pdf/hooks/highlight-drag.tsx";
import { TextInsertHighlight } from "./src/components/TextInsertHighlight.tsx";
import { usePdfUpdateText } from "@/components/pdf/hooks/pdf-insert.tsx";

export const HighlightContainer = ({ panelId }: { panelId?: string } = {}) => {
    // highlightContainer
    const {
        highlight,
        viewportToScaled,
        screenshot,
        isScrolledTo,
        highlightBindings,
    } = useHighlightContainerContext<CustomHighlight>()
    // pdfHighlighter
    const {toggleEditInProgress} = usePdfHighlighterContext()
    // 高亮点击
    const {handleTextClick} = useHighlightClick()
    // 高亮拖拽
    const {onDragStop} = useHighlightDrag()
    const {onUpdateText} = usePdfUpdateText()

    if (highlight.type === "area") {
        return <AreaHighlight
            isScrolledTo={isScrolledTo}
            highlight={highlight}
            onChange={(boundingRect) => {
                const customHighlight: CustomHighlight = {
                    ...highlight,
                    position: {
                        boundingRect: viewportToScaled(boundingRect),
                        rects: [],
                    },
                    content: {
                        image: screenshot(boundingRect),
                    },
                }
                onDragStop(customHighlight)
            }}
            bounds={highlightBindings.textLayer}
            onEditStart={() => toggleEditInProgress(true)}
            onMouseDown={() => handleTextClick(highlight)}
            style={{
                borderColor: highlight.id === "empty-id" ? usePdfStore.getState().selectionColor : highlight.color,
            }}
        />
    } else if (highlight.type === "text-insert") {
        return <TextInsertHighlight
            isScrolledTo={isScrolledTo}
            highlight={highlight}
            onMouseDown={() => handleTextClick(highlight)}
            style={{
                backgroundColor: highlight.id === "empty-id" ? usePdfStore.getState().selectionColor : highlight.color,
            }}
            panelId={panelId}
            onChange={boundingRect => {
                const scaledBoundingRect = viewportToScaled(boundingRect);
                const scaledRects = highlight.position.rects.map(viewportToScaled);
                const customHighlight: CustomHighlight = {
                    ...highlight,
                    position: {
                        ...highlight.position,
                        boundingRect: scaledBoundingRect,
                        rects: scaledRects,
                    },
                };
                onDragStop(customHighlight);
            }}
            onTextChange={(text) => {
                const scaledBoundingRect = viewportToScaled(highlight.position.boundingRect);
                const scaledRects = highlight.position.rects.map(viewportToScaled);
                
                const customHighlight: CustomHighlight = {
                    ...highlight,
                    position: {
                        ...highlight.position,
                        boundingRect: scaledBoundingRect,
                        rects: scaledRects,
                    },
                    content: {
                        ...highlight.content,
                        text: text
                    }
                };
                onUpdateText(customHighlight)
            }}
            onEditStart={() => toggleEditInProgress(true)}
            bounds={highlightBindings.textLayer}
        />
    } else {
        return <TextHighlight
            isScrolledTo={isScrolledTo}
            highlight={highlight}
            onMouseDown={() => handleTextClick(highlight)}
            style={{
                backgroundColor: highlight.id === "empty-id" ? usePdfStore.getState().selectionColor : highlight.color,
            }}
        />
    }
}