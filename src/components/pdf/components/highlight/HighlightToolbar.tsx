import {But<PERSON>, Space, Tooltip} from "antd";
import {DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {SketchPicker} from "react-color";
import {CustomHighlight, usePdfStore} from "@/store/pdf-store.ts";
import {IconFont} from "@/components/IconFont";
import {useEffect, useState} from "react";
import {useHighlightChat} from "@/components/pdf/hooks/highlight-chat.ts";
import {NotebookHoverSelector, useNotebookHoverSelector} from "@/components/shared/notebook";
import {useNodeColor} from "@/components/flow/hooks/node-color.ts";
import {useHighlightAdd} from "@/components/pdf/hooks/highlight-add.tsx";
import {useNodeDelete} from "@/components/flow/hooks/node-delete.ts";
import {useEditNodeStore} from "@/store/workerspace-store/edit-node-store";

export const HighlightToolbar = ({
    customHighlight,
    hideNotebookButton = false,
    onColorChange,
    hideAddButton = false,
    onDelete,
}: {
    customHighlight: CustomHighlight,
    hideNotebookButton?: boolean,
    onColorChange?: (color: string) => void,
    hideAddButton?: boolean,
    onDelete?: () => void,
}) => {
    // 颜色列表
    const colorList = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF"]
    // 是否真实高亮
    const isRealHighlight = customHighlight.nid && customHighlight.nid.length > 0
    // 非节点高亮（如文本插入/截图）时，默认展开颜色区
    // const isNonNodeHighlight = !isRealHighlight && (!!onColorChange || hideAddButton || !!onDelete)
    const [showColorList, setShowColorList] = useState(false)
    // useEffect(() => {
    //     if (isNonNodeHighlight) setShowColorList(true)
    // }, [isNonNodeHighlight])
    // 文本插入等“非节点高亮”下，强制采用 mode=2 风格：隐藏新增、允许改色/删除
    const isTextInsertLike = ((customHighlight as any)?.type === 'text-insert') || Boolean(onColorChange || onDelete || hideAddButton)
   
    // 是否显示颜色选择器面板
    const [showColorPicker, setShowColorPicker] = useState(false)
    // 编辑节点弹窗状态
    const { openEditNode } = useEditNodeStore()
    // AI聊天
    const {handleChat} = useHighlightChat()
    // 悬停式笔记本选择器
    const {
        selectorVisible,
        selectorPosition,
        showSelector,
        hideSelector,
        startHideSelector,
        cancelHideSelector,
        selectNotebook,
        quickCopyToLastNotebook,
        lastSelectedNotebook
    } = useNotebookHoverSelector({
        generateContent: () => {
            let content = customHighlight.content?.text || "";
            if (customHighlight.type === "area") {
                content = `![pdf截图-${new Date().toLocaleString()}](${customHighlight.content?.image})`;
            }
            const aid = usePdfStore.getState().activeAid;
            const pdf = usePdfStore.getState().pdfs.get(aid);
            const page = customHighlight.position.boundingRect?.pageNumber;
            if (pdf && page) {
                content = `${content}\n\n（来源：《${pdf.filename}》 第${page}页 ）`;
            } else if (pdf) {
                content = `${content}\n\n（来源：《${pdf.filename}》）`;
            }
            return content;
        }
    })
    // 节点颜色
    const {handleColorChange} = useNodeColor()
    // 新增节点
    const {addHighlight} = useHighlightAdd()
    // 删除节点
    const {handleDeleteNode} = useNodeDelete()
    // 调试日志移除
    const defaultColor = usePdfStore.getState().defaultColor;
    return (
        <div className="bg-black rounded-md">
            <div className="w-full relative ColorSelectToolbar">
                <div
                    className="w-full inline-flex  justify-between  items-center gap-2.5  p-2 rounded-md  text-white cursor-pointer bg-transparent">
                    <Space>
                        {/*生成摘要*/}
                        {/*<Tooltip title="生成摘要">*/}
                        {/*    <Button type="primary" shape="circle" size="small" style={{backgroundColor: "rgba(0,0,0,.5)"}} icon={<SendOutlined/>} onClick={()=>{}} loading={false}/>*/}
                        {/*</Tooltip>*/}
                        {/* 聊天按钮 */}
                        <Tooltip title="AI聊天">
                            <Button type="primary" shape="circle" size="small"
                                    style={{backgroundColor: "rgba(0,0,0,.5)"}}
                                    icon={<IconFont type="icon-chat" style={{color: "#fff"}}/>}
                                    onClick={() => handleChat(customHighlight)}/>
                        </Tooltip>
                        {!hideNotebookButton && (
                            <Tooltip title={lastSelectedNotebook ? `点击快速复制到"${lastSelectedNotebook.file_name}"，悬停查看所有笔记本` : "复制到笔记本"}>
                                <Button type="primary" shape="circle" size="small"
                                        style={{backgroundColor: "rgba(0,0,0,.5)"}} icon={<IconFont type="icon-note"/>}
                                        onMouseEnter={(e) => {
                                            cancelHideSelector(); // 取消任何延迟隐藏
                                            showSelector(e);
                                        }}
                                        onMouseLeave={startHideSelector}
                                        onClick={quickCopyToLastNotebook}/>
                            </Tooltip>
                        )}
                        {isRealHighlight && (
                            <Tooltip title="编辑节点">
                                <Button type="primary" shape="circle" size="small" icon={<EditOutlined/>}
                                        style={{backgroundColor: "rgba(0,0,0,.5)"}}
                                        onClick={() => openEditNode(customHighlight)}/>
                            </Tooltip>
                        )}
                        <Space
                            className="relative overflow-hidden "
                            onMouseEnter={() => setShowColorList(true)}
                            onMouseLeave={() => setShowColorList(false)}
                        >
                            {!isRealHighlight && !hideAddButton && (
                                <Tooltip title="新增节点">
                                    <Button type="primary" shape="circle" size="small" icon={<PlusOutlined/>}
                                            style={{backgroundColor: "rgba(0,0,0,.5)"}}
                                            onClick={() => {
                                                addHighlight({...customHighlight, color: defaultColor})
                                            }}
                                            />
                                </Tooltip>
                            )}
                            <div className="flex justify-around items-center">
                                {(showColorList || isRealHighlight || isTextInsertLike) && (
                                    <Space
                                            style={(isRealHighlight || isTextInsertLike) ? {} : {animation: `${showColorList ? "slideOut" : "slideIn"} 0.3s ease-in-out forwards`}}>                                        {colorList.map((color, index) => (
                                            <div
                                                key={index}
                                                className="w-5 h-5 rounded-full border border-white cursor-pointer"
                                                style={{backgroundColor: color}}
                                                onClick={(_) => {
                                                    if (onColorChange) {
                                                        onColorChange(color)
                                                        return
                                                    }
                                                    if (isRealHighlight) {
                                                        handleColorChange(customHighlight.nid, color)
                                                    } else {
                                                        addHighlight({...customHighlight, color})
                                                    }
                                                }}
                                            />
                                        ))}
                                        <Tooltip title="选择颜色">
                                            <div className="w-6 h-6 rounded border border-white cursor-pointer"
                                                 style={{backgroundColor: customHighlight.color}}
                                                 onClick={() => setShowColorPicker(!showColorPicker)}
                                            />
                                        </Tooltip>
                                    </Space>
                                )}
                            </div>
                        </Space>
                        {/* 删除按钮 */}
                        {(isRealHighlight || onDelete) && (
                            <Tooltip title={isRealHighlight ? "删除节点" : "删除高亮"}>
                                <Button type="primary" shape="circle" size="small"
                                        style={{backgroundColor: "rgba(0,0,0,.5)"}} icon={<DeleteOutlined/>}
                                        onClick={() => {
                                            if (isRealHighlight) {
                                                handleDeleteNode([customHighlight.nid])
                                            } else {
                                                if (onDelete) {
                                                    onDelete()
                                                } else {
                                                    // 兜底：直接从高亮集合移除
                                                    usePdfStore.getState().removeHighlights(customHighlight.aid, customHighlight.id)
                                                }
                                            }
                                        }}/>
                            </Tooltip>
                        )}
                    </Space>
                </div>
                {/* 颜色选择器面板 */}
                {showColorPicker && (
                    <div className="absolute z-100 top-full mt-2">
                        <SketchPicker color={customHighlight.color} onChange={(color) => {
                            if (onColorChange) {
                                onColorChange(color.hex)
                                return
                            }
                            if (isRealHighlight) {
                                handleColorChange(customHighlight.nid, color.hex)
                            } else {
                                addHighlight({...customHighlight, color: color.hex})
                            }
                        }}/>
                    </div>
                )}


            </div>
            
            {/* 悬停式笔记本选择器 */}
            <NotebookHoverSelector
                position={selectorPosition}
                visible={selectorVisible}
                onSelect={selectNotebook}
                onClose={hideSelector}
                onStartHide={startHideSelector}
                onCancelHide={cancelHideSelector}
            />
        </div>
    )
}