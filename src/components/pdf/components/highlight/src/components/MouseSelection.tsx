import {CSSProperties, useEffect, useRef, useState} from "react";

import {asElement, getPageFromElement, isHTMLElement} from "../lib/pdfjs-dom";
import "../style/MouseSelection.css";

import {PDFViewer} from "pdfjs-dist/types/web/pdf_viewer";
import {viewportPositionToScaled} from "../lib/coordinates";
import screenshot from "../lib/screenshot";
import type {LTWH, LTWHP, ScaledPosition, ViewportPosition} from "../types";
import { modeType, usePdfStore } from "@/store/pdf-store";
import { usePdfHighlighterContext } from "../contexts/PdfHighlighterContext";

type Coords = {
    x: number;
    y: number;
};

const getBoundingRect = (start: Coords, end: Coords): LTWH => {
    return {
        left: Math.min(end.x, start.x),
        top: Math.min(end.y, start.y),

        width: Math.abs(end.x - start.x),
        height: Math.abs(end.y - start.y),
    };
};

export const getContainerCoords = (
    container: HTMLElement,
    pageX: number,
    pageY: number,
) => {
    const containerBoundingRect = container.getBoundingClientRect();
    return {
        x: pageX - containerBoundingRect.left + container.scrollLeft,
        y: pageY - containerBoundingRect.top + container.scrollTop - window.scrollY,
    };
};

/**
 * The props type for {@link MouseSelection}.
 *
 * @category Component Properties
 * @internal
 */
export interface MouseSelectionProps {
    /**
     * The PDFViewer instance containing this MouseSelection.
     */
    viewer: PDFViewer;

    /**
     * Callback triggered whenever the user stops dragging their mouse and a valid
     * mouse selection is made. In general, this will only be called if a mouse
     * selection is rendered.
     *
     * @param viewportPosition - viewport position of the mouse selection.
     * @param scaledPosition - scaled position of the mouse selection.
     * @param image - PNG screenshot of the mouse selection.
     * @param resetSelection - Callback to reset the current selection.
     * @param event - Mouse event associated with ending the selection.
     */
    onSelection?(
        viewportPosition: ViewportPosition,
        scaledPosition: ScaledPosition,
        image: string,
        resetSelection: () => void,
        event: MouseEvent,
    ): void;

    /**
     * Callback triggered whenever the current mouse selection is reset.
     * This includes when dragging ends but the selection is invalid.
     */
    onReset?(): void;

    /**
     * Callback triggered whenever a new valid mouse selection begins.
     *
     * @param event - mouse event associated with the new selection.
     */
    onDragStart?(event: MouseEvent): void;

    /**
     * Condition to check before any mouse selection starts.
     *
     * @param event - mouse event associated with the new selection.
     * @returns - `True` if mouse selection should start.
     */
    enableAreaSelection(event: MouseEvent): boolean;

    /**
     * Callback whenever the mouse selection area changes.
     *
     * @param isVisible - Whether the mouse selection is rendered (i.e., non-zero area)
     */
    onChange?(isVisible: boolean): void;

    /**
     * Optional style props for the mouse selection rectangle.
     */
    style?: CSSProperties;

    /**
     * Current mode to determine selection behavior.
     */
    mode?: number;
}

/**
 * A component that enables the creation of rectangular and interactive mouse
 * selections within a given container. NOTE: This does not disable selection in
 * whatever container the component is placed in. That must be handled through
 * the component's events.
 *
 * @category Component
 * @internal
 */
export const MouseSelection = ({
                                   viewer,
                                   onSelection,
                                   onReset,
                                   onDragStart,
                                   enableAreaSelection,
                                   onChange,
                                   style,
                                   mode: propMode,
                               }: MouseSelectionProps) => {
    const [start, setStart] = useState<Coords | null>(null);
    const [end, setEnd] = useState<Coords | null>(null);
    const [locked, setLocked] = useState(false);
    const rootRef = useRef<HTMLDivElement | null>(null);

    const {globalMode} = usePdfStore((state) => ({
        globalMode: state.mode,
    }))
    
    // 获取PDF高亮器上下文，用于检查编辑状态
    const { isEditInProgress } = usePdfHighlighterContext();
    
    // 优先使用传递的mode，否则使用全局mode
    const mode = propMode ?? globalMode;

    // Needed in order to grab the page info of a mouse selection
    const startTargetRef = useRef<HTMLElement | null>(null);

    const reset = () => {
        onReset && onReset();
        setStart(null);
        setEnd(null);
        setLocked(false);
    };

    // Register event listeners onChange
    useEffect(() => {
        onChange && onChange(Boolean(start && end));
        if (!rootRef.current) return;

        // Should be the PdfHighlighter
        const container = asElement(rootRef.current.parentElement);

        const handleMouseUp = (event: MouseEvent) => {
            // 检查是否正在编辑高亮（拖拽已存在的高亮块）
            if (isEditInProgress()) {
                reset(); // 重置状态但不触发截图
                return;
            }
            
            // 总是重置状态，确保不会留下持久的选择框
            const shouldProcessSelection = start && startTargetRef.current && end;
            
            if (!shouldProcessSelection) {
                reset();
                return;
            }

            const boundingRect = getBoundingRect(start, end);

            // 增加最小拖拽距离要求，防止微小移动被识别为有效选择
            const minDragDistance = 5; // 像素
            const shouldEnd = boundingRect.width >= minDragDistance && boundingRect.height >= minDragDistance;

            if (!container.contains(asElement(event.target)) || !shouldEnd) {
                reset();
                return;
            }

            // 只有在有效选择时才锁定
            setLocked(true);

            const page = getPageFromElement(startTargetRef.current!);
            if (!page) {
                reset();
                return;
            }

            const pageBoundingRect: LTWHP = {
                ...boundingRect,
                top: boundingRect.top - page.node.offsetTop,
                left: boundingRect.left - page.node.offsetLeft,
                pageNumber: page.number,
            };

            const viewportPosition: ViewportPosition = {
                boundingRect: pageBoundingRect,
                rects: [],
            };

            const scaledPosition = viewportPositionToScaled(viewportPosition, viewer);

            const image = screenshot(
                pageBoundingRect,
                pageBoundingRect.pageNumber,
                viewer,
            );

            onSelection &&
            onSelection(viewportPosition, scaledPosition, image, reset, event);
        };

        const handleMouseMove = (event: MouseEvent) => {
            if (!rootRef.current || !start || locked || mode === modeType.textInsert) return;
            
            // 检查是否正在编辑高亮（拖拽已存在的高亮块）
            if (isEditInProgress()) {
                return; // 如果正在拖拽高亮块，不更新选择状态
            }
            
            const currentPos = getContainerCoords(container, event.pageX, event.pageY);
            
            // 计算鼠标移动距离，只有超过最小阈值才更新end
            const minMoveDistance = 3; // 像素
            const distanceX = Math.abs(currentPos.x - start.x);
            const distanceY = Math.abs(currentPos.y - start.y);
            
            if (distanceX >= minMoveDistance || distanceY >= minMoveDistance) {
                setEnd(currentPos);
            }
        };

        const handleMouseDown = (event: MouseEvent) => {
            if (mode === modeType.textInsert) return;
            
            // 检查是否正在编辑高亮（拖拽已存在的高亮块）
            if (isEditInProgress()) {
                return; // 如果正在拖拽高亮块，不触发新的选择
            }
            
            const shouldStart = (event: MouseEvent) =>
                enableAreaSelection(event) &&
                isHTMLElement(event.target) &&
                Boolean(asElement(event.target).closest(".page"));

            // If the user clicks anywhere outside a tip, reset the selection
            const shouldReset = (event: MouseEvent) =>
                start &&
                !asElement(event.target).closest(".PdfHighlighter__tip-container");

            if (!shouldStart(event)) {
                if (shouldReset(event)) reset();
                return;
            }

            // 在任何模式下，如果已经有选择状态，先重置
            if (start) {
                reset();
                // 给状态更新一些时间，然后再开始新的选择
                setTimeout(() => {
                    startTargetRef.current = asElement(event.target);
                    onDragStart && onDragStart(event);
                    setStart(getContainerCoords(container, event.pageX, event.pageY));
                    setEnd(null);
                    setLocked(false);
                }, 0);
                return;
            }

            startTargetRef.current = asElement(event.target);
            onDragStart && onDragStart(event);
            setStart(getContainerCoords(container, event.pageX, event.pageY));
            setEnd(null);
            setLocked(false);
        };

        /**
         * Although we register the event listeners on the PdfHighlighter component, we encapsulate
         * them in this separate component to enhance maintainability and prevent unnecessary
         * rerenders of the PdfHighlighter itself. While synthetic events on PdfHighlighter would
         * be preferable, we need to register "mouseup" on the entire document anyway. Therefore,
         * we can't avoid using useEffect. We must re-register all events on state changes, as
         * custom event listeners may otherwise receive stale state.
         */
        container.addEventListener("mousemove", handleMouseMove);
        container.addEventListener("mousedown", handleMouseDown);

        document.addEventListener("mouseup", handleMouseUp);

        return () => {
            container.removeEventListener("mousemove", handleMouseMove);
            container.removeEventListener("mousedown", handleMouseDown);
            document.removeEventListener("mouseup", handleMouseUp);
        };
    }, [start, end, mode, locked, enableAreaSelection, onChange, onDragStart, onSelection, onReset, viewer, isEditInProgress]);

    // 计算是否应该显示选择框
    const shouldRenderSelection = () => {
        if (!start || !end) return false;
        
        const rect = getBoundingRect(start, end);
        // 只有当选择区域足够大时才显示
        const minSize = 3;
        return rect.width >= minSize && rect.height >= minSize;
    };

    return (
        <div className="MouseSelection-container" ref={rootRef}>
            {shouldRenderSelection() && (
                <div
                    className="MouseSelection"
                    style={{...getBoundingRect(start!, end!), ...style}}
                />
            )}
        </div>
    );
};
