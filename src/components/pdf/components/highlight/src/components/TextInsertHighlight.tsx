import React, { CSSProperties, useState, useRef, useEffect } from "react";
import "@/components/pdf/components/highlight/src/style/TextHighlight.css";
import { LTWHP, ViewportHighlight } from "..";
import { modeType, usePdfStore } from "@/store/pdf-store";
import { useTabPanelStore } from "@/store/tab-panel-store";
import { Rnd } from "react-rnd";
// import type {ViewportHighlight, LTWHP} from "../types";

/**
 * The props type for {@link TextInsertHighlight}.
 *
 * @category Component Properties
 */
export interface TextInsertHighlightProps {
    /**
     * The highlight to render.
     */
    highlight: ViewportHighlight & { color?: string };

    /**
     * Whether the highlight is currently being scrolled to.
     */
    isScrolledTo: boolean;

    /**
     * Callback triggered when the highlight is clicked.
     *
     * @param event - The mouse event.
     */
    onMouseDown(event: React.MouseEvent): void;

    /**
     * Optional style props for the highlight.
     */
    style?: CSSProperties;

    /**
     * Callback triggered when the highlight position changes (after dragging).
     * 
     * @param boundingRect - The new bounding rectangle of the highlight.
     */
    onChange?: (boundingRect: LTWHP) => void;
    onTextChange?: (text: string) => void;
    /**
     * Callback triggered when the user starts editing (dragging) the highlight.
     */
    onEditStart?: () => void;

    /**
     * The bounds element for dragging.
     */
    bounds?: HTMLElement;
    panelId?: string;
}

// 检查坐标是否合理，如果不合理则使用默认值
const isValidCoordinate = (value: number) => {
    return !isNaN(value) && isFinite(value) && value >= 0 && value < 10000;
};

/**
 * A component that renders a text insert highlight in a PDF document.
 * This is used for inserted text annotations that are not part of the original document.
 *
 * @category Component
 */
export const TextInsertHighlight = ({
    highlight,
    isScrolledTo,
    onMouseDown,
    style,
    onChange,
    onEditStart,
    bounds,
    onTextChange,
    panelId,
}: TextInsertHighlightProps) => {
    const { position } = highlight;
    const { boundingRect } = position;

    const [{
        left, top
    }, setPosition] = useState(() => {
        const left = isValidCoordinate(boundingRect.left) ? boundingRect.left : 0;
        const top = isValidCoordinate(boundingRect.top) ? boundingRect.top : 0;
        return { left, top }
    })

    // // 确保坐标值在合理范围内
    const _left = isValidCoordinate(boundingRect.left) ? boundingRect.left : 0;
    const _top = isValidCoordinate(boundingRect.top) ? boundingRect.top : 0;

    const { globalMode } = usePdfStore((state) => ({
        globalMode: state.mode
    }))
    const { getPanelContent } = useTabPanelStore();
    const panel = panelId ? getPanelContent(panelId) : null;
    const mode = panel?.mode ?? globalMode;

    useEffect(() => {
        setPosition(() => ({
            left: _left,
            top: _top
        }))
    }, [_left, _top])

    // 初始尺寸与范围
    const MIN_WIDTH = 150;
    const MIN_HEIGHT = 40;
    const MAX_WIDTH = 300;
    const MAX_HEIGHT = 300;

    // 确保宽度和高度在合理范围内
    const initialWidth = isValidCoordinate(boundingRect.width) && boundingRect.width > 0 ?
        boundingRect.width : MIN_WIDTH;
    const initialHeight = isValidCoordinate(boundingRect.height) && boundingRect.height > 0 ?
        boundingRect.height : MIN_HEIGHT;

    const [boxSize, setBoxSize] = useState<{ width: number; height: number }>({
        width: initialWidth,
        height: initialHeight,
    });

    const highlightRef = useRef<HTMLDivElement>(null);
    const measureRef = useRef<HTMLDivElement>(null);
    // 仅在“疑似首次创建”（无有效持久尺寸）时自适应一次
    // 仅当宽高处于“默认最小值”时视为首次创建需要自适应
    const isDefaultWidth = !isValidCoordinate(boundingRect.width) || boundingRect.width <= MIN_WIDTH;
    const isDefaultHeight = !isValidCoordinate(boundingRect.height) || boundingRect.height <= MIN_HEIGHT;
    const shouldAutoSizeRef = useRef<boolean>(isDefaultWidth && isDefaultHeight);
    // 根据文本内容在初次渲染或文本变化时自适应尺寸
    useEffect(() => {
        if (!shouldAutoSizeRef.current) return;
        const text = highlight.content?.text || "";
        if (!measureRef.current) return;
        // 写入文本并测量
        measureRef.current.textContent = text || "";
        const rect = measureRef.current.getBoundingClientRect();
        const measuredWidth = Math.min(Math.max(rect.width + 16, MIN_WIDTH), MAX_WIDTH);
        const measuredHeight = Math.min(Math.max(rect.height + 16, MIN_HEIGHT), MAX_HEIGHT);

        // 若与当前尺寸差异较大，则更新并同步到外部（持久化）
        const diffW = Math.abs(measuredWidth - boxSize.width);
        const diffH = Math.abs(measuredHeight - boxSize.height);
        if (diffW > 1 || diffH > 1) {
            setBoxSize({ width: measuredWidth, height: measuredHeight });
            if (onChange) {
                onChange({
                    left,
                    top,
                    width: measuredWidth,
                    height: measuredHeight,
                    pageNumber: boundingRect.pageNumber,
                });
            }
            // 仅首帧/首次创建自适应一次
            shouldAutoSizeRef.current = false;
        }
        // 仅在文本或初始位置变化时尝试自适应
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [highlight.content?.text, left, top, boundingRect.pageNumber]);
    const [isDragging, setIsDragging] = useState(false);
    const clickStartRef = useRef<{ x: number; y: number } | null>(null);

    const scrollClass = `${isScrolledTo ? "TextInsertHighlight--scrolledTo" : ""}`
    const dragClass = `${isDragging ? "TextInsertHighlight--dragging" : ""} `
    const inTextInsert = mode === modeType.textInsert

    return (
        <div
            className={`TextInsertHighlight ${scrollClass} ${dragClass}`}
            style={{
                ...style,
                pointerEvents: "auto"
            }}
            ref={highlightRef}
            data-highlight-id={highlight.id}
            onMouseDown={(event) => {
                // 仅用于检测“点击而非拖拽”，触发外部 onMouseDown 展示工具栏
                clickStartRef.current = { x: event.clientX, y: event.clientY };
                const handleMouseUp = (upEvent: globalThis.MouseEvent) => {
                    const start = clickStartRef.current;
                    clickStartRef.current = null;
                    if (!start) return;
                    const dx = Math.abs(upEvent.clientX - start.x);
                    const dy = Math.abs(upEvent.clientY - start.y);
                    if (dx < 5 && dy < 5) {
                        onMouseDown?.(event);
                    }
                    document.removeEventListener('mouseup', handleMouseUp, true);
                };
                document.addEventListener('mouseup', handleMouseUp, true);
            }}
        >
            <div className="TextHighlight__parts ">
                <style>{`
                    .TextInsertHighlight__part::-webkit-scrollbar { display: none; width: 0; height: 0; }
                `}</style>
                <Rnd
                    bounds={bounds as unknown as string | Element}
                    default={{ x: left, y: top, width: boxSize.width, height: boxSize.height }}
                    size={{ width: boxSize.width, height: boxSize.height }}
                    enableResizing={Boolean(onChange)}
                    disableDragging={!onChange}
                    cancel=".TextInsertHighlight__textarea"
                    onDragStart={() => { onEditStart && onEditStart(); setIsDragging(true); }}
                    onResizeStart={() => { onEditStart && onEditStart(); setIsDragging(true); }}
                    onDragStop={(_, data) => {
                        if (!onChange) return;
                        // 若位置与尺寸均未变化，则不触发更新
                        const samePos = Math.abs(data.x - left) < 1 && Math.abs(data.y - top) < 1;
                        const nodeW = data.node.offsetWidth;
                        const nodeH = data.node.offsetHeight;
                        const sameSize = Math.abs(nodeW - boxSize.width) < 1 && Math.abs(nodeH - boxSize.height) < 1;
                        if (samePos && sameSize) {
                            setIsDragging(false);
                            return;
                        }
                        const newBoundingRect: LTWHP = {
                            left: data.x,
                            top: data.y,
                            width: data.node.offsetWidth,
                            height: data.node.offsetHeight,
                            pageNumber: boundingRect.pageNumber
                        };
                        onChange(newBoundingRect);
                        setPosition({ left: data.x, top: data.y });
                        setBoxSize({ width: data.node.offsetWidth, height: data.node.offsetHeight });
                        setIsDragging(false);
                    }}
                    onResizeStop={(_e, _dir, ref, _delta, position) => {
                        if (!onChange) return;
                        // 若位置与尺寸均未变化，则不触发更新
                        const samePos = Math.abs(position.x - left) < 1 && Math.abs(position.y - top) < 1;
                        const sameSize = Math.abs(ref.offsetWidth - boxSize.width) < 1 && Math.abs(ref.offsetHeight - boxSize.height) < 1;
                        if (samePos && sameSize) {
                            setIsDragging(false);
                            return;
                        }
                        const newBoundingRect: LTWHP = {
                            left: position.x,
                            top: position.y,
                            width: ref.offsetWidth,
                            height: ref.offsetHeight,
                            pageNumber: boundingRect.pageNumber
                        };
                        onChange(newBoundingRect);
                        setPosition({ left: position.x, top: position.y });
                        setBoxSize({ width: ref.offsetWidth, height: ref.offsetHeight });
                        setIsDragging(false);
                    }}
                    style={{
                        border: `2px solid ${highlight.color || (style as any)?.backgroundColor || '#1890ff'}`,
                        borderRadius: "4px",
                        padding: "8px",
                        boxShadow: isDragging ? "0 4px 8px rgba(0,0,0,0.3)" : "0 2px 8px rgba(0,0,0,0.15)",
                        backgroundColor: "white",
                        display: "flex",
                        alignItems: "flex-start",
                        justifyContent: "flex-start",
                        zIndex: 1000,
                        overflow: "auto",
                        textOverflow: "initial",
                        whiteSpace: "pre-wrap",
                        cursor: onChange ? "move" : "pointer",
                        userSelect: "none",
                        pointerEvents: "auto",
                        scrollbarWidth: 'none',
                        msOverflowStyle: 'none',
                    }}
                    className="TextHighlight__part"
                >
                    <TextArea
                        value={highlight.content?.text || ""}
                        mode={mode}
                        onChange={(text) => {
                            onTextChange?.(text)
                        }} />
                </Rnd>
                {/* 隐藏度量容器，用于首次创建或文本变化时计算尺寸 */}
                <div
                    ref={measureRef}
                    style={{
                        position: 'fixed',
                        top: -99999,
                        left: -99999,
                        visibility: 'hidden',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                        fontSize: '14px',
                        fontFamily: 'inherit',
                        lineHeight: '1.5',
                        maxWidth: `${MAX_WIDTH - 16}px`,
                    }}
                />
            </div>
        </div>
    );

};

const TextArea = ({
    value,
    onChange,
    mode
}: {
    value: string
    onChange: (e: string) => void
    mode: number
}) => {
    const [text, setText] = useState(value)

    return <textarea
        className="pointer-events-auto TextInsertHighlight__textarea"
        style={{
            width: '100%',
            height: '100%',
            boxSizing: 'border-box',
            resize: 'none',
            background: 'transparent',
            backdropFilter: 'blur(2px)',
            pointerEvents: mode === modeType.textInsert ? "auto" : "none",
            border: 'none',
            outline: 'none'
        }}
        cols={30} rows={10}
        placeholder="插入文本"
        value={text}
        onChange={(e) => {
            setText(e.target.value)
        }}
        onBlur={(e) => {
            onChange?.(e.target.value)
        }}
        onClick={(e) => {
            e.stopPropagation()
        }}
        onMouseDown={(e) => {
            e.stopPropagation()
        }}
    // autoFocus
    />
}