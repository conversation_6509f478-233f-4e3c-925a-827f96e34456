import { usePdfStore } from "@/store/pdf-store";
import { viewportToScaled } from "@/components/pdf/components/highlight/src/lib/coordinates";
import { asElement, getPageFromElement } from "../lib/pdfjs-dom";
import { forwardRef, useImperativeHandle, useState, useRef, useEffect, MouseEvent } from "react";
import { ScaledPosition, usePdfHighlighterContext } from "..";
import { viewportPositionToScaled } from "../lib/coordinates";
import { PDF_CLASS_NAME } from "./PdfHighlighter";
import { usePdfInsertText } from "@/components/pdf/hooks/pdf-insert";
import { useWorkerSpaceStore } from "@/store/workerspace-store/store";
import { useHighlightAdd } from "../../../../hooks/highlight-add";
import { getContainerCoords } from "./MouseSelection";

export const TextInsert = forwardRef<any, any>((props, ref) => {

    const [area, setArea] = useState<{
        left: number,
        top: number,
        width: number,
        height: number,
        open: boolean,
        pageNumber: number,
        pageY: number,
        pageX: number,
    }>({
        left: 0,
        top: 0,
        width: 100,
        height: 100,
        open: false,
        pageNumber: 1,  
        pageY: 0,
        pageX: 0,
    })
    const pdfHighlighterUtils = usePdfHighlighterContext()
    const { pdfs, activeAid } = usePdfStore();
    const { addHighlight } = useHighlightAdd()
    const { onInsertText } = usePdfInsertText()
    const { open } = area
    const wid = useWorkerSpaceStore((state) => state.wid)

    useImperativeHandle(ref, () => ({
        handleMouseDown(e: React.MouseEvent) {
            if (!props?.parentRef) return

            const container = asElement(props?.parentRef?.current)
            if (open) {
                setArea((pre) => ({
                    ...pre,
                    open: false
                }));
                return
            }

            // 如果未打开，则打开并设置left/top为鼠标在PDF容器内的位置
            if (!open) {
                // 优先通过 pdfHighlighterUtils 获取 PDF 容器
                const viewer = pdfHighlighterUtils?.getViewer();
                // const pdfContainer = viewer?.container as HTMLElement;

                if (!viewer) return

                // 可以通过这个元素获取到，最精准的pageNumber
                const pageElement = (e?.target as HTMLElement)?.closest('.page') as HTMLElement;
                const pageNumber = Number(pageElement?.getAttribute('data-page-number'))

                // const pdfDomRoot = (e?.target as HTMLElement)?.closest(`.${PDF_CLASS_NAME}`);
                // const clickpageView = viewer.getPageView(pageNumber - 1);
                const scroll = {
                    offsetTop: pageElement?.offsetTop || 0,
                    offsetLeft: pageElement?.offsetLeft || 0,
                }

                const { x, y } = getContainerCoords(container, e.clientX, e.clientY)

                setArea((pre) => ({
                    ...pre,
                    open: true,
                    left: x,
                    top: y,
                    pageNumber,
                    pageY: y - scroll.offsetTop,
                    pageX: x - scroll.offsetLeft,
                }));
                return;
            }
        }
    }))


    // 修复类型错误，e 应为 React.FocusEvent<HTMLTextAreaElement>
    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
        if (e.target.value.trim() === "") {
            setArea((pre) => {
                return {
                    ...pre,
                    open: false
                }
            })
            return
        }


        const viewer = pdfHighlighterUtils?.getViewer();
        if (!viewer) return
        // 文本框左上角（相对 page）
        const { pageX: left, pageY: top } = area
        const pageNumber = area.pageNumber

        // 根据文本内容测量初始宽高（与 TextInsertHighlight 一致的规则）
        const MIN_WIDTH = 150
        const MIN_HEIGHT = 40
        const MAX_WIDTH = 300
        const MAX_HEIGHT = 300

        const measureDiv = document.createElement('div')
        measureDiv.style.position = 'fixed'
        measureDiv.style.top = '-99999px'
        measureDiv.style.left = '-99999px'
        measureDiv.style.visibility = 'hidden'
        measureDiv.style.whiteSpace = 'pre-wrap'
        measureDiv.style.wordBreak = 'break-word'
        measureDiv.style.fontSize = '14px'
        measureDiv.style.fontFamily = 'inherit'
        measureDiv.style.lineHeight = '1.5'
        measureDiv.style.maxWidth = `${MAX_WIDTH - 16}px`
        measureDiv.textContent = e.target.value || ''
        document.body.appendChild(measureDiv)
        const r = measureDiv.getBoundingClientRect()
        document.body.removeChild(measureDiv)
        const measuredWidth = Math.min(Math.max(r.width + 16, MIN_WIDTH), MAX_WIDTH)
        const measuredHeight = Math.min(Math.max(r.height + 16, MIN_HEIGHT), MAX_HEIGHT)

        // 构造 LTWHP（相对 page 的 viewport 坐标）
        const rect = {
            left,
            top,
            width: measuredWidth,
            height: measuredHeight,
            pageNumber,
        }

        // 转换为 scaled 坐标
        const viewportPosition = {
            boundingRect: rect,
            rects: [rect],
        } as any
        const position: ScaledPosition = viewportPositionToScaled(viewportPosition, viewer)

        const text = e.target.value
        const type = "text-insert"

        const defaultColor = usePdfStore.getState().defaultColor || '#1890ff'
        const inserData = {
            wid,
            aid: activeAid,
            text,
            mark: JSON.stringify({
                ...position,
                type,
                content: {
                    text
                }
            }),
            color: (defaultColor.startsWith('#') ? defaultColor.slice(1) : defaultColor)
        } as any

        // 这个方法会同步到flow-节点
        // addHighlight(
        //     {
        //         aid: activeAid,
        //         type: "text-insert",
        //         content: {
        //             text: e.target.value
        //         },
        //         position
        //     } as any
        // )

        onInsertText(activeAid, inserData, {
            position,
            content: {
                text
            },
            type,
            aid: activeAid,
            color: defaultColor
        })

        setArea((pre) => {
            return {
                ...pre,
                open: false
            }
        })

    }

    if (!area.open) return null

    return <div
        className="text-insert-container absolute z-[9999] pointer-events-none"
        style={{
            left: area.left,
            top: area.top,
        }}
        onMouseDown={(e) => {
            e.stopPropagation()
        }}
        onPointerDown={(e) => {
            e.stopPropagation()
        }}
        onClick={(e) => {
            e.stopPropagation()
        }}
    >
        <TextArea onBlur={handleBlur} />
    </div>
})

interface TextAreaProps {
    onBlur: React.FocusEventHandler<HTMLTextAreaElement>;
}

const TextArea = ({
    onBlur,

}: TextAreaProps) => {

    const [text, setText] = useState("");
    const inputRef = useRef<HTMLTextAreaElement | null>(null);

    useEffect(() => {
        // 组件挂载后自动聚焦并选中，确保双击后立即可输入
        if (inputRef.current) {
            inputRef.current.focus();
            // 将光标置于末尾并选中文本，提升体验
            const len = inputRef.current.value.length;
            inputRef.current.setSelectionRange(len, len);
        }
    }, []);

    return <textarea
        ref={inputRef}
        value={text}
        onChange={e => setText(e.target.value)}
        className="w-[200px] h-[100px] border border-gray-300 rounded p-2 bg-white/50 focus:bg-white/70 resize-none pointer-events-auto"
        style={{ backdropFilter: 'blur(2px)' }}
        cols={30} rows={10}
        onBlur={(e) => {
            onBlur(e)
            setText(e.target.value)
        }}
    />
}