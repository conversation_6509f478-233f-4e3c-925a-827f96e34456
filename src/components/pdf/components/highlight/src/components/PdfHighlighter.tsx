import "pdfjs-dist/web/pdf_viewer.css";
import "../style/PdfHighlighter.css";
import "../style/pdf_viewer.less";

import debounce from "lodash/debounce";
import {PDFDocumentProxy} from "pdfjs-dist";
import {CSSProperties, PointerEventHandler, ReactNode, useEffect, useLayoutEffect, useRef, useState} from "react";
import {createRoot} from "react-dom/client";
import {PdfHighlighterContext, PdfHighlighterUtils,} from "../contexts/PdfHighlighterContext";
import {scaledToViewport, viewportPositionToScaled} from "../lib/coordinates";
import getBoundingRect from "../lib/get-bounding-rect";
import getClientRects from "../lib/get-client-rects";
import groupHighlightsByPage from "../lib/group-highlights-by-page";
import {asElement, findOrCreateContainerLayer, getPagesFromRange, getWindow, isHTMLElement,} from "../lib/pdfjs-dom";
import {
    Content,
    GhostHighlight,
    Highlight,
    HighlightBindings,
    PdfScaleValue,
    PdfSelection,
    Tip,
    ViewportPosition,
} from "../types";
import {HighlightLayer} from "./HighlightLayer";
import {MouseSelection} from "./MouseSelection";
import {TipContainer} from "./TipContainer";

import type {
    EventBus as TEventBus,
    PDFLinkService as TPDFLinkService,
    PDFViewer as TPDFViewer
} from "pdfjs-dist/web/pdf_viewer.mjs";
import { modeType, usePdfStore } from "@/store/pdf-store";
import { TextInsert } from "./useText";


let EventBus: typeof TEventBus, PDFLinkService: typeof TPDFLinkService, PDFViewer: typeof TPDFViewer;

(async () => {
    // Due to breaking changes in PDF.js 4.0.189. See issue #17228
    const pdfjs = await import("pdfjs-dist/web/pdf_viewer.mjs");
    EventBus = pdfjs.EventBus;
    PDFLinkService = pdfjs.PDFLinkService;
    PDFViewer = pdfjs.PDFViewer;
})();


const SCROLL_MARGIN = 10;
const DEFAULT_SCALE_VALUE = "auto";
const DEFAULT_TEXT_SELECTION_COLOR = "rgba(153,193,218,255)";
export const PDF_CLASS_NAME = 'pdfViewer'
const findOrCreateHighlightLayer = (textLayer: HTMLElement) => {
    return findOrCreateContainerLayer(
        textLayer,
        "PdfHighlighter__highlight-layer",
    );
};

const disableTextSelection = (viewer: InstanceType<typeof PDFViewer>, flag: boolean) => {
    viewer.viewer?.classList.toggle("PdfHighlighter--disable-selection", flag);
};

/**
 * The props type for {@link PdfHighlighter}.
 *
 * @category Component Properties
 */
export interface PdfHighlighterProps {
    /**
     * Array of all highlights to be organised and fed through to the child
     * highlight container.
     */
    highlights: Array<Highlight>;

    /**
     * Event is called only once whenever the user changes scroll after
     * the autoscroll function, scrollToHighlight, has been called.
     */
    onScrollAway?(): void;

    /**
     * What scale to render the PDF at inside the viewer.
     */
    pdfScaleValue?: PdfScaleValue;

    /**
     * Callback triggered whenever a user finishes making a mouse selection or has
     * selected text.
     *
     * @param PdfSelection - Content and positioning of the selection. NOTE:
     * `makeGhostHighlight` will not work if the selection disappears.
     */
    onSelection?(PdfSelection: PdfSelection): void;

    /**
     * Callback triggered whenever a ghost (non-permanent) highlight is created.
     *
     * @param ghostHighlight - Ghost Highlight that has been created.
     */
    onCreateGhostHighlight?(ghostHighlight: GhostHighlight): void;

    /**
     * Callback triggered whenever a ghost (non-permanent) highlight is removed.
     *
     * @param ghostHighlight - Ghost Highlight that has been removed.
     */
    onRemoveGhostHighlight?(ghostHighlight: GhostHighlight): void;

    /**
     * Optional element that can be displayed as a tip whenever a user makes a
     * selection.
     */
    selectionTip?: ReactNode;

    /**
     * Condition to check before any mouse selection starts.
     *
     * @param event - mouse event associated with the new selection.
     * @returns - `True` if mouse selection should start.
     */
    enableAreaSelection?(event: MouseEvent): boolean;

    /**
     * Optional CSS styling for the rectangular mouse selection.
     */
    mouseSelectionStyle?: CSSProperties;

    /**
     * Current mode to determine selection behavior.
     */
    mode?: number;

    /**
     * PDF document to view and overlay highlights.
     */
    pdfDocument: PDFDocumentProxy;

    /**
     * This should be a highlight container/renderer of some sorts. It will be
     * given appropriate context for a single highlight which it can then use to
     * render a TextHighlight, AreaHighlight, etc. in the correct place.
     */
    children: ReactNode;

    /**
     * Coloring for unhighlighted, selected text.
     */
    textSelectionColor?: string;

    /**
     * Creates a reference to the PdfHighlighterContext above the component.
     *
     * @param pdfHighlighterUtils - various useful tools with a PdfHighlighter.
     * See {@link PdfHighlighterContext} for more description.
     */
    utilsRef(pdfHighlighterUtils: PdfHighlighterUtils): void;

    /**
     * Style properties for the PdfHighlighter (scrollbar, background, etc.), NOT
     * the PDF.js viewer it encloses. If you want to edit the latter, use the
     * other style props like `textSelectionColor` or overwrite pdf_viewer.css
     */
    style?: CSSProperties;
}

/**
 * This is a large-scale PDF viewer component designed to facilitate
 * highlighting. It should be used as a child to a {@link PdfLoader} to ensure
 * proper document loading. This does not itself render any highlights, but
 * instead its child should be the container component for each individual
 * highlight. This component will be provided appropriate HighlightContext for
 * rendering.
 *
 * @category Component
 */
export const PdfHighlighter = ({
                                   highlights,
                                   onScrollAway,
                                   pdfScaleValue = DEFAULT_SCALE_VALUE,
                                   onSelection: onSelectionFinished,
                                   onCreateGhostHighlight,
                                   onRemoveGhostHighlight,
                                   selectionTip,
                                   enableAreaSelection,
                                   mouseSelectionStyle,
                                   pdfDocument,
                                   children,
                                   textSelectionColor = DEFAULT_TEXT_SELECTION_COLOR,
                                   utilsRef,
                                   style,
                                   mode: propMode,
                               }: PdfHighlighterProps) => {
    // State
    const [tip, setTip] = useState<Tip | null>(null);
    const [isViewerReady, setIsViewerReady] = useState(false);

    // Refs
    const containerNodeRef = useRef<HTMLDivElement | null>(null);
    const highlightBindingsRef = useRef<{ [page: number]: HighlightBindings }>(
        {},
    );
    const ghostHighlightRef = useRef<GhostHighlight | null>(null);
    const selectionRef = useRef<PdfSelection | null>(null);
    const scrolledToHighlightIdRef = useRef<string | null>(null);
    const isAreaSelectionInProgressRef = useRef(false);
    const isEditInProgressRef = useRef(false);
    const updateTipPositionRef = useRef(() => {
    });

    const eventBusRef = useRef<InstanceType<typeof EventBus>>(new EventBus());
    const linkServiceRef = useRef<InstanceType<typeof PDFLinkService>>(
        new PDFLinkService({
            eventBus: eventBusRef.current,
            externalLinkTarget: 2,
        }),
    );
    const resizeObserverRef = useRef<ResizeObserver | null>(null);
    const viewerRef = useRef<InstanceType<typeof PDFViewer> | null>(null);

    const {globalMode} = usePdfStore((state) => ({
        globalMode: state.mode,
    }))
    
    // 优先使用传递的mode，否则使用全局mode
    const mode = propMode ?? globalMode;

    const textInsertRef = useRef<{handleMouseDown: (e: React.MouseEvent) => void} | null>(null);

    useEffect(() => {
        if (mode !== modeType.textInsert) return
        // 禁止
        viewerRef.current?.viewer?.classList.add("PdfHighlighter--disable-selection");
        return () => {
            viewerRef.current?.viewer?.classList.remove("PdfHighlighter--disable-selection");
        }
    }, [isViewerReady, mode])

    useEffect(() => {

        if (mode === modeType.textInsert) return 

        const handleDocumentMouseDown = (e: MouseEvent) => {
            
            const target = e?.target as Node;
            const container = containerNodeRef.current;
            
            if (container && (container === target || container.contains(target))) {
                console.log('内部');
                // 这里可以添加处理逻辑
                container.classList.add("inner");
            } else {
                // 这里可以添加外部点击的处理逻辑
            }
        }

        const handleUp = (e:MouseEvent) => {
            const container = containerNodeRef.current;
            if (container) {
                container.classList.remove("inner");
            }
        }
        
        document?.addEventListener('mousedown', handleDocumentMouseDown)
        document?.addEventListener('mouseup', handleUp)

        return () => {
            document?.removeEventListener('mousedown', handleDocumentMouseDown)
            document?.removeEventListener('mouseup', handleUp)
        }

    }, [isViewerReady, mode])

    // Initialise PDF Viewer
    useLayoutEffect(() => {
        if (!containerNodeRef.current) return;

        // 清理旧的高亮绑定，防止切换PDF时显示旧PDF的标记
        // 先卸载所有现有的React根节点，避免内存泄漏
        Object.values(highlightBindingsRef.current).forEach(binding => {
            if (binding.reactRoot) {
                try {
                    binding.reactRoot.unmount();
                } catch (e) {
                    console.debug('Failed to unmount highlight binding:', e);
                }
            }
        });
        highlightBindingsRef.current = {};

        const debouncedDocumentInit = debounce(() => {
            viewerRef.current =
                viewerRef.current ||
                new PDFViewer({
                    container: containerNodeRef.current!,
                    eventBus: eventBusRef.current,
                    textLayerMode: 2,
                    removePageBorders: true,
                    linkService: linkServiceRef.current,
                });

            viewerRef.current.setDocument(pdfDocument);
            linkServiceRef.current.setDocument(pdfDocument);
            linkServiceRef.current.setViewer(viewerRef.current);
            setIsViewerReady(true);
        }, 100);

        debouncedDocumentInit();

        return () => {
            debouncedDocumentInit.cancel();
            // 组件卸载时也清理高亮绑定
            Object.values(highlightBindingsRef.current).forEach(binding => {
                if (binding.reactRoot) {
                    try {
                        binding.reactRoot.unmount();
                    } catch (e) {
                        console.debug('Failed to unmount highlight binding:', e);
                    }
                }
            });
            highlightBindingsRef.current = {};
        };
    }, [pdfDocument]);

    // Initialise viewer event listeners
    useLayoutEffect(() => {
        if (!containerNodeRef.current) return;

        resizeObserverRef.current = new ResizeObserver(handleScaleValue);
        resizeObserverRef.current.observe(containerNodeRef.current);

        const doc = containerNodeRef.current.ownerDocument;

        eventBusRef.current.on("textlayerrendered", renderHighlightLayers);
        eventBusRef.current.on("pagesinit", handleScaleValue);
        doc.addEventListener("keydown", handleKeyDown);

        renderHighlightLayers();

        return () => {
            eventBusRef.current.off("pagesinit", handleScaleValue);
            eventBusRef.current.off("textlayerrendered", renderHighlightLayers);
            doc.removeEventListener("keydown", handleKeyDown);
            resizeObserverRef.current?.disconnect();
        };
    }, [selectionTip, highlights, onSelectionFinished]);

    // Event listeners
    const handleScroll = () => {
        onScrollAway && onScrollAway();
        scrolledToHighlightIdRef.current = null;
        renderHighlightLayers();
    };

    const handleMouseUp: PointerEventHandler = () => {
        if (mode === modeType.textInsert) return
        const container = containerNodeRef.current;
        const selection = getWindow(container).getSelection();

        if (!container || !selection || selection.isCollapsed || !viewerRef.current)
            return;

        const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

        // Check the selected text is in the document, not the tip
        if (!range || !container.contains(range.commonAncestorContainer)) return;

        const pages = getPagesFromRange(range);
        if (!pages || pages.length === 0) return;

        const rects = getClientRects(range, pages);
        if (rects.length === 0) return;

        // 添加对选区有效性的判断
        // const selectedText = selection.toString().trim();
        // if (!selectedText) return;  // 如果选中的文本为空或只包含空白字符，则不处理

        // 检查选区的高度是否超过一定阈值
        // const boundingRect = getBoundingRect(rects);
        // const maxAllowedHeight = window.innerHeight * 0.8; // 设置最大允许高度为视窗高度的80%
        // if (boundingRect.height > maxAllowedHeight) return;

        const viewportPosition: ViewportPosition = {
            boundingRect: getBoundingRect(rects),
            rects,
        };

        const scaledPosition = viewportPositionToScaled(
            viewportPosition,
            viewerRef.current,
        );

        const content: Content = {
            text: selection.toString().split("\n").join(" "), // Make all line breaks spaces
        };

        selectionRef.current = {
            content,
            type: "text",
            position: scaledPosition,
            makeGhostHighlight: () => {
                ghostHighlightRef.current = {
                    content: content,
                    type: "text",
                    position: scaledPosition,
                };

                onCreateGhostHighlight &&
                onCreateGhostHighlight(ghostHighlightRef.current);
                clearTextSelection();
                renderHighlightLayers();
                return ghostHighlightRef.current;
            },
        };

        onSelectionFinished && onSelectionFinished(selectionRef.current);

        selectionTip &&
        setTip({position: viewportPosition, content: selectionTip});
    };

    const handleMouseDown: PointerEventHandler = (event) => {
        if (!isHTMLElement(event.target)) return;

        // 1) 点击在工具栏、文本高亮内容、弹窗等区域：忽略
        const isOnTip = asElement(event.target).closest('.PdfHighlighter__tip-container');
        const isOnTextParts = asElement(event.target).closest('.TextHighlight__parts');
        const isOnModal = asElement(event.target).closest('.ant-modal') || 
                         asElement(event.target).closest('#summary-modal-container') || 
                         document.getElementById('summary-modal-container')?.contains(event.target as Node) ||
                         asElement(event.target).closest('#notebook-selector-dialog') ||
                         asElement(event.target).closest('.notebook-selector-dialog') ||
                         asElement(event.target).closest('[role="dialog"]');
        if (isOnTip || isOnTextParts || isOnModal) return;

        // 2) 文本插入模式下：点击空白也需要关闭工具栏，但要放行在文本插入框内部的交互
        if (mode === modeType.textInsert) {
            const isOnTextInsert = asElement(event.target).closest('.TextInsertHighlight');
            if (isOnTextInsert) return; // 文本框内部不关闭
            setTip(null);
            removeGhostHighlight();
            toggleEditInProgress(false);
            return;
        }

        // 3) 其他模式：点击空白关闭
        setTip(null);
        clearTextSelection();
        removeGhostHighlight();
        toggleEditInProgress(false);
    };

    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.code === "Escape") {
            clearTextSelection();
            removeGhostHighlight();
            setTip(null);
        }
    };

    const handleScaleValue = () => {
        if (viewerRef.current) {
            viewerRef.current.currentScaleValue = pdfScaleValue.toString();
        }
    };

    // 添加选择过程中的处理函数
    const handleMouseMove: PointerEventHandler = (event) => {
        if (!isEditInProgressRef.current) {
            const selection = getWindow(containerNodeRef.current).getSelection();
            if (selection && !selection.isCollapsed) {
                const range = selection.getRangeAt(0);
                if (range) {
                    // 获取鼠标所在位置的元素
                    const mouseTarget = document.elementFromPoint(event.clientX, event.clientY);
                    if (!mouseTarget) return;
                    
                    // 检查鼠标是否在文本层内
                    const textLayer = mouseTarget.closest('.textLayer');
                    if (!textLayer) {
                        // 如果不在文本层内，不做任何处理，保持当前选择
                        return;
                    }
                    
                    // 获取当前页面的所有文本节点
                    const textSpans = Array.from(textLayer.getElementsByTagName('span'));
                    if (textSpans.length === 0) return;
                    
                    // 获取当前选择的结束节点
                    const currentEndContainer = range.endContainer;
                    const currentEndNode = currentEndContainer.nodeType === Node.TEXT_NODE ? 
                                         currentEndContainer.parentNode : 
                                         currentEndContainer;
                    
                    // 找到鼠标位置最近的文本节点
                    let closestSpan = null;
                    let minDistance = Infinity;
                    
                    for (const span of textSpans) {
                        const rect = span.getBoundingClientRect();
                        // 只考虑在鼠标Y坐标之上或附近的节点
                        if (rect.bottom <= event.clientY + 5) {
                            const distance = Math.abs(event.clientY - rect.bottom);
                            if (distance < minDistance) {
                                minDistance = distance;
                                closestSpan = span;
                            }
                        }
                    }
                    
                    // 如果找到了最近的文本节点，更新选区
                    if (closestSpan) {
                        try {
                            // 检查是否需要更新选区
                            // 如果鼠标在空白区域，但已经有一个有效的选区，就不更新
                            const isOnText = mouseTarget.nodeName === 'SPAN' || 
                                           mouseTarget.closest('.textLayer > span');
                            
                            // 只有当鼠标在文本上，或者找到的最近节点与当前结束节点不同时才更新
                            if (isOnText || closestSpan !== currentEndNode) {
                                const newRange = document.createRange();
                                // 保持原始起点
                                newRange.setStart(range.startContainer, range.startOffset);
                                // 设置终点为找到的最近文本节点的末尾
                                newRange.setEnd(closestSpan, closestSpan.childNodes.length || 0);
                                
                                // 更新选区
                                selection.removeAllRanges();
                                selection.addRange(newRange);
                            }
                        } catch (e) {
                            console.debug('Failed to update selection range:', e);
                        }
                    }
                }
            }
        }
    };

    // Render Highlight layers
    const renderHighlightLayer = (
        highlightBindings: HighlightBindings,
        pageNumber: number,
    ) => {
        if (!viewerRef.current) return;

        highlightBindings.reactRoot.render(
            <PdfHighlighterContext.Provider value={pdfHighlighterUtils}>
                <HighlightLayer
                    highlightsByPage={groupHighlightsByPage([
                        ...highlights,
                        ghostHighlightRef.current,
                    ])}
                    pageNumber={pageNumber}
                    scrolledToHighlightId={scrolledToHighlightIdRef.current}
                    viewer={viewerRef.current}
                    highlightBindings={highlightBindings}
                    children={children}
                />
            </PdfHighlighterContext.Provider>,
        );
    };

    const renderHighlightLayers = () => {
        if (!viewerRef.current) return;

        for (let pageNumber = 1; pageNumber <= pdfDocument.numPages; pageNumber++) {
            const highlightBindings = highlightBindingsRef.current[pageNumber];

            // Need to check if container is still attached to the DOM as PDF.js can unload pages.
            if (highlightBindings?.container?.isConnected) {
                renderHighlightLayer(highlightBindings, pageNumber);
            } else {
                const {textLayer} =
                viewerRef.current!.getPageView(pageNumber - 1) || {};
                if (!textLayer) continue; // Viewer hasn't rendered page yet

                // textLayer.div for version >=3.0 and textLayer.textLayerDiv otherwise.
                const highlightLayer = findOrCreateHighlightLayer(
                    textLayer.div,
                );

                if (highlightLayer) {
                    const reactRoot = createRoot(highlightLayer);
                    highlightBindingsRef.current[pageNumber] = {
                        reactRoot,
                        container: highlightLayer,
                        textLayer: textLayer.div, // textLayer.div for version >=3.0 and textLayer.textLayerDiv otherwise.
                    };

                    renderHighlightLayer(
                        highlightBindingsRef.current[pageNumber],
                        pageNumber,
                    );
                }
            }
        }
    };

    // Utils
    const isEditingOrHighlighting = () => {
        return (
            Boolean(selectionRef.current) ||
            Boolean(ghostHighlightRef.current) ||
            isAreaSelectionInProgressRef.current ||
            isEditInProgressRef.current
        );
    };

    const toggleEditInProgress = (flag?: boolean) => {
        if (flag !== undefined) {
            isEditInProgressRef.current = flag;
        } else {
            isEditInProgressRef.current = !isEditInProgressRef.current;
        }

        // Disable text selection
        if (viewerRef.current)
            viewerRef.current.viewer?.classList.toggle(
                "PdfHighlighter--disable-selection",
                isEditInProgressRef.current,
            );
    };

    const removeGhostHighlight = () => {
        if (onRemoveGhostHighlight && ghostHighlightRef.current)
            onRemoveGhostHighlight(ghostHighlightRef.current);
        ghostHighlightRef.current = null;
        renderHighlightLayers();
    };

    const clearTextSelection = () => {
        selectionRef.current = null;

        const container = containerNodeRef.current;
        const selection = getWindow(container).getSelection();
        if (!container || !selection) return;
        selection.removeAllRanges();
    };

    const scrollToHighlight = (highlight: Highlight) => {
        const { boundingRect, usePdfCoordinates } = highlight.position;
        if (!boundingRect) {
            // 如果 boundingRect 未定义，则直接返回，不进行滚动
            return;
        }
        const pageNumber = boundingRect.pageNumber;

        // 移除滚动监听器，防止用户连续自动滚动时重复添加
        viewerRef.current?.container.removeEventListener("scroll", handleScroll);

        const pageViewport = viewerRef.current!.getPageView(
            pageNumber - 1,
        ).viewport;

        // 将高亮滚动至视窗中间位置
        const scaledRect = scaledToViewport(
            boundingRect,
            pageViewport,
            usePdfCoordinates,
        );
        
        // 获取容器高度和当前滚动位置
        const containerHeight = viewerRef.current!.container.clientHeight || 0;
        const currentScrollTop = viewerRef.current!.container.scrollTop || 0;
        
        // 计算高亮区域在页面中的位置
        const pageElement = viewerRef.current!.getPageView(pageNumber - 1).div;
        const pageTop = pageElement.offsetTop;
        
        // 计算目标滚动位置：高亮区域居中显示
        const highlightTop = pageTop + scaledRect.top;
        const highlightHeight = scaledRect.height;
        const targetScrollTop = highlightTop - (containerHeight / 2) + (highlightHeight / 2);
        
        // 确保滚动位置在有效范围内
        const maxScrollTop = viewerRef.current!.container.scrollHeight - containerHeight;
        const finalScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));

        // 执行滚动
        viewerRef.current!.container.scrollTo({
            top: finalScrollTop,
            behavior: 'smooth'
        });

        scrolledToHighlightIdRef.current = highlight.id;
        renderHighlightLayers();

        // wait for scrolling to finish
        setTimeout(() => {
            viewerRef.current!.container.addEventListener("scroll", handleScroll, {
                once: true,
            });
        }, 100);
    };

    const pdfHighlighterUtils: PdfHighlighterUtils = {
        isEditingOrHighlighting,
        getCurrentSelection: () => selectionRef.current,
        getGhostHighlight: () => ghostHighlightRef.current,
        removeGhostHighlight,
        toggleEditInProgress,
        isEditInProgress: () => isEditInProgressRef.current,
        isSelectionInProgress: () =>
            Boolean(selectionRef.current) || isAreaSelectionInProgressRef.current,
        scrollToHighlight,
        getViewer: () => viewerRef.current,
        getTip: () => tip,
        setTip,
        updateTipPosition: updateTipPositionRef.current,
    };

    utilsRef(pdfHighlighterUtils);


     // 添加滚动条控制逻辑
     useEffect(() => {
        const container = containerNodeRef.current;
        if (!container) return;

        let scrollTimer: NodeJS.Timeout | null = null;

        const showScrollbar = () => {
            container.classList.add('scrolling');

            // 清除之前的定时器
            if (scrollTimer) clearTimeout(scrollTimer);

            // 设置新的定时器，1.5秒后隐藏滚动条
            scrollTimer = setTimeout(() => {
                container.classList.remove('scrolling');
            }, 1000);
        };

        // 监听滚轮事件
        const handleWheel = () => {
            showScrollbar();
        };

        // 监听滚动事件
        const handleScroll = () => {
            showScrollbar();
        };

        container.addEventListener('wheel', handleWheel);
        container.addEventListener('scroll', handleScroll);

        // 清理函数
        return () => {
            container.removeEventListener('wheel', handleWheel);
            container.removeEventListener('scroll', handleScroll);
            if (scrollTimer) clearTimeout(scrollTimer);
        };
    }, []);

    const handleInsertText = (e: React.MouseEvent<HTMLDivElement>) => {
        if (mode === modeType.textInsert) {
            textInsertRef?.current?.handleMouseDown(e)
        }
        e?.stopPropagation()
    }

    return (
        <PdfHighlighterContext.Provider value={pdfHighlighterUtils}>
            <div
                ref={containerNodeRef}
                className="PdfHighlighter"
                onPointerDown={handleMouseDown}
                onPointerUp={handleMouseUp}
                // onPointerMove={handleMouseMove}
                style={{
                    ...style,
                }}
            >
                <div className={PDF_CLASS_NAME}
                    onDoubleClick={handleInsertText}
                />
                {isViewerReady && (
                    <TipContainer
                        viewer={viewerRef.current!}
                        updateTipPositionRef={updateTipPositionRef}
                    />
                )}
                <TextInsert ref={textInsertRef} parentRef={containerNodeRef}/>
                {isViewerReady && enableAreaSelection && (
                    <MouseSelection
                        viewer={viewerRef.current!}
                        onChange={(isVisible) =>
                            (isAreaSelectionInProgressRef.current = isVisible)
                        }
                        enableAreaSelection={enableAreaSelection}
                        style={mouseSelectionStyle}
                        mode={mode}
                        onDragStart={() => disableTextSelection(viewerRef.current!, true)}
                        onReset={() => {
                            selectionRef.current = null;
                            disableTextSelection(viewerRef.current!, false);
                        }}
                        onSelection={(
                            viewportPosition,
                            scaledPosition,
                            image,
                            resetSelection,
                        ) => {
                            selectionRef.current = {
                                content: {image},
                                type: "area",
                                position: scaledPosition,
                                makeGhostHighlight: () => {
                                    ghostHighlightRef.current = {
                                        position: scaledPosition,
                                        type: "area",
                                        content: {image},
                                    };
                                    onCreateGhostHighlight &&
                                    onCreateGhostHighlight(ghostHighlightRef.current);
                                    resetSelection();
                                    renderHighlightLayers();
                                    return ghostHighlightRef.current;
                                },
                            };

                            onSelectionFinished && onSelectionFinished(selectionRef.current);
                            selectionTip &&
                            setTip({position: viewportPosition, content: selectionTip});
                        }}
                    />
                )}
            </div>
        </PdfHighlighterContext.Provider>
    );
};
