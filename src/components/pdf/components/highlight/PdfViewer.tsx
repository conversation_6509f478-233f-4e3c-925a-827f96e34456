import {usePdfStore} from "@/store/pdf-store.ts";
import {useTabPanelStore} from "@/store/tab-panel-store";
import {PdfHighlighter, PdfLoader, PdfSelection} from "@/components/pdf/components/highlight/src";
import {HighlightContainer} from "@/components/pdf/components/highlight/HighlightContainer.tsx";
import {PdfZoom} from "@/components/pdf/components/highlight/PdfZoom.tsx";
import {PdfPage} from "@/components/pdf/components/highlight/PdfPage.tsx";
import {usePdfSelection} from "@/components/pdf/hooks/pdf-selection.tsx";

type PdfViewerProps = {
    aid: string
    panelId?: string // 添加panelId参数
}
export const PdfViewer = ({aid, panelId}: PdfViewerProps) => {
    // pdf信息
    const {url, scale, highlights, setPdfHighlighterUtils, selectionColor, globalMode} = usePdfStore((state) => ({
        url: state.pdfs.get(aid)?.url || "",
        scale: state.pdfs.get(aid)?.scale || 1,
        highlights: state.pdfs.get(aid)?.highlights || [],
        setPdfHighlighterUtils: state.setPdfHighlighterUtils,
        selectionColor: state.selectionColor,
        globalMode: state.mode,
    }));

    const {getPanelContent} = useTabPanelStore();
    // 根据panelId获取对应的mode，如果没有panelId或Panel不存在，使用全局mode
    const panel = panelId ? getPanelContent(panelId) : null;
    const mode = panel?.mode ?? globalMode;

    const {onSelection} = usePdfSelection(panelId)

    // 如果正在加载，显示加载中
    if (!url) {
        return (
            <div className="w-full h-full flex items-center justify-center">
                <div className="text-gray-500">loading...</div>
            </div>
        );
    }

    // 如果加载失败，显示错误
    // if (loadError) {
    //     return (
    //         <div className="w-full h-full flex items-center justify-center">
    //             <div className="text-red-500">{loadError}</div>
    //         </div>
    //     );
    // }

    // 如果没有 URL，显示错误信息
    if (!url) {
        return (
            <div className="w-full h-full flex items-center justify-center">
                <div className="text-gray-500"></div>
            </div>
        );
    }

    return (
        <div className="w-full h-full">
            <PdfLoader document={url}>
                {(pdfDocument) => {
                    return (
                        <div className={`w-full h-full mode-${mode}`}>
                            <PdfHighlighter
                                enableAreaSelection={(event) => event.altKey || mode === 4}
                                mouseSelectionStyle={{backgroundColor: selectionColor}}
                                pdfDocument={pdfDocument}
                                utilsRef={(_pdfHighlighterUtils) => {
                                    setPdfHighlighterUtils(aid, _pdfHighlighterUtils)
                                }}
                                pdfScaleValue={scale}
                                textSelectionColor={selectionColor}
                                highlights={highlights}
                                mode={mode}
                                onSelection={(selection: PdfSelection) => onSelection(aid, selection.makeGhostHighlight())}>
                                <HighlightContainer panelId={panelId}/>
                            </PdfHighlighter>
                            <PdfZoom aid={aid}/>
                            <PdfPage aid={aid} totalPages={pdfDocument.numPages}/>
                        </div>
                    )
                }}
            </PdfLoader>
        </div>
    )
}