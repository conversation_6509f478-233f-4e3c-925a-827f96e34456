import { Button } from "@/components/ui/button"

export const MouseIcon = () => {
    return <svg width="7" height="11" viewBox="0 0 7 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0.0528982 1.54942L0.128958 8.13989C0.132956 8.48594 0.416718 8.76323 0.76278 8.75925H0.76278C0.911065 8.75753 1.05394 8.70328 1.16599 8.60615L2.7694 7.21625L4.40438 10.0481C4.57741 10.3478 4.96065 10.4505 5.26035 10.2775L5.94733 9.88083C6.24705 9.70779 6.34973 9.32455 6.17669 9.02485L4.54173 6.19299L6.54855 5.49885C6.87561 5.38573 7.04903 5.02888 6.9359 4.70181V4.70181C6.88749 4.56184 6.79106 4.44354 6.66372 4.36789L0.999495 1.00344C0.701947 0.826703 0.317473 0.924632 0.140726 1.22218L0.140726 1.22218C0.0819566 1.32113 0.0515696 1.43434 0.0528982 1.54942L0.0528982 1.54942Z" fill="currentColor" />
    </svg>

}

export const HightIcon = () => {
    return <svg width="9" height="11" viewBox="0 0 9 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1.25 0.8125C1.06766 0.8125 0.892795 0.884933 0.763864 1.01386C0.634933 1.1428 0.5625 1.31766 0.5625 1.5V3.21875C0.5625 3.49226 0.671149 3.75456 0.864546 3.94796C1.05794 4.14135 1.32024 4.25 1.59375 4.25C1.58229 4.25 1.57083 4.25069 1.55937 4.25206H1.6295L1.59375 4.25H1.76287V4.25206H7.63756V4.25H7.78193L7.74687 4.25206H7.81699L7.78124 4.25H7.78193C8.05544 4.25 8.31774 4.14135 8.51113 3.94796C8.70453 3.75456 8.81318 3.49226 8.81318 3.21875V1.5C8.81318 1.31766 8.74075 1.1428 8.61182 1.01386C8.48288 0.884933 8.30802 0.8125 8.12568 0.8125H1.25ZM1.25069 4.93957C1.25123 5.30388 1.39634 5.65309 1.65414 5.91051C1.91195 6.16792 2.26137 6.3125 2.62569 6.3125H6.75068C7.115 6.3125 7.46442 6.16792 7.72222 5.91051C7.98003 5.65309 8.12513 5.30388 8.12568 4.93957H1.25069ZM2.62706 10.0938V7H6.75137V7.50876C6.75173 7.68182 6.70853 7.8522 6.62575 8.00418C6.54297 8.15617 6.42327 8.28487 6.27768 8.37844L3.15643 10.3832C3.10449 10.4165 3.04453 10.4352 2.98285 10.4374C2.92118 10.4396 2.86005 10.4251 2.8059 10.3955C2.75175 10.3659 2.70657 10.3222 2.6751 10.2691C2.64363 10.2161 2.62704 10.1555 2.62706 10.0938Z" fill="currentColor" />
    </svg>

}

export const TextIcon = () => {
    return <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.47396 6.85528H8.77603V6.25221H2.47396V6.85528ZM2.47396 5.04607H8.77603V4.443H2.47396V5.04607ZM2.47396 3.23686H8.77603V2.63379H2.47396V3.23686ZM10.7812 10.8217L9.01838 8.96603H1.39458C1.13066 8.96603 0.910278 8.87316 0.733438 8.68741C0.556598 8.50167 0.468369 8.26989 0.468751 7.99207V1.497C0.468751 1.21959 0.557171 0.987808 0.734011 0.801661C0.910852 0.615513 1.13085 0.52264 1.39401 0.523042H9.85598C10.1195 0.523042 10.3395 0.615915 10.516 0.801661C10.6924 0.987406 10.7809 1.21919 10.7812 1.497V10.8217ZM1.39458 8.36296H9.26301L10.2083 9.35441V1.4976C10.2083 1.40473 10.1717 1.3195 10.0983 1.2419C10.025 1.16431 9.94421 1.12571 9.85598 1.12611H1.39401C1.30616 1.12611 1.22538 1.16471 1.15167 1.2419C1.07795 1.3191 1.04129 1.40413 1.04167 1.497V7.99207C1.04167 8.08454 1.07833 8.16958 1.15167 8.24717C1.225 8.32477 1.30578 8.36336 1.39401 8.36296" fill="currentColor" />
    </svg>
}

export const CutIcon = () => {
    return <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.66667 1.75H2.33333C2.01117 1.75 1.75 2.01117 1.75 2.33333V4.66667" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M4.66667 12.25H2.33333C2.01117 12.25 1.75 11.9889 1.75 11.6667V9.33334" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M9.33337 12.25H11.6667C11.9889 12.25 12.25 11.9889 12.25 11.6667V9.33334" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M9.33337 1.75H11.6667C11.9889 1.75 12.25 2.01117 12.25 2.33333V4.66667" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
        <path d="M9.33337 4.08334H4.66671C4.34454 4.08334 4.08337 4.34451 4.08337 4.66668V9.33334C4.08337 9.65551 4.34454 9.91668 4.66671 9.91668H9.33337C9.65554 9.91668 9.91671 9.65551 9.91671 9.33334V4.66668C9.91671 4.34451 9.65554 4.08334 9.33337 4.08334Z" stroke="currentColor" />
    </svg>
}

export const OperateButton = (props: { children: React.ReactElement } & any) => {
    const { children, ...rest } = props
    return <Button
        {...rest}
        // variant={mode === 1 ? "default" : "secondary"}
        // onClick={() => setMode(1)}
        className="w-[25px] h-[25px] operate-btn"
    >
        {
            children
        }
    </Button>
}