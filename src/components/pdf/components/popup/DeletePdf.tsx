import {Button, Checkbox, Flex, Modal} from "antd";
import {ExclamationCircleOutlined} from "@ant-design/icons";

export const DeletePdf = ({isModalOpen, checked, onChange, handleOk, handleCancel, loading = false}: {
    isModalOpen: boolean,
    checked?: boolean,
    onChange: (checked: boolean) => void,
    handleOk: () => void,
    handleCancel: () => void,
    loading?: boolean,
}) => {
    return (
        <Modal
            open={isModalOpen}
            onCancel={handleCancel}
            closable={!loading}
            maskClosable={!loading}
            keyboard={!loading}
            zIndex={2000}
            footer={() => (
                <Flex justify="center" gap="small">
                    <Button onClick={handleCancel} disabled={loading}>取消</Button>
                    <Button onClick={handleOk} type="primary" loading={loading} disabled={loading}>
                        {loading ? '删除中...' : '确认'}
                    </Button>
                </Flex>
            )}
        >
            <Flex vertical justify="center" align="center">
                <div className="pt-3 text-xl leading-4">
                    <ExclamationCircleOutlined className="text-[#e46759]"/> <span>确认要删除吗？</span>
                </div>
                <Checkbox 
                    className="my-5" 
                    disabled={loading}
                    checked={checked}
                    onChange={(e) => {
                    onChange(e.target.checked)
                    }}
                >
                    删除对应的高亮节点
                </Checkbox>
            </Flex>
        </Modal>
    )
}