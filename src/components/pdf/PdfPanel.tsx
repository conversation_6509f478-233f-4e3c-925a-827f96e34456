import {DragAndResize} from "@/components/custom-drag-and-resize";
import {Header} from "@/components/pdf/components/layout/Header.tsx";
import {Main} from "@/components/pdf/components/layout/Main.tsx";
import {Menu} from "@/components/pdf/components/layout/Menu.tsx";
import {PanelPosition, ResizableContainer,} from '@/pages/workspace/Panel';
import {usePdfStore} from "@/store/pdf-store.ts";
import {Flex} from "antd";
import React, {FC, useEffect, useRef} from "react";
import styled from "styled-components";
import "./style/pdf.css";

export const PdfPanel = () => {

    const nodeRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const onWheel = (e: WheelEvent) => {
    
          // 只阻止缩放手势，允许正常滚动
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            e.stopPropagation();
          }
        };
    
        const onKeyDown = (e: KeyboardEvent) => {
    
          // 阻止键盘缩放快捷键
          if ((e.ctrlKey || e.metaKey) && ['+', '-', '=', '0'].includes(e.key)) {
            e.preventDefault();
            e.stopPropagation();
          }
        };
    
        const element = nodeRef?.current;
        if (element) {
          element.addEventListener('wheel', onWheel, { passive: false });
          element.addEventListener('keydown', onKeyDown);
    
          return () => {
            element.removeEventListener('wheel', onWheel);
            element.removeEventListener('keydown', onKeyDown);
          };
        }
      }, []);

    return (
        <div className="flex flex-col h-[100%] ">
            {/*顶部*/}
            <div>
                <Header/>
            </div>
            <div
                className="border-box"
                style={{height: '100%', overflow: 'hidden'}} 
                ref={nodeRef}
                >
                <Flex className="h-[100%]">
                    <Menu/>
                    <Main/>
                </Flex>
            </div>
        </div>
    )
}

export default PdfPanel

export const PdfPanelMain: FC<{
    zIndex: number,
    panelPosition: PanelPosition,
    setPanelPosition: React.Dispatch<React.SetStateAction<PanelPosition>>,
    setPanelsPosition: Record<string, React.Dispatch<React.SetStateAction<PanelPosition>>>,
    setDragPanel: (isDragging: boolean) => void,
    getAdjacentPanels: (panels: any) => {
        leftAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
        rightAdjacentPanels: Array<{
            x: number;
            y: number;
            width: number;
            height: number;
            isOpen: boolean;
            type: string;
        }>;
    },
    handlePanelClick?: () => void,
    onBumpTabPanel?: (panelId: string, dx: number) => void,
    otherPanels?: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        isOpen: boolean;
        type: string
    }>,
}> = (props) => {

    const {
        zIndex,
        panelPosition,
        otherPanels = [],
        ...rest
    } = props

    const handlePdfResizeStart = () => {
        const activeAid = usePdfStore.getState().activeAid
        if (activeAid) {
            const pdfHighlighterUtils = usePdfStore.getState().pdfs.get(activeAid)?.pdfHighlighterUtils
            if (pdfHighlighterUtils) {
                pdfHighlighterUtils.getViewer()?.viewer!.classList.toggle("PdfHighlighter--disable-selection", true);
            }
        }
    }
    const handlePdfResizeStop = () => {
        const activeAid = usePdfStore.getState().activeAid
        if (activeAid) {
            const pdfHighlighterUtils = usePdfStore.getState().pdfs.get(activeAid)?.pdfHighlighterUtils
            if (pdfHighlighterUtils) {
                pdfHighlighterUtils.getViewer()?.viewer!.classList.toggle("PdfHighlighter--disable-selection", false);
            }
        }
    }

    return <DragAndResize
        panelPosition={panelPosition}
        className="pdf-panel-resizable"
        panelClassName="pdf-panel-resizable"
        zIndex={zIndex}
        innerElement={<PdfPanel/>}
        PanelWrapper={PdfPanelWrapper}
        otherPanels={otherPanels}
        extraEvent={{
            handlePdfResizeStart,
            handlePdfResizeStop
        }}
        {...rest}
    />
}


const PdfPanelWrapper = styled(ResizableContainer)`
    top: 20px;
    left: calc(100% - 420px); /* 使用left而不是right，预留20px边距 */
    border-radius: 30px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.15);
    overflow: hidden;

`;
