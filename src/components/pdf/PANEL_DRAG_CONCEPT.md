# Panel 级别拖拽概念说明

## 两种拖拽模式对比

### 1. Tab 级别拖拽（当前已实现）
- **触发区域**：Tab 标签本身
- **拖拽效果**：单个 Tab 脱离原 Panel
- **结果**：
  - 拖到空白处 → 创建新 Panel（只包含该 Tab）
  - 拖到其他 Panel → 合并到目标 Panel
  - 原 Panel 保持不变

### 2. Panel 级别拖拽（建议功能）
- **触发区域**：Panel 标题栏或特定拖拽手柄
- **拖拽效果**：整个 Panel 作为整体移动
- **结果**：
  - 拖到空白处 → Panel 移动到新位置
  - 拖到其他 Panel 附近 → 可能触发吸附/对齐
  - 包含的所有 Tabs 一起移动

## 实际应用场景

### 场景 1：整理工作区布局
用户有 3 个 Panel，想要重新排列它们的位置：
- **现在**：只能通过 DragAndResize 调整每个 Panel 的位置
- **Panel 拖拽**：可以快速拖拽整个 Panel 到想要的位置

### 场景 2：合并多个 Panel
用户想将 Panel A（3个tabs）整体合并到 Panel B：
- **现在**：需要逐个拖拽 3 个 tabs
- **Panel 拖拽**：拖拽 Panel A 到 Panel B 上，一次性合并所有 tabs

### 场景 3：分离 Panel 到新窗口
- **现在**：不支持
- **Panel 拖拽**：拖拽 Panel 到屏幕边缘，可能创建新的浏览器窗口（需要 Electron 等支持）

## 技术实现思路

### 1. 识别拖拽意图
```typescript
// 在 Panel Header 组件中
const handleDragStart = (e: DragEvent) => {
  const isDraggingPanel = e.target.classList.contains('panel-drag-handle');
  const isDraggingTab = e.target.classList.contains('tab-drag-handle');
  
  if (isDraggingPanel) {
    // 设置拖拽数据为整个 Panel
    e.dataTransfer.setData('panel', JSON.stringify({
      panelId,
      tabItems: panel.tabItems,
      position: panel.position
    }));
  }
};
```

### 2. Panel 拖拽预览
```typescript
// 显示整个 Panel 的缩略图作为拖拽预览
const createPanelDragPreview = (panel: PanelState) => {
  return (
    <div className="panel-drag-preview">
      <div className="mini-panel">
        {panel.tabItems.map(tab => (
          <div key={tab.key} className="mini-tab">{tab.label}</div>
        ))}
      </div>
    </div>
  );
};
```

### 3. 处理 Panel 放置
```typescript
const handlePanelDrop = (draggedPanel: PanelState, targetPosition: Position) => {
  if (isNearAnotherPanel(targetPosition)) {
    // 合并 Panel
    mergePanel(draggedPanel, targetPanel);
  } else {
    // 移动 Panel 到新位置
    movePanel(draggedPanel.id, targetPosition);
  }
};
```

## 用户体验优化

### 视觉反馈
1. **拖拽手柄**：Panel 标题栏显示拖拽图标
2. **拖拽预览**：显示 Panel 缩略图，包含所有 tabs
3. **放置区域**：高亮可放置的区域
4. **吸附对齐**：接近其他 Panel 时自动对齐

### 交互细节
1. **区分拖拽类型**：
   - 拖拽 Tab → 单个 Tab 操作
   - 拖拽标题栏 → 整个 Panel 操作
   - 按住 Shift 拖拽 → 复制 Panel

2. **智能合并**：
   - Panel 拖到另一个 Panel 上 → 提示是否合并
   - 拖到 Panel 边缘 → 并排放置
   - 拖到 Tab 栏 → 添加所有 tabs

## 实现优先级

1. **第一阶段**（基础功能）
   - Panel 位置拖拽（已有 DragAndResize）
   - Tab 级别拖拽（已实现）

2. **第二阶段**（增强功能）
   - Panel 整体拖拽到新位置
   - Panel 之间的快速合并

3. **第三阶段**（高级功能）
   - Panel 拖拽到新窗口
   - 多 Panel 选中和批量操作
   - Panel 布局模板保存

## 代码示例

```tsx
// TabPanel.tsx 中添加 Panel 级拖拽
export const TabPanelMain = (props) => {
  const [isDraggingPanel, setIsDraggingPanel] = useState(false);
  
  const handlePanelDragStart = (e: DragEvent) => {
    setIsDraggingPanel(true);
    // 设置拖拽数据
    e.dataTransfer.setData('application/x-panel', JSON.stringify({
      panelId: props.tabId,
      type: 'panel-drag'
    }));
  };
  
  return (
    <DragAndResize>
      <div 
        className="panel-header drag-handle"
        draggable
        onDragStart={handlePanelDragStart}
      >
        {/* Panel 标题栏 */}
      </div>
      <DraggableTabSystem>
        {/* Tabs */}
      </DraggableTabSystem>
    </DragAndResize>
  );
};
```