import { useState } from "react";
import { Dropdown, Tag, Tooltip } from "antd";
import styled from "styled-components";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { CopyableContent } from "@/components/ui/copyable-content";
import { Reference } from "./reference";
import type { ChatInputProps, TagRefType } from "../types";
import { Remark } from "react-remark";
import { getNodeHighlightWithPdf } from "@/store/pdf-store";
import '../../../common/icon.less'
import '../chat.less'
import { LinkIcon } from "@/common/icons/link";
// type: 1. pdf  4. 图片  2 flow节点 

enum PreviewType {
  pdf = 1,
  img = 4,
  flow = 2,
  pdfHightLight = 3,
}

/**
 * 引用标签容器样式
 * 用于水平排列多个引用标签
 */
const CharBar = styled.div`
  display: flex;
  gap: 2px;
  padding: 2px 3px;
  flex-wrap: wrap;
`;

const CustomTag = styled(Tag) <{ $selected: boolean }>`
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 24px;
  position: relative;
  cursor: pointer;
  border: none;
  border: ${(props) => (props.$selected ? "1px solid blue" : "none")};
  
  /* 关闭图标样式 */
  .ant-tag-close-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 4px;
    background-color: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
  }

  /* 标签文本样式 */
  .ant-tag-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
  }
`;

/**
 * 引用选择器组件
 * 用于显示、选择和管理聊天中的引用资源
 *
 * @param currentRefs - 当前聊天的引用列表
 * @param footerRefs - 页脚引用列表
 * @param rid - 聊天记录ID
 * @param isFooter - 是否为页脚输入框
 */
export const ReferencePicker = ({
  currentRefs,
  // footerRefs,
  rid,
  isFooter,
  active,
}: Pick<ChatInputProps, "currentRefs"> & {
  rid: string | number;
  isFooter?: boolean;
  footerRefs?: TagRefType[];
  active?: boolean;
}) => {
  // 当前选中的引用索引
  const [selectedRefIndex, setSelectedRefIndex] = useState<number>(0);
  const [previewClick, setPreviewClick] = useState(false)
  const { currentChatRid } = useChatStore()
  // 从全局状态获取引用相关数据和方法
  const {
    footerRefs,
    currentChat,
    updateCurrentChat,
    updateChatList,
    deleteFooterRef,
  } = useChatStore();

  const getPrewview = () => {

    let curNodeInfo = currentRefs?.[selectedRefIndex]

    if (isFooter) {
      curNodeInfo = footerRefs?.[selectedRefIndex] as any
    }


    if (!curNodeInfo) return null

    if (curNodeInfo?.type === PreviewType.pdf) {
      return <PdfView content={curNodeInfo?.title as string} />
    }

    if (curNodeInfo?.type === PreviewType.flow) {
      //需要展示, 引用来源(如果有), 
      const content = curNodeInfo?.content
      if (curNodeInfo?.id) {
        const pdfInfo = getNodeHighlightWithPdf(curNodeInfo?.id)
        return <FlowView content={content} refTitle={pdfInfo?.filename} />
      }

      return <FlowView content={content} refTitle={''} />
    }

    // 其他,如PDF高亮过来的内容,仅展示内容即可
    if (curNodeInfo?.type === PreviewType.pdfHightLight) {
      // pdf高亮直接展示即可
      const content = curNodeInfo?.content
      return <Other content={content} />
    }

    return <> 未配置的类型: {curNodeInfo?.type}</>
  }

  const renderCurrentView = () => {

    if (active || isFooter) {
      if (currentChatRid === rid) {
        return getPrewview()
      }
    }

    if (previewClick) {
      return getPrewview()
    }
  }

  return (
    <>
      {/* 引用标签栏 */}
      <CharBar
        style={{
          padding: 5,
          paddingTop: 10
        }}
      >
        {
          (active || isFooter) && <Tooltip title="添加引用">
            <div>
              <Dropdown
                trigger={["click"]}
                placement="topRight"
                overlayStyle={{ minWidth: "200px" }}
                dropdownRender={(menu) => (
                  <div onClick={(e) => e.stopPropagation()}>
                    <Reference />
                  </div>
                )}
              >
                <div
                  className="link-icon"
                  onMouseDown={(e) => {
                    e.stopPropagation();
                  }}
                ><LinkIcon /></div>
              </Dropdown>
            </div>
          </Tooltip>
        }

        {currentRefs?.map((tagref, index) => (
          <CusTag
            // $selected={(!!active) && selectedRefIndex === index}
            key={`${tagref?.id + index}`}
            onClose={() => {
              // 删除当前聊天引用
              if (currentChat) {
                // 过滤掉要删除的引用
                const refs = currentChat.refs
                  ? currentChat.refs.filter((ref) => ref.id !== tagref.id)
                  : [];
                const newCurrentChat = {
                  ...currentChat,
                  refs: refs,
                };
                updateCurrentChat(newCurrentChat);
                updateChatList(newCurrentChat);
              }
            }}
            tagref={tagref}
            onClick={() => {
              setSelectedRefIndex(index)
              setPreviewClick(true)
            }}
            className="line-clamp-1"
          >
            <Tooltip title={tagref.title + (tagref.page ? `(第${tagref.page}页)` : '')}>
              {tagref.title ? tagref.title + (tagref.page ? `(第${tagref.page}页)` : '') : "未命名.node"}
            </Tooltip>
          </CusTag>
        ))}

        {/* 页脚引用标签 - 仅在页脚输入框时显示 */}
        {isFooter &&
          footerRefs?.map((tagref, index) => {
            return <CusTag
              tagref={tagref}
              index={index} key={tagref?.id + '_' + index}
              selected={selectedRefIndex === index && currentChatRid === rid}
              onClick={(e: any) => {
                e?.stopPropagation()
                setSelectedRefIndex(index);
              }}

              onClose={() => {
                deleteFooterRef(tagref.id);
              }}
            />
          })}
      </CharBar>
      {
        renderCurrentView()
      }
    </>
  );
};


const PreviewWrap = (props: any) => {
  return <div
    style={{
      padding: 10,
    }}>
    <div
      className="flex items-center border boder-gray rounded-md scrollbar-custom preview-warp">
      {
        props?.children
      }
    </div>
  </div>
}

const PdfView = ({ content = '' }: { content?: string, refTitle?: '' }) => {
  return <PreviewWrap>
    <div className="pdf-icon" />
    <div className="line-clamp-1">{
      content
    }</div>
  </PreviewWrap>
}

const FlowView = ({ content = '', refTitle = '' }: { content?: string, refTitle?: string }) => {

  return <PreviewWrap>
    <div className="node-icon" />
    <div className="line-clamp-1">
      {content}
      {
        !!refTitle && <span>节点绑定的文档: {refTitle}</span>
      }
    </div>
  </PreviewWrap>
}

const Other = ({ content = '' }: { content?: string }) => {
  return <CopyableContent
    content={content}
    iconPosition="right-center"
    className="flex flex-col gap-2.5 border boder-gray p-2.5 rounded-md max-h-[400px] scrollbar-custom vditor-reset"
  >
    <div className="line-clamp-4">
      <Remark>{content}</Remark>
    </div>
  </CopyableContent>
}

const CusTag = (props: any) => {
  const { tagref, onClick, onClose, selected } = props

  const geticon = () => {
    if (props?.tagref?.type === PreviewType.pdf) return 'tag-pdf-icon'
    if (props?.tagref?.type === PreviewType.flow) return 'tag-node-icon'
    return ''
  }
  return <div className={`cus-tag ${!!selected ? 'cus_slect_tag' : ''}`}>
    <div className={geticon()} />
    <span
      className="title line-clamp-1"
      onClick={(e)=> {
        e?.stopPropagation()
        onClick?.()
      }}
    >
      <Tooltip title={tagref.title + (tagref.page ? `(第${tagref.page}页)` : '')}>
        {tagref.title ? tagref.title + (tagref.page ? `(第${tagref.page}页)` : '') : "未命名.node"}
      </Tooltip>
    </span>
    <span className="icon tag-close-icon" onClick={(e) => {
      e?.stopPropagation()
      onClose?.()
    }} />
  </div>
}