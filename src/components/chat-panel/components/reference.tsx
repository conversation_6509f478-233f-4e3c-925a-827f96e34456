import { FC, useEffect, useRef, useState } from "react";
import { Input, message, Upload } from "antd";
import { PlusOutlined, SearchOutlined, FileTextFilled } from "@ant-design/icons";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { ChatResponseRefs, NodeRef, PdfRef } from "../types";
import { messageError } from "@/components/message/hooks";
import { useReactFlow } from "@xyflow/react";
import './reference.less';
import '../../../common/icon.less'
import { ReferenceIcon } from "@/common/icons/link";

/**
 * 高亮显示搜索文本的函数
 * @param text 原始文本
 * @param search 搜索关键词
 * @returns 包含高亮标记的文本元素
 */
const highlightText = (text: string, search: string) => {
  if (!search) return text;
  // 创建正则表达式匹配搜索词（忽略大小写）
  const regex = new RegExp(`(${search})`, "gi");
  // 将文本按匹配项分割，并对匹配部分应用高亮样式
  return text.split(regex).map((part, i) =>
    part.toLowerCase() === search.toLowerCase() ? (
      <span key={i} style={{ backgroundColor: "#ffeb3b" }}>
        {part}
      </span>
    ) : (
      part
    )
  );
};

type ReferenceProps = {
  handleClick?: (source: "pdf" | "node", ref: PdfRef | NodeRef) => void
}

/**
 * 引用管理组件
 * 用于搜索、显示和选择引用资源（PDF文件和节点）
 */
export const Reference: FC<ReferenceProps> = ({ handleClick }) => {
  // 从全局状态获取引用相关数据和方法
  const {
    currentChatRid,
    pdfRefs,
    // nodeRefs,
    footerRefs,
    addFooterRef,
    currentChat,
    updateCurrentChat,
    updateChatList,
    setPdfRefs,
  } = useChatStore();
  const { getNodes } = useReactFlow()

  // const nodes = useFlowStore.getState().nodes;
  const nodes = getNodes()
  // 新增：将 flow-store 的 nodes 映射为 NodeRef 结构
  const flowNodesAsNodeRefs: NodeRef[] = nodes.map((node) => ({
    id: node.id,
    title: (node.data?.title ?? "") as string,
    content: (node.data?.content ?? "") as string,
  }));
  // 搜索输入框引用
  const searchRef = useRef(null);
  // 搜索关键词状态
  const [searchValue, setSearchValue] = useState("");
  // PDF搜索结果状态
  const [pdfSearchResults, setPdfSearchResults] = useState<PdfRef[]>([]);
  // 节点搜索结果状态
  const [nodeSearchResults, setNodeSearchResults] = useState<NodeRef[]>([]);

  // 组件挂载后自动聚焦搜索输入框
  useEffect(() => {
    // @ts-ignore
    searchRef.current.focus();
  }, [searchRef]);

  /**
   * 处理搜索输入变化
   * @param e 输入事件对象
   */
  const onHandleSearch = (e: any) => {
    const searchText = e.target.value;
    setSearchValue(searchText);

    if (searchText !== "") {
      // 搜索PDF文件，忽略大小写
      const pdfResults = pdfRefs.filter((pdf) =>
        pdf.fileName.toLowerCase().includes(searchText.toLowerCase())
      );
      // 搜索节点，忽略大小写
      // TODO 0708,这里以前用的逻辑是nodeRefs, 但是nodeRefs是空的, 所以改成flowNodesAsNodeRefs
      const nodeResults = flowNodesAsNodeRefs.filter((node) =>
        node.content.toLowerCase().includes(searchText.toLowerCase()) || node.title?.toLowerCase().includes(searchText.toLowerCase())
      );

      // 更新搜索结果状态
      setPdfSearchResults(pdfResults);
      setNodeSearchResults(nodeResults);
    } else {
      // 清空搜索结果
      setPdfSearchResults([]);
      setNodeSearchResults([]);
    }
  };

  /**
   * 处理引用项点击事件
   * @param source 引用来源类型（"pdf"或"node"）
   * @param ref 引用对象
   */
  const onHandleClick = (source: "pdf" | "node", ref: PdfRef | NodeRef) => {
    handleClick?.(source, ref)
    if (currentChatRid === -1) {
      if (footerRefs.length === 10) {
        message.error("最多只能添加10个页脚引用");
        return;
      }
      // 处理页脚引用（新聊天模式）
      if (source === "pdf") {
        // 检查PDF引用是否已存在
        const item = footerRefs.find((item) => item.id === ref.id);
        if (!item) {
          // 添加PDF引用到页脚引用
          addFooterRef({
            id: ref.id as string,
            title: (ref as PdfRef).fileName,
            url: (ref as PdfRef).url,
            type: 1, // PDF类型
          });
        } else {
          messageError("重复引用!")
        }
      } else {
        // 检查节点引用是否已存在
        const item = footerRefs.find((item) => item.id === ref.id);
        if (!item) {
          // 添加节点引用到页脚引用
          addFooterRef({
            id: ref.id as string,
            title: (ref as NodeRef).title || "未命名.node",
            content: (ref as NodeRef).content,
            type: 2, // 节点类型
          });
        } else {
          messageError("重复引用!")
        }
      }
    } else {
      // 处理当前聊天引用（编辑现有聊天模式）
      const tempRefs: ChatResponseRefs = [...currentChat?.refs || []];
      if (tempRefs.length === 10) {
        message.error("最多只能添加10个引用");
        return;
      }
      if (source === "pdf") {
        // 检查PDF引用是否已存在于当前聊天引用中
        const item = tempRefs.find((item) => item.id === (ref as PdfRef).id);
        if (!item) {
          // 添加PDF引用到当前聊天引用
          tempRefs.push({
            type: 1, // PDF类型
            id: (ref as PdfRef).id as string,
            title: (ref as PdfRef).fileName,
            url: (ref as PdfRef).url,
            content: "",
          });
          // 更新当前聊天和会话详情
          if (currentChat) {
            currentChat.refs = tempRefs;
            updateCurrentChat(currentChat);
            updateChatList(currentChat);
          }
        } else {
          messageError("重复引用!")
        }
      } else {
        // 检查节点引用是否已存在于当前聊天引用中
        const item = tempRefs.find((item) => item.id === ref.id);
        if (!item) {
          // 添加节点引用到当前聊天引用
          tempRefs.push({
            type: 2, // 节点类型
            id: (ref as NodeRef).id as string,
            title: (ref as NodeRef).title || "未命名.node",
            content: (ref as NodeRef).content,
          } as any);
          // 更新当前聊天和会话详情
          if (currentChat) {
            currentChat.refs = tempRefs;
            updateCurrentChat(currentChat);
            updateChatList(currentChat);
          }
        } else {
          messageError("重复引用!")
        }
      }
    }
  };

  // 上传文件
  const customRequest = (options: any) => {
    const file = options.file;
    const URL = window.URL || window.webkitURL;
    const attachUrl = URL.createObjectURL(file);
    const ref = {
      id: crypto.randomUUID(),
      fileName: file.name,
      url: attachUrl,
      type: 1
    };
    onHandleClick("pdf", ref);
    // 新增：将上传的PDF添加到pdfRefs
    setPdfRefs([...pdfRefs, ref]);
  };

  return (
    <div className="w-80 h-[500px] all-refs-result chat-reference rounded-2xl flex flex-col relative overflow-hidden">
      {/* 搜索输入框区域 */}
      <div className="p-3 search-bg">
        <Input
          ref={searchRef}
          value={searchValue}
          onChange={onHandleSearch}
          placeholder="关键词搜索"
          className="rounded-[10px] border-0  shadow-sm"
          prefix={<SearchOutlined className="text-muted-foreground text-lg" />}
        />
      </div>

      {/* 引用整个知识仓库按钮 */}
      <div className="px-4 py-3">
        <div className="all-refs flex items-center justify-center bg-primary/10 cursor-pointer h-[30px]">
          <span className="flex items-center text-primary font-medium"><ReferenceIcon /> &nbsp; 引用整个知识仓库</span>
        </div>
      </div>

      {/* 引用列表区域 */}
      <div className="flex-1 px-4 pb-16 overflow-y-auto scrollbar-custom">
        {/* PDF文件部分 */}
        {searchValue !== "" ? (
          pdfSearchResults.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-3">File</h3>
              <div className="space-y-2">
                {pdfSearchResults.map((file) => (
                  <div
                    key={file.id}
                    onClick={() => onHandleClick("pdf", file)}
                    className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50 transition-colors shadow-sm"
                  >
                    <span className="text-sm text-foreground truncate">
                      {highlightText(file.fileName, searchValue)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )
        ) : (
          pdfRefs.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-3">File</h3>
              <div className="space-y-2">
                {pdfRefs.map((file) => (
                  <div
                    key={file.id}
                    onClick={() => onHandleClick("pdf", file)}
                    className="search-hover flex items-center p-1 cursor-pointer hover:bg-gray-50 transition-colors "
                  >
                    <div className="flex items-center gap-2 w-full min-w-0">
                      <div className={`tag-pdf-icon w-[20px] h-[20px] flex-shrink-0`} />
                      <span className="text-sm text-foreground truncate flex-1 min-w-0">{file.fileName}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        )}

        {/* 节点部分 */}
        {searchValue !== "" ? (
          nodeSearchResults.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-3">Node</h3>
              <div className="space-y-3">
                {nodeSearchResults.map((node, index) => (
                  <div
                    key={node?.id}
                    onClick={() => onHandleClick("node", node)}
                    className="p-4 bg-white cursor-pointer hover:bg-gray-50 transition-colors shadow-sm"
                  >
                    <div className="flex items-start">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-foreground mb-1 truncate">
                          {highlightText(node.title || "未命名.node", searchValue)}
                        </h4>
                        <p className="text-xs text-muted-foreground line-clamp-3">
                          {highlightText(node.content ?? "", searchValue)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        ) : (
          flowNodesAsNodeRefs.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-3">Node</h3>
              <div className="space-y-3 gap-2">
                {flowNodesAsNodeRefs.map((node, index) => (
                  <div
                    key={`${node?.id}`}
                    onClick={() => onHandleClick("node", node)}
                    className="cursor-pointer hover:bg-gray-50 transition-colors search-hover"
                  >
                    <div className="flex items-start">
                      <div className="flex-1 min-w-0 " >
                        <div className="flex items-center w-full min-w-0 text-sm">
                          <div className={`tag-node-icon w-[20px] h-[20px] flex-shrink-0`} 
                          />&nbsp;
                          {node.title && node.title.trim() !== "" ? node.title : "未命名.node"}
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-3">
                          {node.content}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        )}
      </div>

      {/* 底部上传按钮 */}
      <Upload
        name="avatar"
        listType="text"
        showUploadList={false}
        accept=".pdf"
        multiple={true}
        customRequest={customRequest}
      >
        <div className="absolute bottom-4 left-4 right-4 h-12 bg-primary rounded-[24px] flex items-center justify-center gap-3 cursor-pointer hover:bg-primary/90 transition-colors shadow-lg">
          <PlusOutlined className="text-white text-lg font-bold" />
          <span className="text-white font-medium text-base">上传本地文件</span>
        </div>
      </Upload>
    </div>
  );
};
