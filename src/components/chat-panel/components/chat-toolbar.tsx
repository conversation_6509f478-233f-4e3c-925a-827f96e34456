import { <PERSON>, <PERSON>lt<PERSON>, But<PERSON> } from "antd";
import styled from "styled-components";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { PlusMini, ChatSideIcon } from "@/components/icons";
import { usePanelOpenStore } from "@/store/panel-open-store";
import { CloseOutlined } from "@ant-design/icons";
import { useMemo } from "react";
import '@/common/icon.less'

const ToolbarContainer = styled.div`
  flex: 1;
  height: 46px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding-right: 30px;
`;

// 文本溢出隐藏组件
const TruncatedText = styled.div`
  max-width: 150px; /* 可以根据需要调整宽度 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
`;

const CloseButton = styled(Button)`
  border: none;
  background: transparent;
  color: #666;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  &:hover {
    color: #f56c6c;
    background: rgba(245, 108, 108, 0.1);
  }
`;

export const Toolbar = () => {
  const { sessionId, setOpenHistory, cleanSession, chatList = [], sessions, session_detail_list } = useChatStore();
  const toggleChatPanel = usePanelOpenStore.getState().toggleChatPanel;

  // 处理关闭按钮点击事件
  const handleClose = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发拖拽区域的事件
    e.stopPropagation();
    // 阻止默认行为
    e.preventDefault();
    // 直接关闭面板
    toggleChatPanel(false);
  };

  const sessionTitle = useMemo(() => {
    const currentSession = sessions.find(session => session.sid === sessionId)
    return currentSession?.title || ''
  }, [sessions, sessionId])

  return (
    <ToolbarContainer 
      className="chat-toolbar"
      onClick={(e) => {
        // 阻止工具栏容器的点击事件冒泡
        e.stopPropagation();
      }}
    >
        <div className="chat-title flex-1">
          <span className="chat-title__icon"></span>
          <span className="inline-block truncate max-w-[110px] align-middle">{sessionTitle || 'new Chat'}</span>
        </div>
      {/* </TruncatedText> */}
      <Space className="chat-history__toolbar">
        <Tooltip title="新建聊天">
          <div
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              if (sessionId !== 0) {
                cleanSession();
              }
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
            }}
            className="chat"
          >
          </div>
        </Tooltip>
        <Tooltip title="聊天记录">
          <div 
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setOpenHistory();
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
            }}
            className="open" 
          />
        </Tooltip>
        <Tooltip title="关闭">
          <div className="chat-close"
            onClick={handleClose}
            onMouseDown={(e) => {
              // 阻止鼠标按下事件冒泡，防止触发拖拽开始
              e.stopPropagation();
            }}
          />
        </Tooltip>
      </Space>
    </ToolbarContainer>
  );
};
