import { memo, useEffect, useRef, useState } from "react";
import { Divider, Input, Space, Tooltip } from "antd";
import styled from "styled-components";
import { ReferencePicker } from "./reference-picker";
import { ModelSelector } from "./model-selector";
import { useChatConversation } from "../hooks/use-chat-conversation";
import type { ChatInputProps } from "../types";
import { useChatStore } from "@/store/workerspace-store/chat-store";
import { ChatEditIcon, ChatReloadIcon, EnterIcon } from "@/components/icons";
import { CHAT_INPUT_DIV__TAG } from "..";

const TextAreaCustom = styled(Input.TextArea)`
  resize: none;
  border: none;
  outline: none;
  background-color: transparent;
  box-shadow: none !important;

  &:focus {
    outline: none !important;
  }

  &:hover {
    outline: none !important;
  }

  &.ant-input-focused {
    box-shadow: none !important;
  }
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

// 底部容器样式，用于放置模型选择器和提交按钮
const FooterContainer = styled.div`   
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

// 聊天输入组件，使用memo优化性能
export const ChatInput = memo(
  ({ question, currentRefs, rid, isFooter, inStream = false, children = null }: ChatInputProps) => {
    // 从全局状态获取聊天相关数据和方法
    const {
      footerRefs,
      chatList,
      currentChat,
      currentChatRid,
      sseClient,
      isFetchStream,
      setCurrentChat,
      updateCurrentChat,
      updateChatList,
      setCurrentChatRid,
      // setUserInput,
    } = useChatStore();

    // 获取开始对话的方法
    const { onStartConversation } = useChatConversation();

    // 本地状态：选中的AI模型
    const [selectedModel, setSelectedModel] = useState<string>("gpt-3.5-turbo");

    // 本地状态：新问题文本，初始化为传入的问题或页脚问题
    const [newQuestion, setNewQuestion] = useState<string>(question as string);
    const [showReloadIcon, setShowReloadIcon] = useState<boolean>(true);
    // 添加一个引用来处理点击外部区域
    const chatInputRef = useRef<HTMLDivElement>(null);
    const footerInputRef = useRef<HTMLDivElement>(null);
    const handleBtnRef = useRef<HTMLDivElement>(null);
    const TextAreaRef = useRef<HTMLTextAreaElement>(null);

    useEffect(() => {
      if (rid === currentChatRid) {
        return
      }
      const handleClickOutside = (event: MouseEvent) => {
        // 如果正在获取数据流，不处理点击事件，避免中断流程
        if (isFetchStream) {
          return;
        }

        const chatRootDom = document.getElementById(CHAT_INPUT_DIV__TAG)

        // 判断点击是否在整个chat面板内部
        if (!chatRootDom || !chatRootDom.contains(event?.target as Node)) {
          // 点击在chat面板外部，不处理
          return;
        }

        // 找到当前正在编辑的chat项的DOM元素
        const currentEditingChatItem = document.querySelector(`[data-chat-rid="${currentChatRid}"]`);

        // 如果当前有编辑项，并且点击在编辑项内部，则不处理
        if (currentEditingChatItem && currentEditingChatItem.contains(event?.target as Node)) {
          return;
        }

        // 点击在chat面板内部，但不在当前编辑项内部，取消选中
        setCurrentChatRid(-1);
        setCurrentChat(null);
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, [currentChatRid, rid, isFooter, currentChat, newQuestion, isFetchStream]);

    const handleModelChange = (value: string) => {
      if (isFetchStream) return
      setSelectedModel(value);
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // 如果正在使用输入法（IME），则不处理Enter键
      // 避免在输入中文等时过早提交
      if (e.nativeEvent.isComposing || e.key === "Dead") {
        return;
      }
      // Mac系统 Command + Enter 或 Windows系统 Alt + Enter 换行
      if (e.key === "Enter" && (e.metaKey || e.altKey)) {
        e.preventDefault();
        // 在光标位置插入换行符
        const textarea = e.currentTarget;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const value = textarea.value;
        const newValue =
          value.substring(0, start) + "\n" + value.substring(end);
        setNewQuestion(newValue);
        // 设置光标位置到换行符后
        setTimeout(() => {
          textarea.selectionStart = textarea.selectionEnd = start + 1;
        }, 0);
      }
      // Enter键提交（不按其他修饰键）
      if (e.key === "Enter" && !e.shiftKey && !e.altKey && !e.metaKey) {
        e.preventDefault();
        onBlurUpdateChat(newQuestion);
        if (newQuestion) {
          if (isFetchStream) {
            // 先中断当前的 SSE 连接
            sseClient?.abort();
            // 等待一小段时间后再发起新的请求
            setTimeout(() => {
              onStartConversation(newQuestion, currentChatRid === -1 ? undefined : Number(currentChatRid));
            }, 100);
          } else {
            onStartConversation(newQuestion, currentChatRid === -1 ? undefined : Number(currentChatRid));
          }
          setNewQuestion("");
        }
      }
    };

    // 更新问题文本
    const onChangeQuestion = (value: string) => {
      if (isFetchStream) return
      setShowReloadIcon(false);
      setNewQuestion(value);
    };

    // 失去焦点时更新聊天内容
    const onBlurUpdateChat = (value: string) => {
      // 只在非编辑状态下更新聊天内容
      if (currentChat && showReloadIcon) {
        currentChat.question = value;
        updateChatList(currentChat);
        updateCurrentChat(currentChat);
      }
    };

    if (inStream) {
      return <>
        <div
          data-chat-rid={rid}
          style={{
            transition: "all 0.3s ease",
            position: "relative",
            // border: `${isFooter ? "1px solid #979797" : ""}`,
          }}
          className={`flex-1 bg-white overflow-hidden  p-2.5 text-[#7B86CB]}`}
        >
          <div className="relative rounded-md">
            {/* 引用选择器组件 */}
            <ReferencePicker
              rid={rid}
              currentRefs={currentRefs}
              isFooter={isFooter}
            />
            <div
              className="flex justify-between items-center handle_btn  rounded-[7px] overflow-hidden"
            >
              <div className="text-sm">{question}</div>
            </div>
          </div>
        </div>
        {
          children
        }
      </>
    }

    const active = currentChatRid === rid

    const setActive = () => {
      if (isFetchStream) return
      if (rid) {
        const currentChat = chatList.find((chat) => chat.rid === rid);
        setCurrentChat(currentChat as any)
        setCurrentChatRid(rid);
      }
    }

    const reloadOrReask = (e: React.MouseEvent<HTMLDivElement>) => {
      if (isFetchStream) return
      e.stopPropagation()

      if (rid) {
        const currentChat = chatList.find((chat) => chat.rid === rid);
        setCurrentChat(currentChat as any)
        setCurrentChatRid(rid)
        onStartConversation(newQuestion as string, rid as number);
      }
    }

    return (
      <div
        data-chat-rid={rid}
        style={{
          transition: "all 0.3s ease",
          position: "relative",
          // border: `${active ? (isFooter ? '' : "1px dashed #3367d9") : ""}`, //todo 
          borderRadius: 20,
        }}
        onMouseDown={(e) => {
          // 正在编辑的项，阻止mouseDown事件冒泡
          if (isFetchStream) return
          if (rid === currentChatRid) {
            e.stopPropagation()
          }
        }}
        className={`
            chat-input__item
            rounded-[7px] flex-1 overflow-hidden
            ${active || isFooter ? "border rounded-md overflow-hidden" : " rounded-md text-[#7B86CB]"}
            ${active && !isFooter ? 'active-item' : ''}
          `}
      >

        <div
          onClick={setActive}
          onMouseDown={(e) => e?.stopPropagation()}
          className=" bg-white"
          style={{ borderRadius: 20, }}
        >
          {/* 引用选择器组件 */}
          <ReferencePicker
            rid={rid}
            currentRefs={currentRefs}
            isFooter={isFooter}
            active={active}
          />
          {/* 底部  || 当前输入项目 */}
          {
            (active || isFooter) ?
              <TextAreaCustom
                disabled={isFetchStream}
                ref={TextAreaRef}
                value={newQuestion}
                className={`
                  w-full
                  footer-bg
                  textarea-custom
                  `}
                onChange={(e) => onChangeQuestion(e.target.value)}
                onBlur={(e) => onBlurUpdateChat(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`${isFooter ? "请继续你的提问" : "输入不能为空"}`}
                autoSize={{ minRows: 2, maxRows: 10 }}
              />
              :
              <div className="text-sm p-[10px]">{question}</div>
          }
        </div>

        {/* 非底部输出，1. 在获取焦点需要展示，取消和重新发送  2. 未获取焦点，需要展示重新加载icon*/}
        {!isFooter && (
          <div
            className={`
              flex items-center overflow-hidden footer justify-end pr-[10px] pt-[2px]
              ${active ? 'bg-white' : ''}
            `}
            ref={handleBtnRef}
          >
            {
              active ?
                <>
                  {/* 获取焦点时: 展示取消和重新发送 */}
                  <div
                    className="re-submit" >

                    <div
                      className="re-submit__cancel"
                      onClick={() => {
                        setCurrentChatRid(-1);
                        setNewQuestion(question as string)
                      }}
                    >
                      取消
                    </div>

                    <div
                      className="re-submit__send"
                      onClick={reloadOrReask}
                    >
                      发送
                    </div>
                  </div>
                </>
                :
                <div className="flex items-center operate-hover">
                  <Tooltip title="重新加载" className="h-full ">
                    <div
                      className="reload-icon"
                      onClick={reloadOrReask}
                    />
                  </Tooltip>
                  &nbsp;
                  <div
                    className="flex justify-center items-center rounded-full edit-icon"
                    onClick={setActive}
                  />
                </div>
            }
          </div>
        )}

        {isFooter && (
          <FooterContainer
            ref={footerInputRef}
            className="footer-bg"
          >
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={handleModelChange}
            />
            <div
              className="flex items-center"
              onClick={() => {
                if (isFetchStream) return
                if (newQuestion || footerRefs.length > 0) {
                  if (isFetchStream) {
                    // 先中断当前的 SSE 连接
                    sseClient?.abort();
                    // 等待一小段时间后再发起新的请求
                    setTimeout(() => {
                      onStartConversation(newQuestion);
                    }, 100);
                  } else {
                    onStartConversation(newQuestion);
                  }
                  setNewQuestion("");
                }
              }}
            >
              <div
                className={`
                  ${((newQuestion && newQuestion.trim().length > 0) || !!footerRefs.length)
                    ? "cursor-pointer"
                    : "text-gray-400 cursor-not-allowed opacity-40"
                  } text-[13px] submit-icon`}
              >
              </div>
            </div>
          </FooterContainer>
        )}
      </div>
    );
  }
);
