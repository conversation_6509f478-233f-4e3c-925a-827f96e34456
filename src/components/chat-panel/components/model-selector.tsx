import { Select } from "antd";
import styled from "styled-components";
const { Option } = Select;

const ModelSelectContainer = styled.div`
  flex: 1;
`;

const models = [
  { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
  { value: "gpt-4", label: "GPT-4" },
  { value: "custom-model", label: "自定义模型" },
];

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (value: string) => void;
}

export const ModelSelector = ({ selectedModel, onModelChange }: ModelSelectorProps) => {
  return (
    <ModelSelectContainer>
      <Select
        value={selectedModel}
        onChange={onModelChange}
        className="text=[12px]"
      >
        {models.map((model) => (
          <Option key={model.value} value={model.value}>
            {model.label}
          </Option>
        ))}
      </Select>
    </ModelSelectContainer>
  );
}; 