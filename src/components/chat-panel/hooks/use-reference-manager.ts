import {useEffect} from "react";

/**
 * 导入相关的状态管理存储
 * - useChatStore: 聊天相关状态管理
 * - usePdfStore: PDF文档相关状态管理
 * - useFlowStore: 流程图节点相关状态管理
 */
import {useChatStore} from "@/store/workerspace-store/chat-store";
import {pdfSingleState, usePdfStore} from "@/store/pdf-store";
import type {CustomNode} from "@/store/flow-store";
// import {useFlowStore} from "@/store/workerspace-store/flow-store";
import {PdfRef} from "@/components/chat-panel/types";
import { useFlowStore } from "@/store/flow-store";

/**
 * 引用管理自定义Hook
 * 
 * 该Hook负责管理聊天中的引用资源，包括PDF文档和流程图节点
 * 当PDF列表或节点列表发生变化时，自动更新聊天中的引用数据
 * 
 * @returns 无直接返回值，通过状态管理更新全局引用数据
 */
export const useReferenceManager = () => {
  // 从聊天状态管理中获取设置引用的方法
  const { setPdfRefs, setNodeRefs } = useChatStore();
  // 从PDF状态管理中获取所有PDF文档
  const { pdfs } = usePdfStore();
  // 从自定义 flow-store 获取节点（具备严格的 nodeData 类型）
  const nodes = useFlowStore((state) => state.nodes);

  /**
   * 处理创建搜索引用的函数
   * 
   * 将原始的PDF和节点数据转换为聊天引用格式，并更新到全局状态
   * 
   * @param nodes - 流程图节点列表
   * @param pdfs - PDF文档列表
   */
  const handleCreatesSearchRefs = async (
    nodes: CustomNode[],
    pdfs: Map<string, pdfSingleState>
  ) => {
    // 处理PDF引用 - 如果有PDF文档
      if (pdfs.size > 0) {
          // 将PDF文档转换为引用格式
          let pdfRefs: PdfRef[] = [];
          pdfs.forEach((pdf) => (
              pdfRefs.push({
                  id: pdf.aid,         // PDF的唯一标识
                  fileName: pdf.filename, // PDF文件名
                  url: pdf.url,        // PDF的URL
              })
          ));
          // 更新全局PDF引用列表
          setPdfRefs(pdfRefs);
      }
    
    // 处理节点引用 - 如果有节点且内容不为空
    if (nodes.length > 0) {
      // 过滤出有内容的节点并转换为引用格式
      const nodeRefs = nodes
        .filter((item) => item.data.content !== "")
        .map((node) => ({
          id: node.id,       // 节点的唯一标识
          title: node.data.title ? node.data.title + ".node" : "未命名.node", // 节点标题
          content: node.data.content || "", // 节点内容
        }));
      // 更新全局节点引用列表
      setNodeRefs(nodeRefs);
    }
  };

  /**
   * 副作用：监听PDF和节点数据变化
   * 
   * 当PDF列表或节点列表发生变化时，自动更新引用数据
   * 确保聊天中的引用始终与最新的资源保持同步
   */
  useEffect(() => {
    if (pdfs || nodes) {
      handleCreatesSearchRefs(nodes, pdfs);
    }
  }, [pdfs, nodes]); // 依赖项：PDF列表和节点列表
}; 