@import "../../common/icon.less";

.chat-content {
    background-color: @bg-item;

    &__empty {
        position: relative;
        justify-content: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        // max-width: 389px;
        padding: 10px 20px;

        &--img {
            background-image: url("@/assets/icons/empty.svg");
            background-size: contain;
            background-repeat: no-repeat;
            max-width: 236px;
            height: 157px;
            min-width: 236px;
            position: relative;

            .img-title {
                position: absolute;
                bottom: -30px;
                left: 50%;
                transform: translateX(-50%);
                text-align: center;
                background-image: url("@/assets/icons/empty-title.svg");
                background-size: contain;
                background-repeat: no-repeat;
                max-width: 306px;
                min-width: 110%;
                height: 45px;
            }
        }

        &--des {
            font-size: 12px;
            color: @font-color80;
            text-align: center;


            >div {
                padding: 5px;
                box-sizing: border-box;
            }

            .weight {
                font-weight: 400;
                color: @font-color;
            }
        }

        &--bimg {
            background-image: url("@/assets/icons/empty-b-icon.svg");
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            width: 134px;
            height: 134px;
        }
    }

    .chat-input__item {
        background-color: #F0F0FA;

        .footer-bg {
            background-color: #fff;
            padding: 10px 10px;
            box-sizing: border-box;
        }

        .textarea-custom {
            min-height: 100px;
            resize: none;
        }

        .ant-input {
            border-radius: 15px 15px 0 0;
        }
    }
}

.preview-warp {
    background-color: @bg-item;
}

.cus-tag {
    display: flex;
    background-color: @bg-item;
    margin: 0 4px;
    height: 18px;
    align-items: center;
    border-radius: 12px;
    max-width: 120px;
    min-width: 30px;
    padding: 0 5px;
    font-size: 8px;

    .title {
        flex: 1;
        max-width: 100px;
        padding: 0 5px;
        text-overflow: ellipsis;
    }

    .icon {
        width: 14px;
        height: 14px;
    }
}

.re-submit {
    display: flex;
    width: 100px;
    border-radius: 7.56px;
    align-items: center;
    justify-content: space-between;
    background-color: @bg-color;
    padding: 5px;
    font-size: 12px;

    .re-submit__send {
        background-color: @bg-btn;
        color: @font-color-w;
        padding: 3px 5px;
        border-radius: 5.4px;
    }

    .re-submit__cancel {
        color: @font-color-cancel;
    }
}