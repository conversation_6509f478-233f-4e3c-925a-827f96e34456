import {WorkerSpaceWrapper} from "./pages/workspace";
import {Login} from "./pages/login";
import {Home} from "./pages/home";
import {Register} from "./pages/register";
import {NotFound} from "./pages/not-found";
import {BrowserRouter, Route, Routes} from "react-router-dom";
import {Toaster} from "sonner";
import {TooltipProvider} from "@/components/ui/tooltip";
import { ToolsPage } from "./pages/tools";
import { TagManagementPage } from "./pages/tag-management";
import { CardListPage } from "./pages/card-list";

function App() {
    // 获取基础路径，用于GitHub Pages部署
    // const basename = (import.meta.env as any).BASE_URL || '/';

    return (
            <BrowserRouter
                // basename={basename}
                future={{
                    v7_startTransition: true,
                    v7_relativeSplatPath: true,
                }}
            >
                <TooltipProvider>
                    <div className="app" style={{userSelect: 'none'}}>
                        <Routes>
                            <Route path="/" element={<Home/>}/>
                            <Route path="/workerspace" element={<WorkerSpaceWrapper/>}/>
                            <Route path="/login" element={<Login/>}/>
                            <Route path="/home" element={<Home/>}/>
                            <Route path="/register" element={<Register/>}/>
                            <Route path="/tools" element={<ToolsPage/>}/>
                            <Route path="/tag-management" element={<TagManagementPage/>}/>
                            <Route path="/card-list" element={<CardListPage/>}/>
                            <Route path="*" element={<NotFound/>}/>
                        </Routes>
                        <Toaster/>
                    </div>
                </TooltipProvider>
            </BrowserRouter>
    );
}

export default App;
