@import "./reset.css";
@import "./custom.css";

@tailwind base;
@tailwind components;
@tailwind utilities;



@layer base {
  :root {
    /* === 核心设计令牌 (统一使用 HSL 格式) === */
    /* 背景色系 */
    --background: hsl(210, 33%, 98%);
    --foreground: hsl(241, 54%, 15%);
    
    /* 卡片色系 */
    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(241, 54%, 15%);
    
    /* 弹出层色系 */
    --popover: hsl(0, 0%, 100%);
    --popover-foreground: hsl(241, 54%, 15%);
    
    /* 主色调 */
    --primary: hsl(241, 79%, 68%);
    --primary-foreground: hsl(0, 0%, 100%);
    
    /* 次要色 */
    --secondary: hsl(241, 79%, 95%);
    --secondary-foreground: hsl(241, 79%, 68%);
    
    /* 静音色 */
    --muted: hsla(241, 79%, 68%, 0.1);
    --muted-foreground: hsl(0, 0%, 60%);
    
    /* 强调色 */
    --accent: hsl(237, 25%, 89%);
    --accent-foreground: hsl(241, 54%, 15%);
    
    /* 危险色 */
    --destructive: hsl(0, 84%, 60%);
    --destructive-foreground: hsl(0, 0%, 98%);
    
    /* 边框和输入 */
    --border: hsl(220, 30%, 85%);
    --input: hsl(220, 30%, 85%);
    --ring: hsl(241, 79%, 68%);
    
    /* 图表颜色 */
    --chart-1: hsl(12, 76%, 61%);
    --chart-2: hsl(173, 58%, 39%);
    --chart-3: hsl(197, 37%, 24%);
    --chart-4: hsl(43, 74%, 66%);
    --chart-5: hsl(27, 87%, 67%);
    
    /* 圆角 */
    --radius: 0.5rem;

    /* 下面的变量需要被逐步替代和删除 */
    /* 文字颜色 */
    --text-primary: var(--foreground); /* 主要文字 */
    --text-secondary: var(--secondary-foreground); /* 次要文字 */
    --text-disabled: var(--muted-foreground); /* 禁用文字 */
    --text-form-label: var(--secondary-foreground); /* 表单标签文字 */
    --text-icon: var(--primary);

    /* 边框颜色 */
    --border-color: var(--border); /* 边框色 */
    --border-color-2: var(--border);
    /* 背景色 */
    --bg-primary: var(--background); /* 主背景 */
    --bg-secondary: var(--secondary); /* 次要背景 */
    --bg-logo-color: var(--primary); /* 主logo背景色 */
    --bg-category-tabs: var(--muted); /* 分类tabs背景色 */
    --bg-category-tabs-hover: var(--accent); /* 分类tabs背景色 */
    --bg-search-hover: var(--secondary);
    --bg-upload: var(--muted);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-size: 14px;
  }

  body {
    @apply bg-background text-foreground;
  }
}
