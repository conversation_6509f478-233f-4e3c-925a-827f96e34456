import { useEffect, RefObject } from 'react';

interface UsePreventBrowserZoomOptions {
  /**
   * 元素引用
   */
  elementRef: RefObject<HTMLElement | null> | null ;
  
  /**
   * 是否启用禁用功能
   */
  enabled?: boolean;
  
  /**
   * 是否阻止滚轮事件（默认只阻止缩放）
   */
  preventWheel?: boolean;
  
  /**
   * 是否阻止键盘事件（默认只阻止缩放快捷键）
   */
  preventKeyboard?: boolean;
  
  /**
   * 自定义要阻止的按键组合
   */
  customKeys?: string[];
}

/**
 * 通用的浏览器缩放和滚动禁用 Hook
 * 
 * @param options 配置选项
 * 
 * @example
 * ```tsx
 * const nodeRef = useRef<HTMLDivElement>(null);
 * 
 * // 基础用法：只在选中时禁用缩放
 * usePreventBrowserZoom({
 *   elementRef: nodeRef,
 *   enabled: selected
 * });
 * 
 * // 高级用法：禁用所有滚轮和键盘事件
 * usePreventBrowserZoom({
 *   elementRef: nodeRef,
 *   enabled: true,
 *   preventWheel: true,
 *   preventKeyboard: true,
 *   customKeys: ['Enter', 'Space'] // 额外阻止的按键
 * });
 * ```
 */
export const usePreventBrowserZoom = ({
  elementRef,
  enabled = true,
  preventWheel = false,
  preventKeyboard = false,
  customKeys = []
}: UsePreventBrowserZoomOptions) => {
  useEffect(() => {
    if (!enabled) return;

    const onWheel = (e: WheelEvent) => {
      if (preventWheel) {
        // 阻止所有滚轮事件
        e.preventDefault();
        e.stopPropagation();
      } else {
        // 只阻止缩放手势，允许正常滚动
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    };

    const onKeyDown = (e: KeyboardEvent) => {
      if (preventKeyboard) {
        // 阻止所有键盘事件
        e.preventDefault();
        e.stopPropagation();
      } else {
        // 默认的缩放快捷键
        const zoomKeys = ['+', '-', '=', '0'];
        const allBlockedKeys = [...zoomKeys, ...customKeys];
        
        // 阻止键盘缩放快捷键和自定义按键
        if ((e.ctrlKey || e.metaKey) && allBlockedKeys.includes(e.key)) {
          e.preventDefault();
          e.stopPropagation();
        }
        
        // 阻止自定义按键（不需要修饰键）
        if (customKeys.includes(e.key)) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    };

    if(!elementRef?.current) return
    const element = elementRef.current;
    if (element) {
      element.addEventListener('wheel', onWheel, { passive: false });
      element.addEventListener('keydown', onKeyDown);

      return () => {
        element.removeEventListener('wheel', onWheel);
        element.removeEventListener('keydown', onKeyDown);
      };
    }
  }, [enabled, preventWheel, preventKeyboard, customKeys, elementRef]);
};

/**
 * 预设配置的便捷 Hooks
 */

/**
 * 只禁用浏览器缩放（推荐用于大多数场景）
 */
export const usePreventZoomOnly = (
  elementRef: RefObject<HTMLElement | null>| null , 
  enabled: boolean = true
) => {
  return usePreventBrowserZoom({
    elementRef,
    enabled,
    preventWheel: false,
    preventKeyboard: false
  });
};

/**
 * 禁用所有滚轮和键盘事件（用于特殊交互场景）
 */
export const usePreventAllEvents = (
  elementRef: RefObject<HTMLElement | null>, 
  enabled: boolean = true
) => {
  return usePreventBrowserZoom({
    elementRef,
    enabled,
    preventWheel: true,
    preventKeyboard: true
  });
};

/**
 * 自定义禁用特定按键
 */
export const usePreventCustomKeys = (
  elementRef: RefObject<HTMLElement | null>, 
  keys: string[], 
  enabled: boolean = true
) => {
  return usePreventBrowserZoom({
    elementRef,
    enabled,
    customKeys: keys
  });
};