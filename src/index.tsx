import "./wdyr";
import ReactDOM from "react-dom/client";
import "./styles/index.css";
import App from "./App";
import {getDatabase} from "@/local";
import "./common/message.ts"

// 抑制特定警告
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
// 处理 console.error
console.error = (...args: any[]) => {
  const suppressedWarnings = [
    "findDOMNode",
    "DOMNodeInserted",
    "ReactDOM.findDOMNode",
  ];

  if (
    typeof args[0] === "string" &&
    suppressedWarnings.some((warning) => args[0].includes(warning))
  ) {
    return;
  }
  originalConsoleError.apply(console, args);
};

// 处理 console.warn
console.warn = (...args: any[]) => {
  const suppressedWarnings = ["DOMNodeInserted", "mutation event"];

  if (
    typeof args[0] === "string" &&
    suppressedWarnings.some((warning) => args[0].includes(warning))
  ) {
    return;
  }
  originalConsoleWarn.apply(console, args);
};

// 初始化数据库 - 使用立即执行的异步函数避免top-level await
(async () => {
  await getDatabase().catch(console.error);

  const root = ReactDOM.createRoot(
    document.getElementById("root") as HTMLElement
  );

  root.render(<App />);
})();
