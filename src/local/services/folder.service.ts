import { getDatabase } from '../db';
import { generateId } from '@/local';
import { FolderDocType } from '../db/schemas/folder';

/**
 * 文件夹和文件管理服务
 */
export interface CreateFolderReq {
    wid: string;
    file_name: string;
    parent_id?: string;
    order?: number;
    type?: string
}

export interface UpdateFolderReq {
    id: string;
    file_name?: string;
    parent_id?: string;
    order?: number;
}

export interface GetFolderListReq {
    wid: string;
    parent_id?: string;
    type?: 'file' | 'folder';
}

export interface MoveFolderReq {
    id: string;
    new_parent_id: string;
    new_order?: number;
}

export interface CreateFileReq {
    wid: string;
    file_name: string;
    parent_id?: string;
    order?: number;
    file_type?: string;
    file_size?: number;
    file_path?: string;
    type?: string;
    mime_type?: string;
}

export interface UpdateFileReq {
    id: string;
    file_name?: string;
    parent_id?: string;
    order?: number;
    file_type?: string;
    file_size?: number;
    file_path?: string;
}

export class FolderService {

    /**
     * 创建文件夹
     */
    async createFolder(req: CreateFolderReq): Promise<FolderDocType> {
        try {
            const db = await getDatabase();
            const rootId = req.wid + '_root'
            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            // 验证工作区是否存在
            const workspace = await db.workspaces.findOne({
                selector: { id: req.wid }
            }).exec();

            if (!workspace) {
                throw new Error('工作区不存在');
            }

            await this.ensureRootFolderExists(req.wid)
            // 验证父文件夹是否存在（如果不是root）
            const parent_id = req.parent_id || rootId;
            if (parent_id) {
                const parentFolder = await db.folders.findOne({
                    selector: {
                        id: parent_id,
                        type: 'folder',
                        wid: req.wid
                    }
                }).exec();

                if (!parentFolder) {
                    throw new Error(`父文件夹不存在${parent_id}`);
                }
            }

            // 获取父文件夹下的最大order值
            const maxOrder = await this.getMaxOrderInParent(req.wid, parent_id);
            const order = req.order !== undefined ? req.order : maxOrder + 1;

            const now = Math.floor(Date.now() / 1000);
            const folderId = generateId();

            const folderData: FolderDocType = {
                id: folderId,
                wid: req.wid,
                file_name: req.file_name,
                type: 'folder',
                parent_id,
                order,
                create_at: now,
                update_at: now,
                children: []
            };

            console.log('📁 创建文件夹:', {
                id: folderId,
                name: req.file_name,
                parent_id,
                wid: req.wid,
                order,
                timestamp: new Date().toLocaleString()
            });

            const folder = await db.folders.insert(folderData);

            // 更新父文件夹的children数组（包括根文件夹）
            await this.updateParentChildren(parent_id, folderId, 'add');

            return folder.toJSON();
        } catch (error) {
            console.error('❌ 创建文件夹失败:', error);
            throw error;
        }
    }

    /**
     * 创建文件
     */
    async createFile(req: CreateFileReq): Promise<FolderDocType> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            // 验证工作区是否存在
            const workspace = await db.workspaces.findOne({
                selector: { id: req.wid }
            }).exec();

            if (!workspace) {
                throw new Error('工作区不存在');
            }

            await this.ensureRootFolderExists(req.wid)

            // 验证父文件夹是否存在（如果不是root）
            const parent_id = req.parent_id || req.wid + '_root';
            const parentFolder = await db.folders.findOne({
                selector: {
                    id: parent_id,
                    type: 'folder',
                    wid: req.wid
                }
            }).exec();

            if (!parentFolder) {
                throw new Error(`父文件夹不存在${parent_id}`);
            }

            const now = Math.floor(Date.now() / 1000);
            const fileId = generateId();

            // 2. 在attachments表中创建对应记录
            const attachmentData = {
                id: generateId(),
                wid: req.wid,
                parent_id: parent_id,
                file_name: req.file_name,
                type: 'file',
                mime_type: req?.mime_type || 'notebook',
                order: 0,
                create_at: now,
                update_at: now
            };
            const file = await db.attachments.insert(attachmentData);

            console.log('📎 创建文件(attachments表):', {
                file_id: fileId,
                name: req.file_name,
                timestamp: new Date().toLocaleString()
            });

            await this.updateParentChildren(parent_id, fileId, 'add');

            return file.toJSON();
        } catch (error) {
            console.error('❌ 创建文件失败:', error);
            throw error;
        }
    }

    /**
     * 更新文件夹信息
     */
    async updateFolder(req: UpdateFolderReq): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const folder = await db.folders.findOne({
                selector: { id: req.id }
            }).exec();

            if (!folder) {
                throw new Error('文件夹不存在');
            }

            const oldData = folder.toJSON();
            const now = Math.floor(Date.now() / 1000);
            const updateData: Partial<FolderDocType> = {
                update_at: now
            };

            // 处理重命名
            if (req.file_name && req.file_name !== folder.file_name) {
                updateData.file_name = req.file_name;
            }

            // 处理移动文件夹
            if (req.parent_id && req.parent_id !== folder.parent_id) {
                // 验证新父文件夹是否存在（根文件夹不需要验证）
                if (!req.parent_id.endsWith('_root')) {
                    const newParentFolder = await db.folders.findOne({
                        selector: {
                            id: req.parent_id,
                            type: 'folder',
                            wid: folder.wid
                        }
                    }).exec();

                    if (!newParentFolder) {
                        throw new Error('新父文件夹不存在');
                    }

                    // 检查是否会造成循环引用
                    if (await this.wouldCreateCycle(req.id, req.parent_id)) {
                        throw new Error('不能将文件夹移动到其子文件夹中');
                    }
                }

                // 从旧父文件夹中移除
                await this.updateParentChildren(folder.parent_id, req.id, 'remove');

                updateData.parent_id = req.parent_id;

                // 获取新位置的order
                if (req.order !== undefined) {
                    updateData.order = req.order;
                } else {
                    const maxOrder = await this.getMaxOrderInParent(folder.wid, req.parent_id);
                    updateData.order = maxOrder + 1;
                }

                // 添加到新父文件夹
                await this.updateParentChildren(req.parent_id, req.id, 'add');
            } else if (req.order !== undefined && req.order !== folder.order) {
                updateData.order = req.order;
            }

            await folder.update({ $set: updateData });

            console.log('📝 更新文件夹:', {
                id: req.id,
                oldName: oldData.file_name,
                newName: updateData.file_name || oldData.file_name,
                oldParent: oldData.parent_id,
                newParent: updateData.parent_id || oldData.parent_id,
                changes: Object.keys(updateData).filter(key => key !== 'update_at'),
                timestamp: new Date().toLocaleString()
            });

        } catch (error) {
            console.error('❌ 更新文件夹失败:', error);
            throw error;
        }
    }

    /**
     * 更新文件信息
     */
    async updateFile(req: UpdateFileReq): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const file = await db.attachments.findOne({
                selector: { id: req.id, type: 'file' }
            }).exec();

            if (!file) {
                throw new Error('文件不存在');
            }

            const oldData = file.toJSON();
            const now = Math.floor(Date.now() / 1000);
            const updateData: Partial<FolderDocType> = {
                update_at: now
            };

            // 处理重命名
            if (req.file_name && req.file_name !== file.file_name) {
                updateData.file_name = req.file_name;
            }


            // 处理文件路径更新
            if (req.file_path !== undefined) {
                updateData.file_path = req.file_path;
            }

            // 处理移动文件
            if (req.parent_id && req.parent_id !== file.parent_id) {
                // 验证新父文件夹是否存在（根文件夹不需要验证）
                if (!req.parent_id.endsWith('_root')) {
                    const newParentFolder = await db.folders.findOne({
                        selector: {
                            id: req.parent_id,
                            type: 'folder',
                            wid: file.wid
                        }
                    }).exec();

                    if (!newParentFolder) {
                        throw new Error('新父文件夹不存在');
                    }
                }

                // 从旧父文件夹中移除
                await this.updateParentChildren(file.parent_id, req.id, 'remove');

                updateData.parent_id = req.parent_id;

                // 获取新位置的order
                if (req.order !== undefined) {
                    updateData.order = req.order;
                } else {
                    const maxOrder = await this.getMaxOrderInParent(file.wid, req.parent_id);
                    updateData.order = maxOrder + 1;
                }

                // 添加到新父文件夹
                await this.updateParentChildren(req.parent_id, req.id, 'add');
            } else if (req.order !== undefined && req.order !== file.order) {
                updateData.order = req.order;
            }

            await file.update({ $set: updateData });

            console.log('📝 更新文件:', {
                id: req.id,
                oldName: oldData.file_name,
                newName: updateData.file_name || oldData.file_name,
                oldParent: oldData.parent_id,
                newParent: updateData.parent_id || oldData.parent_id,
                changes: Object.keys(updateData).filter(key => key !== 'update_at'),
                timestamp: new Date().toLocaleString()
            });

        } catch (error) {
            console.error('❌ 更新文件失败:', error);
            throw error;
        }
    }

    //     /**
    //      * 删除文件夹（递归删除所有子文件夹和文件）
    //      */
    /**
     * 删除文件夹
     */
    async deleteFolder(id: string): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const folder = await db.folders.findOne({
                selector: { id, type: 'folder' }
            }).exec();

            if (!folder) {
                throw new Error('文件夹不存在');
            }

            const folderData = folder.toJSON();

            console.log('🗑️ 开始删除文件夹:', {
                id,
                name: folderData.file_name,
                type: folderData.type,
                timestamp: new Date().toLocaleString()
            });

            // 递归删除所有子文件和子文件夹
            // 1. 查找子文件夹
            const childFolders = await db.folders.find({
                selector: {
                    parent_id: id,
                    wid: folderData.wid,
                    type: 'folder'
                }
            }).exec();

            // 2. 查找子文件
            const childFiles = await db.attachments.find({
                selector: {
                    parent_id: id,
                    wid: folderData.wid,
                    type: 'file'
                }
            }).exec();

            const totalChildren = childFolders.length + childFiles.length;
            
            if (totalChildren > 0) {
                console.log(`🔄 文件夹 ${folderData.file_name} 包含 ${totalChildren} 个子项目 (${childFolders.length} 个文件夹, ${childFiles.length} 个文件)，开始递归删除`);
                
                // 先删除所有子文件夹 (不更新父文件夹children，避免冲突)
                for (const childFolder of childFolders) {
                    const childData = childFolder.toJSON();
                    try {
                        await this.deleteFolderWithoutUpdatingParent(childData.id);
                        // 给每个删除操作一点时间间隔，避免RxDB文档更新冲突
                        await new Promise(resolve => setTimeout(resolve, 50));
                    } catch (error) {
                        console.error(`删除子文件夹失败: ${childData.file_name}`, error);
                        // 继续删除其他项目，不要因为一个失败就停止
                    }
                }
                
                // 然后删除所有子文件 (不更新父文件夹children，避免冲突)
                for (const childFile of childFiles) {
                    const childData = childFile.toJSON();
                    try {
                        await this.deleteFileWithoutUpdatingParent(childData.id);
                        // 给每个删除操作一点时间间隔，避免RxDB文档更新冲突
                        await new Promise(resolve => setTimeout(resolve, 50));
                    } catch (error) {
                        console.error(`删除子文件失败: ${childData.file_name}`, error);
                        // 继续删除其他项目，不要因为一个失败就停止
                    }
                }
            }

            // 从父文件夹的children中移除
            await this.updateParentChildren(folderData.parent_id, id, 'remove');

            // 删除文件夹本身
            await folder.remove();

            console.log('✅ 文件夹删除完成:', {
                id,
                name: folderData.file_name,
                timestamp: new Date().toLocaleString()
            });

        } catch (error) {
            console.error('❌ 删除文件夹失败:', error);
            throw error;
        }
    }

    /**
     * 删除文件
     */
    /**
     * 删除文件夹但不更新父文件夹的children数组（用于递归删除避免冲突）
     */
    private async deleteFolderWithoutUpdatingParent(id: string): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const folder = await db.folders.findOne({
                selector: { id, type: 'folder' }
            }).exec();

            if (!folder) {
                console.warn(`文件夹不存在: ${id}`);
                return;
            }

            const folderData = folder.toJSON();

            console.log('🗑️ 删除子文件夹:', {
                id,
                name: folderData.file_name,
                type: folderData.type
            });

            // 递归删除子项目 (复用相同逻辑)
            const childFolders = await db.folders.find({
                selector: {
                    parent_id: id,
                    wid: folderData.wid,
                    type: 'folder'
                }
            }).exec();

            const childFiles = await db.attachments.find({
                selector: {
                    parent_id: id,
                    wid: folderData.wid,
                    type: 'file'
                }
            }).exec();

            // 删除子文件夹
            for (const childFolder of childFolders) {
                const childData = childFolder.toJSON();
                try {
                    await this.deleteFolderWithoutUpdatingParent(childData.id);
                    await new Promise(resolve => setTimeout(resolve, 30));
                } catch (error) {
                    console.error(`删除子文件夹失败: ${childData.file_name}`, error);
                }
            }

            // 删除子文件
            for (const childFile of childFiles) {
                const childData = childFile.toJSON();
                try {
                    await this.deleteFileWithoutUpdatingParent(childData.id);
                    await new Promise(resolve => setTimeout(resolve, 30));
                } catch (error) {
                    console.error(`删除子文件失败: ${childData.file_name}`, error);
                }
            }

            // 直接删除文件夹本身，不更新父文件夹children
            await folder.remove();

            console.log('✅ 子文件夹删除完成:', folderData.file_name);

        } catch (error) {
            console.error('删除文件夹失败:', error);
            throw error;
        }
    }

    /**
     * 删除文件但不更新父文件夹的children数组（用于递归删除避免冲突）
     */
    private async deleteFileWithoutUpdatingParent(id: string): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const file = await db.attachments.findOne({
                selector: { id, type: 'file' }
            }).exec();

            if (!file) {
                console.warn(`文件不存在: ${id}`);
                return;
            }

            const fileData = file.toJSON();

            console.log('🗑️ 删除子文件:', {
                id,
                name: fileData.file_name,
                type: fileData.file_type,
                parent_id: fileData.parent_id
            });

            // 直接删除文件本身，不更新父文件夹children
            await file.remove();

            console.log('✅ 子文件删除完成:', fileData.file_name);

        } catch (error) {
            console.error('删除文件失败:', error);
            throw error;
        }
    }

    async deleteFile(id: string): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const file = await db.attachments.findOne({
                selector: { id, type: 'file' }
            }).exec();

            if (!file) {
                throw new Error('文件不存在');
            }

            const fileData = file.toJSON();

            console.log('🗑️ 开始删除文件:', {
                id,
                name: fileData.file_name,
                type: fileData.file_type,
                parent_id: fileData.parent_id,
                timestamp: new Date().toLocaleString()
            });

            // 从父文件夹的children中移除
            await this.updateParentChildren(fileData.parent_id, id, 'remove');

            // 删除文件本身
            await file.remove();

            console.log('✅ 文件删除完成:', {
                id,
                name: fileData.file_name,
                timestamp: new Date().toLocaleString()
            });

        } catch (error) {
            console.error('❌ 删除文件失败:', error);
            throw error;
        }
    }

    /**
     * 获取文件夹列表
     */
    async getFolderList(req: GetFolderListReq): Promise<FolderDocType[]> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                console.error('数据库或folders集合未初始化');
                return [];
            }

            const selector: any = {
                wid: req.wid
            };

            if (req.parent_id !== undefined) {
                selector.parent_id = req.parent_id;
            }

            if (req.type) {
                selector.type = req.type;
            }

            const folders = await db.folders.find({
                selector,
            }).exec();

            return folders.map((folder: { toJSON: () => any; }) => folder.toJSON());

        } catch (error) {
            console.error('❌ 获取文件夹列表失败:', error);
            return [];
        }
    }

    /**
     * 根据ID获取文件夹详情
     */
    async getFolderById(id: string): Promise<FolderDocType | null> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const folder = await db.folders.findOne({
                selector: { id }
            }).exec();

            return folder ? folder.toJSON() : null;

        } catch (error) {
            console.error('❌ 获取文件夹详情失败:', error);
            return null;
        }
    }

    async updateFileInfo({ id, content }: { id: string, content: string }) {
        try {
            const db = await getDatabase();

            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const file = await db.attachments.findOne({
                selector: { id, mime_type: 'notebook' }
            }).exec();

            if (!file) {
                throw new Error('文件不存在');
            }

            const now = Math.floor(Date.now() / 1000);
            
            // 更新 attachments 表中的文件内容
            await file.update({
                $set: {
                    content: content,
                    update_at: now
                }
            });

            console.log('📝 更新文件内容:', {
                id: id,
                fileName: file.file_name,
                contentLength: content.length,
                timestamp: new Date().toLocaleString()
            });

            return file.toJSON();

        } catch (error) {
            console.error('❌ 更新文件内容失败:', error);
            throw error;
        }
    }
    /**
     * 根据ID获取文件详情
     */
    async getFileById(id: string): Promise<FolderDocType | null> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const file = await db.attachments.findOne({
                selector: { id, mime_type: 'notebook' }
            }).exec();

            return file ? file.toJSON() : null;

        } catch (error) {
            console.error('❌ 获取文件详情失败:', error);
            return null;
        }
    }

    /**
     * 移动文件夹
     */
    async moveFolder(req: MoveFolderReq): Promise<void> {
        return this.updateFolder({
            id: req.id,
            parent_id: req.new_parent_id,
            order: req.new_order
        });
    }

    /**
     * 检查并创建根文件夹结构
     */
    async checkRootAndCreate(wid: string): Promise<void> {
       await this.ensureRootFolderExists(wid)
    }

    /**
     * 获取文件夹树结构
     */
    async getFolderTree(wid: string, parent_id: string = 'root'): Promise<FolderDocType[]> {
        try {
            const folders = await this.getFolderList({ wid, parent_id });

            // 递归获取子文件夹
            for (const folder of folders) {
                if (folder.type === 'folder') {
                    const children = await this.getFolderTree(wid, folder.id);
                    (folder as any).children = children;
                }
            }

            return folders;

        } catch (error) {
            console.error('❌ 获取文件夹树结构失败:', error);
            return [];
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 获取指定父文件夹下的最大order值
     */
    private async getMaxOrderInParent(wid: string, parent_id: string): Promise<number> {
        try {
            const db = await getDatabase();

            const items = await db.folders.find({
                selector: {
                    wid,
                    parent_id
                },
                sort: [{ order: 'desc' }],
                limit: 1
            }).exec();

            return items.length > 0 ? (items[0].order || 0) : 0;

        } catch (error) {
            console.error('获取最大order值失败:', error);
            return 0;
        }
    }

    /**
     * 更新父文件夹的children数组
     */
    private async updateParentChildren(parent_id: string, child_id: string, action: 'add' | 'remove'): Promise<void> {
        try {
            const db = await getDatabase();

            let parentFolder = await db.folders.findOne({
                selector: { id: parent_id }
            }).exec();

            if (!parentFolder) {
                console.warn('父文件夹不存在:', parent_id);
                return;
            }

            const currentChildren = parentFolder.children || [];
            let updatedChildren: string[];

            if (action === 'add') {
                if (!currentChildren.includes(child_id)) {
                    updatedChildren = [...currentChildren, child_id];
                } else {
                    return; // 已存在，无需更新
                }
            } else {
                updatedChildren = currentChildren.filter((id: string) => id !== child_id);
            }

            // 简化的重试机制处理偶尔的并发更新冲突
            try {
                await parentFolder.update({
                    $set: {
                        children: updatedChildren,
                        update_at: Math.floor(Date.now() / 1000)
                    }
                });
            } catch (error: any) {
                // 如果是文档更新冲突，尝试一次重试
                if (error.message && error.message.includes('Document update conflict')) {
                    console.log('更新父文件夹children发生冲突，尝试重试...');
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    // 重新获取最新数据并重试一次
                    const refreshedParent = await db.folders.findOne({
                        selector: { id: parent_id }
                    }).exec();
                    
                    if (refreshedParent) {
                        const refreshedChildren = refreshedParent.children || [];
                        const finalChildren = action === 'add' 
                            ? (refreshedChildren.includes(child_id) ? refreshedChildren : [...refreshedChildren, child_id])
                            : refreshedChildren.filter((id: string) => id !== child_id);
                        
                        await refreshedParent.update({
                            $set: {
                                children: finalChildren,
                                update_at: Math.floor(Date.now() / 1000)
                            }
                        });
                    }
                } else {
                    throw error; // 非冲突错误，直接抛出
                }
            }

        } catch (error) {
            console.error('更新父文件夹children失败:', error);
            throw error;
        }
    }

    /**
     * 检查是否会创建循环引用
     */
    private async wouldCreateCycle(folder_id: string, new_parent_id: string): Promise<boolean> {
        try {
            const db = await getDatabase();

            let currentParentId = new_parent_id;
            const visited = new Set<string>();

            while (currentParentId && currentParentId !== 'root') {
                if (currentParentId === folder_id) {
                    return true; // 发现循环
                }

                if (visited.has(currentParentId)) {
                    break; // 防止无限循环
                }
                visited.add(currentParentId);

                const parent = await db.folders.findOne({
                    selector: { id: currentParentId }
                }).exec();

                if (!parent) {
                    break;
                }

                currentParentId = parent.parent_id;
            }

            return false;

        } catch (error) {
            console.error('检查循环引用失败:', error);
            return true; // 出错时保守处理，认为会产生循环
        }
    }

    /**
     * 获取所有文件列表（从attachments表获取）
     */
    async getAllFiles(req: { wid: string; parent_id?: string; type?: string }): Promise<any[]> {
        try {
            const db = await getDatabase();

            if (!db || !db.attachments) {
                console.error('数据库或attachments集合未初始化');
                return [];
            }

            const selector: any = {
                wid: req.wid
            };

            if (req.parent_id !== undefined) {
                selector.parent_id = req.parent_id;
            }

            if (req.type) {
                selector.type = req.type;
            }

            const files = await db.attachments.find({
                selector,
                sort: [{ order: 'asc' }, { create_at: 'asc' }]
            }).exec();

            console.log('📄 获取所有文件:', {
                wid: req.wid,
                parent_id: req.parent_id,
                type: req.type,
                count: files.length,
                timestamp: new Date().toLocaleString()
            });

            return files.map((file: { toJSON: () => any; }) => file.toJSON());

        } catch (error) {
            console.error('❌ 获取所有文件失败:', error);
            return [];
        }
    }

    /**
     * 检查并创建root文件夹（如果不存在）
     * @param wid 工作区ID
     * @param childId 子项ID（用于更新root的children）
     */
    async ensureRootFolderExists(wid: string, childId?: string): Promise<void> {
        try {
            const db = await getDatabase();
            const rootId = wid + '_root'
            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            // 检查root文件夹是否存在
            const rootFolder = await db.folders.findOne({
                selector: {
                    id: rootId,
                    wid: wid
                }
            }).exec();

            if (!rootFolder) {
                // 创建root文件夹
                const now = Math.floor(Date.now() / 1000);
                const rootFolderData: FolderDocType = {
                    id: rootId,
                    wid: wid,
                    file_name: 'root',
                    type: 'folder',
                    parent_id: '',
                    order: 0,
                    create_at: now,
                    update_at: now,
                    children: []
                };

                await db.folders.insert(rootFolderData);

                console.log('🏗️ 创建root文件夹:', {
                    wid: wid,
                    childId: childId,
                    timestamp: new Date().toLocaleString()
                });
            }

        } catch (error) {
            console.error('❌ 确保root文件夹存在失败:', error);
            throw error;
        }
    }

    /**
     * 更新文件夹的children数组
     * @param req 包含文件夹ID和新的children数组
     */
    async updateFolderChildren(req: { id: string; children: string[] }): Promise<void> {
        try {
            const db = await getDatabase();

            if (!db || !db.folders) {
                throw new Error('数据库或folders集合未初始化');
            }

            const folder = await db.folders.findOne({
                selector: { id: req.id }
            }).exec();

            if (!folder) {
                throw new Error(`文件夹不存在: ${req.id}`);
            }

            const now = Math.floor(Date.now() / 1000);
            await folder.update({
                $set: {
                    children: req.children,
                    update_at: now
                }
            });

            console.log('📂 更新文件夹children:', {
                folderId: req.id,
                folderName: folder.file_name,
                newChildren: req.children,
                timestamp: new Date().toLocaleString()
            });

        } catch (error) {
            console.error('❌ 更新文件夹children失败:', error);
            throw error;
        }
    }

}

export const folderService = new FolderService();