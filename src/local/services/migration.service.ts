import { RxDatabase } from 'rxdb';

/**
 * 数据库迁移服务
 * 处理数据备份、恢复和迁移过程中的错误处理
 */
export class MigrationService {
  private static readonly BACKUP_KEY = 'rxdb_backup';

  /**
   * 备份数据库
   */
  static async backupDatabase(db: RxDatabase): Promise<void> {
    try {
      console.log('📦 开始备份数据库...');
      const backup: Record<string, any[]> = {};
      
      // 备份所有集合的数据
      for (const [collectionName, collection] of Object.entries(db.collections)) {
        try {
          const docs = await collection.find().exec();
          backup[collectionName] = docs.map((doc: any) => doc.toJSON());
          console.log(`✅ 已备份集合 ${collectionName}: ${docs.length} 条记录`);
        } catch (error) {
          console.warn(`⚠️  备份集合 ${collectionName} 失败:`, error);
        }
      }

      // 保存备份到 localStorage
      localStorage.setItem(this.BACKUP_KEY, JSON.stringify({
        timestamp: Date.now(),
        data: backup
      }));

      console.log('✅ 数据库备份完成');
    } catch (error) {
      console.error('❌ 数据库备份失败:', error);
      throw error;
    }
  }

  /**
   * 从备份恢复数据库
   */
  static async restoreFromBackup(db: RxDatabase): Promise<boolean> {
    try {
      const backupData = localStorage.getItem(this.BACKUP_KEY);
      if (!backupData) {
        console.warn('⚠️  未找到备份数据');
        return false;
      }

      console.log('🔄 开始从备份恢复数据...');
      const backup = JSON.parse(backupData);
      
      // 检查备份时间（超过7天的备份不自动恢复）
      const backupAge = Date.now() - backup.timestamp;
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
      
      if (backupAge > maxAge) {
        console.warn('⚠️  备份数据过旧，跳过自动恢复');
        return false;
      }

      // 恢复数据到各个集合
      let restoredCount = 0;
      for (const [collectionName, documents] of Object.entries(backup.data)) {
        if (db.collections[collectionName] && Array.isArray(documents)) {
          try {
            await db.collections[collectionName].bulkInsert(documents);
            console.log(`✅ 已恢复集合 ${collectionName}: ${documents.length} 条记录`);
            restoredCount += documents.length;
          } catch (error) {
            console.warn(`⚠️  恢复集合 ${collectionName} 失败:`, error);
          }
        }
      }

      console.log(`✅ 数据恢复完成，共恢复 ${restoredCount} 条记录`);
      return true;
    } catch (error) {
      console.error('❌ 数据恢复失败:', error);
      return false;
    }
  }

  /**
   * 清理旧的备份数据
   */
  static clearBackup(): void {
    localStorage.removeItem(this.BACKUP_KEY);
    console.log('🗑️  已清理备份数据');
  }


  /**
   * 处理迁移错误
   */
  static async handleMigrationError(error: any, db?: RxDatabase): Promise<void> {
    console.error('🚨 迁移过程中发生错误:', error);
    
    // 如果是版本冲突错误，尝试备份恢复
    if (error.name === 'RxError' && error.code === 'SCH20') {
      console.log('🔄 检测到schema冲突，尝试清理并重建数据库...');
      
      // 如果有数据库实例，先备份数据
      if (db) {
        try {
          await this.backupDatabase(db);
        } catch (backupError) {
          console.warn('⚠️  自动备份失败:', backupError);
        }
      }

      // 显示用户友好的错误信息
      this.showMigrationDialog();
    }
  }

  /**
   * 显示迁移对话框
   */
  private static showMigrationDialog(): void {
    // 这里可以集成你的UI库显示更友好的对话框
    const message = `
应用数据结构已更新，需要重新初始化数据库。

选项：
1. 刷新页面继续使用（推荐）
2. 如有重要数据丢失，请联系技术支持

是否立即刷新页面？
    `.trim();

    if (confirm(message)) {
      window.location.reload();
    }
  }

  /**
   * 数据库健康检查
   */
  static async healthCheck(db: RxDatabase): Promise<{
    isHealthy: boolean;
    issues: string[];
    collections: Record<string, number>;
  }> {
    const issues: string[] = [];
    const collections: Record<string, number> = {};

    try {
      // 检查各个集合的健康状态
      for (const [collectionName, collection] of Object.entries(db.collections)) {
        try {
          const count = await collection.count().exec();
          collections[collectionName] = count;
        } catch (error: any) {
          issues.push(`集合 ${collectionName} 访问失败: ${error.message}`);
        }
      }

      const isHealthy = issues.length === 0;
      
      console.log(isHealthy ? '✅ 数据库健康检查通过' : '⚠️  数据库存在问题', {
        collections,
        issues
      });

      return { isHealthy, issues, collections };
    } catch (error: any) {
      console.error('❌ 健康检查失败:', error);
      return {
        isHealthy: false,
        issues: [`健康检查失败: ${error.message}`],
        collections: {}
      };
    }
  }

  /**
   * 导出数据库数据为JSON
   */
  static async exportDatabase(db: RxDatabase): Promise<string> {
    try {
      const exportData: Record<string, any[]> = {};
      
      for (const [collectionName, collection] of Object.entries(db.collections)) {
        const docs = await collection.find().exec();
        exportData[collectionName] = docs.map((doc: any) => doc.toJSON());
      }

      const exportObject = {
        timestamp: Date.now(),
        data: exportData
      };

      return JSON.stringify(exportObject, null, 2);
    } catch (error) {
      console.error('❌ 导出数据库失败:', error);
      throw error;
    }
  }
}