import { getDatabase } from '../db';
import { generateId } from '@/local';
import { AttachmentDocType } from '../db/schemas/attachment';

/**
 * 笔记本管理服务
 */
export interface CreateNotebookReq {
    wid: string;
    file_name: string;
    parent_id?: string;
    content?: string;
}

export interface UpdateNotebookReq {
    id: string;
    file_name?: string;
    content?: string;
    parent_id?: string;
}

export interface GetNotebookListReq {
    wid: string;
    parent_id?: string;
}

export class NotebookService {

    /**
     * 创建笔记本
     */
    async createNotebook(req: CreateNotebookReq): Promise<AttachmentDocType> {
        try {
            const db = await getDatabase();
            
            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const id = generateId();
            const now = Date.now();
            const rootId = req.wid + '_root';

            const notebook: AttachmentDocType = {
                id,
                wid: req.wid,
                file_name: req.file_name,
                type: 'file',
                parent_id: req.parent_id || rootId,
                order: 0,
                mime_type: 'notebook',
                content: req.content || '',
                create_at: now,
                update_at: now
            };

            const result = await db.attachments.insert(notebook);
            return result.toJSON();
        } catch (error) {
            console.error('创建笔记本失败:', error);
            throw error;
        }
    }

    /**
     * 获取笔记本列表
     */
    async getNotebookList(req: GetNotebookListReq): Promise<AttachmentDocType[]> {
        try {
            const db = await getDatabase();
            
            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const rootId = req.wid + '_root';
            const parentId = req.parent_id || rootId;

            const notebooks = await db.attachments.find({
                selector: {
                    wid: req.wid,
                    parent_id: parentId,
                    mime_type: 'notebook',
                    type: 'file'
                },
                sort: [{ create_at: 'desc' }]
            }).exec();

            return notebooks.map((notebook: { toJSON: () => any; }) => notebook.toJSON());
        } catch (error) {
            console.error('获取笔记本列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有笔记本列表（不限制parent_id）
     */
    async getAllNotebooks(wid: string): Promise<AttachmentDocType[]> {
        try {
            const db = await getDatabase();
            
            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const notebooks = await db.attachments.find({
                selector: {
                    wid: wid,
                    mime_type: 'notebook',
                    type: 'file'
                },
                sort: [{ update_at: 'desc' }]
            }).exec();

            return notebooks.map((notebook: { toJSON: () => any; }) => notebook.toJSON());
        } catch (error) {
            console.error('获取所有笔记本失败:', error);
            throw error;
        }
    }

    /**
     * 更新笔记本
     */
    async updateNotebook(req: UpdateNotebookReq): Promise<AttachmentDocType> {
        try {
            const db = await getDatabase();
            
            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const notebook = await db.attachments.findOne({
                selector: { 
                    id: req.id,
                    mime_type: 'notebook',
                    type: 'file'
                }
            }).exec();

            if (!notebook) {
                throw new Error('笔记本不存在');
            }

            const updateData: Partial<AttachmentDocType> = {
                update_at: Date.now()
            };

            if (req.file_name !== undefined) {
                updateData.file_name = req.file_name;
            }
            if (req.content !== undefined) {
                updateData.content = req.content;
            }
            if (req.parent_id !== undefined) {
                updateData.parent_id = req.parent_id;
            }

            const result = await notebook.patch(updateData);
            return result.toJSON();
        } catch (error) {
            console.error('更新笔记本失败:', error);
            throw error;
        }
    }

    /**
     * 删除笔记本
     */
    async deleteNotebook(id: string): Promise<boolean> {
        try {
            const db = await getDatabase();
            
            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const notebook = await db.attachments.findOne({
                selector: { 
                    id,
                    mime_type: 'notebook',
                    type: 'file'
                }
            }).exec();

            if (!notebook) {
                throw new Error('笔记本不存在');
            }

            await notebook.remove();
            return true;
        } catch (error) {
            console.error('删除笔记本失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取笔记本
     */
    async getNotebookById(id: string): Promise<AttachmentDocType | null> {
        try {
            const db = await getDatabase();
            
            if (!db || !db.attachments) {
                throw new Error('数据库或attachments集合未初始化');
            }

            const notebook = await db.attachments.findOne({
                selector: { 
                    id,
                    mime_type: 'notebook',
                    type: 'file'
                }
            }).exec();

            return notebook ? notebook.toJSON() : null;
        } catch (error) {
            console.error('获取笔记本失败:', error);
            throw error;
        }
    }
}

// 导出单例
export const notebookService = new NotebookService();