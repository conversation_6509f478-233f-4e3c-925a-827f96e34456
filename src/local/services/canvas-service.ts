import { NODE_TYPE_MAPS } from "@/components/canvas/lib/node-types";
import { getDatabase } from "../db";
import { generateId } from "@/local";
import { filter, flatten, map } from "lodash-es";

export interface CreateCanvasReq {
  wid: string;
  content: string;
}

export interface CreateCanvasResp {
  cid: string;
}

export interface UpdateCanvasReq {
  cid: string;
  content: string;
}

export interface GetCanvasReq {
  wid?: string; // 可选，为空时获取全部画布
}

export interface GetCanvasResp {
  cid: string;
  wid: string;
  content: string;
  created_at: number;
  updated_at: number;
  deleted_at: number;
}

export interface ListCanvasReq {
  wid?: string; // 可选，为空时获取全部画布
}

export interface ListCanvasResp {
  list: Array<{
    cid: string;
    wid: string;
    content: string;
    created_at: number;
    updated_at: number;
    deleted_at: number;
  }>;
  total: number;
}

export interface DeleteCanvasReq {
  cid: string;
}

export interface RestoreCanvasReq {
  cid: string;
}

export interface PermanentDeleteCanvasReq {
  cid: string;
}

export interface UpsertCanvasReq {
  wid: string; // 工作区ID
  content: string; // 画布内容
}

export interface UpsertCanvasResp {
  cid: string;
  isNew: boolean; // 标识是否为新创建的画布
}

export interface GetNodeContentResp {
  list: Array<any>;
  total: number;
}

export class CanvasService {
  /**
   * 创建画布
   */
  async create(req: CreateCanvasReq): Promise<CreateCanvasResp> {
    try {
      console.log("创建画布请求:", req);

      const db = await getDatabase();

      // 确保数据库和集合已初始化
      if (!db || !db.canvases) {
        throw new Error("数据库或canvases集合未初始化");
      }

      // 验证工作区是否存在
      const workspace = await db.workspaces
        .findOne({
          selector: { id: req.wid, del_id: "0" },
        })
        .exec();

      if (!workspace) {
        throw new Error("工作区不存在");
      }

      const cid = generateId();
      const now = Math.floor(Date.now() / 1000);

      console.log("创建画布:", cid, req.wid);

      // 创建画布
      await db.canvases.insert({
        id: cid,
        wid: req.wid,
        content: req.content,
        created_at: now,
        updated_at: now,
        deleted_at: 0,
      });

      console.log("画布创建成功:", cid);
      return { cid };
    } catch (error) {
      console.error("创建画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("创建画布失败");
      }
    }
  }

  /**
   * 更新画布
   */
  async update(req: UpdateCanvasReq): Promise<void> {
    try {
      const db = await getDatabase();
      const canvas = await db.canvases
        .findOne({
          selector: { id: req.cid, deleted_at: 0 },
        })
        .exec();

      if (!canvas) {
        throw new Error("画布不存在");
      }

      const now = Math.floor(Date.now() / 1000);

      await canvas.update({
        $set: {
          content: req.content,
          updated_at: now,
        },
      });

      console.log("画布更新成功:", req.cid);
    } catch (error) {
      console.error("更新画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("更新画布失败");
      }
    }
  }

  /**
   * 获取画布详情
   */
  async get(req: GetCanvasReq): Promise<GetCanvasResp | null> {
    try {
      const db = await getDatabase();

      // 构建查询条件
      const selector: any = { deleted_at: 0 };
      if (req.wid) {
        selector.wid = req.wid;
      }

      const canvas = await db.canvases
        .findOne({
          selector,
        })
        .exec();

      if (!canvas) {
        return null;
      }

      return {
        cid: canvas.id,
        wid: canvas.wid,
        content: canvas.content,
        created_at: canvas.created_at,
        updated_at: canvas.updated_at,
        deleted_at: canvas.deleted_at,
      };
    } catch (error) {
      console.error("获取画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("获取画布失败");
      }
    }
  }

  /**
   * 获取工作区下的画布列表
   */
  async list(req: ListCanvasReq): Promise<ListCanvasResp> {
    try {
      const db = await getDatabase();

      // 构建查询条件
      const selector: any = { deleted_at: 0 };
      if (req.wid) {
        selector.wid = req.wid;
      }

      // 查询工作区下的未删除画布（或全部未删除画布）
      const canvases = await db.canvases
        .find({
          selector,
          sort: [{ updated_at: "desc" }],
        })
        .exec();

      const list = canvases.map((canvas: any) => ({
        cid: canvas.id,
        wid: canvas.wid,
        content: canvas.content,
        created_at: canvas.created_at,
        updated_at: canvas.updated_at,
        deleted_at: canvas.deleted_at,
      }));
      try {
        const content = JSON.parse(list[0].content);
        console.log("画布内容:", content);
      } catch (error) {
        console.log("画布内容解析失败:", error);
      }
      console.log("画布列表:", list);
      return {
        list,
        total: list.length,
      };
    } catch (error) {
      console.error("获取画布列表失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("获取画布列表失败");
      }
    }
  }

  // 获取所有node节点内容
  async getNodeContentList(req: ListCanvasReq): Promise<GetNodeContentResp> {
    try {
      const db = await getDatabase();

      // 构建查询条件
      const selector: any = { deleted_at: 0 };
      if (req.wid) {
        selector.wid = req.wid;
      }

      // 查询工作区下的未删除画布（或全部未删除画布）
      const canvases = await db.canvases
        .find({
          selector,
          sort: [{ updated_at: "desc" }],
        })
        .exec();

      const list = flatten(map(canvases, (canvas: any) => {
        const content = JSON.parse(canvas.content);
        return content.nodes;
      }));
      return {
        list,
        total: list.length,
      };
    } catch (error) {
      console.error("获取画布列表失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("获取画布列表失败");
      }
    }
  }
  
  /**
   * 删除画布（软删除）
   */
  async delete(req: DeleteCanvasReq): Promise<void> {
    try {
      const db = await getDatabase();
      const canvas = await db.canvases
        .findOne({
          selector: { id: req.cid },
        })
        .exec();

      if (!canvas) {
        throw new Error("画布不存在");
      }

      const now = Math.floor(Date.now() / 1000);

      await canvas.update({
        $set: {
          deleted_at: now,
          updated_at: now,
        },
      });

      console.log("画布删除成功:", req.cid);
    } catch (error) {
      console.error("删除画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("删除画布失败");
      }
    }
  }

  /**
   * 恢复画布
   */
  async restore(req: RestoreCanvasReq): Promise<void> {
    try {
      const db = await getDatabase();
      const canvas = await db.canvases
        .findOne({
          selector: { id: req.cid },
        })
        .exec();

      if (!canvas) {
        throw new Error("画布不存在");
      }

      const now = Math.floor(Date.now() / 1000);

      await canvas.update({
        $set: {
          deleted_at: 0,
          updated_at: now,
        },
      });

      console.log("画布恢复成功:", req.cid);
    } catch (error) {
      console.error("恢复画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("恢复画布失败");
      }
    }
  }

  /**
   * 永久删除画布
   */
  async permanentDelete(req: PermanentDeleteCanvasReq): Promise<void> {
    try {
      const db = await getDatabase();
      const canvas = await db.canvases
        .findOne({
          selector: { id: req.cid },
        })
        .exec();

      if (!canvas) {
        throw new Error("画布不存在");
      }

      await canvas.remove();

      console.log("画布永久删除成功:", req.cid);
    } catch (error) {
      console.error("永久删除画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("永久删除画布失败");
      }
    }
  }

  /**
   * 有就更新，没有就创建画布
   */
  async upsert(req: UpsertCanvasReq): Promise<UpsertCanvasResp> {
    try {
      console.log("Upsert画布请求:", req);

      const db = await getDatabase();

      // 确保数据库和集合已初始化
      if (!db || !db.canvases) {
        throw new Error("数据库或canvases集合未初始化");
      }

      // 验证工作区是否存在
      const workspace = await db.workspaces
        .findOne({
          selector: { id: req.wid, del_id: "0" },
        })
        .exec();

      if (!workspace) {
        throw new Error("工作区不存在");
      }

      const now = Math.floor(Date.now() / 1000);
      // 如果没有wid， 则报错
      if (!req.wid) {
        throw new Error("工作区ID不能为空");
      }
      const existingCanvas = await db.canvases
        .findOne({
          selector: { wid: req.wid, deleted_at: 0 },
        })
        .exec();

      if (existingCanvas) {
        // 画布存在，执行更新
        await existingCanvas.update({
          $set: {
            content: req.content,
            updated_at: now,
          },
        });

        console.log("画布更新成功:", req.wid);
        return { cid: req.wid, isNew: false };
      }

      // 画布不存在或未提供cid，创建新画布
      const cid = generateId();

      await db.canvases.insert({
        id: cid,
        wid: req.wid,
        content: req.content,
        created_at: now,
        updated_at: now,
        deleted_at: 0,
      });

      console.log("画布创建成功:", cid);
      return { cid, isNew: true };
    } catch (error) {
      console.error("Upsert画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("Upsert画布失败");
      }
    }
  }

  /**
   * 根据工作区ID获取画布（支持获取已删除的画布）
   * @param wid 工作区ID，为空时获取全部画布
   * @param includeDeleted 是否包含已删除的画布
   */
  async getByWorkspace(
    wid?: string,
    includeDeleted: boolean = false
  ): Promise<GetCanvasResp[]> {
    try {
      const db = await getDatabase();

      const selector: any = {};

      // 如果提供了 wid，则按工作区过滤
      if (wid) {
        selector.wid = wid;
      }

      // 如果不包含已删除的，则添加删除状态过滤
      if (!includeDeleted) {
        selector.deleted_at = 0;
      }

      const canvases = await db.canvases
        .find({
          selector,
          sort: [{ updated_at: "desc" }],
        })
        .exec();

      return canvases.map((canvas: any) => ({
        cid: canvas.id,
        wid: canvas.wid,
        content: canvas.content,
        created_at: canvas.created_at,
        updated_at: canvas.updated_at,
        deleted_at: canvas.deleted_at,
      }));
    } catch (error) {
      console.error("根据工作区获取画布失败:", error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error("根据工作区获取画布失败");
      }
    }
  }
  // 根据工作区ID获取画布节点列表
  async getCanvasNodeList(wid: string): Promise<any> {
    try {
      const db = await getDatabase();
      const canvases = await db.canvases
        .find({
          selector: { wid: wid, deleted_at: 0 },
        })
        .exec();
      const orgNodes = canvases.map((canvas: any) => {
        const content = JSON.parse(canvas.content);
        return content.nodes;
      });
      const nodes = filter(flatten(orgNodes), (node: any) => node.type === 'markdown');
      return nodes;
    } catch (error) {
      console.error("根据工作区获取画布节点列表失败:", error);
    }
  }
}
