// 简单事件总线：用于跨组件通知编辑器按 id 刷新内容
export const EditorEvents = {
  listeners: new Map<string, Function[]>(),

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);
  },

  off(event: string, callback: Function) {
    if (!this.listeners.has(event)) return;
    const callbacks = this.listeners.get(event) || [];
    this.listeners.set(
      event,
      callbacks.filter((cb) => cb !== callback)
    );
  },

  emit(event: string, ...args: any[]) {
    if (!this.listeners.has(event)) return;
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach((callback) => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`Error in ${event} event handler:`, error);
      }
    });
  },
};

// 统一事件名，避免拼写错误
export const EDITOR_EVENTS = {
  RELOAD_REQUEST: 'EDITOR_RELOAD_REQUEST',
};

