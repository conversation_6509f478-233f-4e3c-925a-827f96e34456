# 数据库迁移指南

## 概述

本项目已实现完整的RxDB数据库自动迁移机制，支持：
- ✅ 自动数据备份和恢复
- ✅ Schema版本管理
- ✅ 渐进式数据迁移
- ✅ 错误处理和回滚
- ✅ 数据库健康检查
- ✅ 用户友好的迁移提示

## 如何添加新的迁移

### 1. 更新Schema版本

在需要修改的schema文件中更新版本号：

```typescript
// src/local/db/schemas/note.ts
export const noteSchema: RxJsonSchema<NoteDocType> = {
    title: 'note schema',
    version: 1, // 从0升级到1
    // ... 其他配置
    properties: {
        // 现有字段
        id: { type: 'string', maxLength: 100 },
        content: { type: 'string' },
        
        // 新增字段
        tags: { 
            type: 'array', 
            items: { type: 'string' },
            default: []
        }
    }
};
```

### 2. 添加迁移策略

在 `src/local/db/migrations/index.ts` 中添加对应的迁移策略：

```typescript
export const noteMigrationStrategies: RxMigrationStrategies = {
  // 版本0 → 版本1 的迁移
  1: function(oldDoc: any) {
    // 为旧文档添加默认的tags字段
    oldDoc.tags = oldDoc.tags || [];
    
    // 使用工具函数确保数据类型正确
    oldDoc.content = migrationUtils.ensureString(oldDoc.content, '');
    
    return oldDoc;
  }
};
```

### 3. 更新数据库版本

在 `src/local/db/migrations/index.ts` 中更新整体数据库版本：

```typescript
export const DATABASE_VERSION = 2; // 递增版本号
```

## 迁移策略编写指南

### 基础原则

1. **向后兼容**: 总是假设旧数据可能缺少新字段
2. **类型安全**: 使用 `migrationUtils` 确保数据类型正确
3. **错误处理**: 迁移函数应该能处理异常情况
4. **性能考虑**: 对于大量数据，避免复杂的同步操作

### 常见迁移场景

#### 添加新字段
```typescript
1: function(oldDoc: any) {
  oldDoc.newField = oldDoc.newField || 'defaultValue';
  return oldDoc;
}
```

#### 修改字段类型
```typescript
1: function(oldDoc: any) {
  // 字符串转数组
  if (typeof oldDoc.tags === 'string') {
    oldDoc.tags = [oldDoc.tags];
  }
  return oldDoc;
}
```

#### 重命名字段
```typescript
1: function(oldDoc: any) {
  migrationUtils.renameField(oldDoc, 'oldName', 'newName');
  return oldDoc;
}
```

#### 删除废弃字段
```typescript
1: function(oldDoc: any) {
  migrationUtils.removeFields(oldDoc, ['deprecatedField1', 'deprecatedField2']);
  return oldDoc;
}
```

#### 复杂数据转换
```typescript
1: function(oldDoc: any) {
  // 合并字段
  if (oldDoc.firstName && oldDoc.lastName) {
    oldDoc.fullName = `${oldDoc.firstName} ${oldDoc.lastName}`;
    delete oldDoc.firstName;
    delete oldDoc.lastName;
  }
  return oldDoc;
}
```

### 异步迁移
```typescript
1: async function(oldDoc: any) {
  try {
    // 执行异步操作
    const enrichedData = await fetchAdditionalData(oldDoc.id);
    oldDoc.metadata = enrichedData;
    return oldDoc;
  } catch (error) {
    console.warn('Migration failed for doc', oldDoc.id, error);
    return oldDoc; // 即使失败也要返回文档
  }
}
```

## 测试迁移

### 1. 本地测试
```javascript
// 在浏览器控制台中
// 清除当前数据库版本（触发迁移）
localStorage.removeItem('rxdb_version');

// 重新加载页面，观察迁移日志
location.reload();
```

### 2. 备份测试数据
```javascript
// 导出当前数据
const db = await getDatabase();
const exportData = await MigrationService.exportDatabase(db);
console.log('Export data:', exportData);

// 保存到文件
const blob = new Blob([exportData], { type: 'application/json' });
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'db-backup.json';
a.click();
```

### 3. 健康检查
```javascript
// 检查数据库健康状态
const db = await getDatabase();
const health = await MigrationService.healthCheck(db);
console.log('Database health:', health);
```

## 部署注意事项

### 1. 分阶段部署
- **第一阶段**: 部署支持新旧两种数据格式的代码
- **第二阶段**: 等待大部分用户升级后，移除对旧格式的支持

### 2. 监控迁移
- 查看浏览器控制台的迁移日志
- 监控用户反馈
- 准备回滚方案

### 3. 用户通知
迁移过程对用户是透明的，但在重大更改时可以考虑：
- 在更新日志中说明数据库升级
- 提供数据导出功能
- 准备客服支持

## 故障排除

### 迁移失败怎么办？
1. 检查浏览器控制台的错误日志
2. 尝试清除数据库重新开始：`localStorage.clear(); location.reload();`
3. 检查迁移策略代码是否有语法错误
4. 确认schema版本号是否正确递增

### 数据丢失怎么办？
1. 检查localStorage中是否有备份：`localStorage.getItem('rxdb_backup')`
2. 使用MigrationService恢复备份
3. 联系技术支持

### 性能问题怎么办？
1. 检查迁移策略是否包含耗时操作
2. 考虑分批处理大量数据
3. 使用Web Worker执行复杂迁移

## 工具函数

### MigrationService API

```typescript
// 手动备份
await MigrationService.backupDatabase(db);

// 从备份恢复
await MigrationService.restoreFromBackup(db);

// 健康检查
const health = await MigrationService.healthCheck(db);

// 导出数据
const exportData = await MigrationService.exportDatabase(db);

// 清理备份
MigrationService.clearBackup();
```

### migrationUtils 工具

```typescript
// 类型确保
migrationUtils.ensureString(value, defaultValue);
migrationUtils.ensureNumber(value, defaultValue);
migrationUtils.ensureArray(value, defaultValue);

// 字段操作
migrationUtils.renameField(doc, 'oldName', 'newName');
migrationUtils.removeFields(doc, ['field1', 'field2']);

// 时间戳
migrationUtils.addTimestamp(doc);
```

## 最佳实践

1. **小步迭代**: 每次只升级一个版本号
2. **充分测试**: 在本地环境测试迁移策略
3. **文档记录**: 记录每次迁移的原因和影响
4. **监控日志**: 关注生产环境的迁移日志
5. **用户友好**: 提供清晰的错误提示和帮助信息