/**
 * 迁移示例
 * 展示如何添加新的迁移策略
 */

import { MigrationStrategies } from 'rxdb';
import { migrationUtils } from './index';

// 示例：笔记schema从版本0到版本1的迁移
// 假设我们要添加一个tags字段
export const noteV1MigrationExample: MigrationStrategies = {
  1: function(oldDoc: any) {
    // 添加tags字段，默认为空数组
    oldDoc.tags = oldDoc.tags || [];
    
    // 使用工具函数确保数据类型正确
    oldDoc.content = migrationUtils.ensureString(oldDoc.content, '');
    oldDoc.wid = migrationUtils.ensureString(oldDoc.wid, '');
    
    // 确保时间戳字段存在
    migrationUtils.addTimestamp(oldDoc);
    
    return oldDoc;
  }
};

// 示例：工作区schema从版本0到版本1的迁移
// 假设我们要添加description字段并修改一些字段
export const workspaceV1MigrationExample: MigrationStrategies = {
  1: function(oldDoc: any) {
    // 添加新字段
    oldDoc.description = oldDoc.description || '';
    
    // 确保数字字段的类型正确
    oldDoc.last_at = migrationUtils.ensureNumber(oldDoc.last_at);
    oldDoc.del_at = migrationUtils.ensureNumber(oldDoc.del_at);
    
    // 如果某些字段不再需要，可以移除
    // migrationUtils.removeFields(oldDoc, ['old_field']);
    
    return oldDoc;
  },
  
  // 版本1到版本2的迁移示例
  2: function(oldDoc: any) {
    // 重命名字段
    migrationUtils.renameField(oldDoc, 'del_id', 'deleted_id');
    migrationUtils.renameField(oldDoc, 'del_at', 'deleted_at');
    
    return oldDoc;
  }
};

// 示例：复杂的异步迁移
export const complexMigrationExample: MigrationStrategies = {
  1: async function(oldDoc: any) {
    // 对于复杂的迁移，可能需要异步操作
    try {
      // 例如：从外部API获取额外数据
      // const additionalData = await fetchExternalData(oldDoc.id);
      // oldDoc.enrichedData = additionalData;
      
      // 或者进行复杂的数据转换
      if (oldDoc.metadata && typeof oldDoc.metadata === 'string') {
        try {
          oldDoc.metadata = JSON.parse(oldDoc.metadata);
        } catch (e) {
          oldDoc.metadata = {};
        }
      }
      
      return oldDoc;
    } catch (error) {
      console.warn('Migration warning for doc', oldDoc.id, error);
      // 即使出错也要返回文档，确保迁移继续进行
      return oldDoc;
    }
  }
};

// 示例：批处理迁移（用于大量数据的优化）
export const batchMigrationExample: MigrationStrategies = {
  1: function(oldDoc: any, context: any) {
    // context 可以用来在批处理中共享状态
    if (!context.processedCount) {
      context.processedCount = 0;
    }
    
    context.processedCount++;
    
    // 每处理1000条记录打印一次日志
    if (context.processedCount % 1000 === 0) {
      console.log(`Migrated ${context.processedCount} documents`);
    }
    
    // 执行实际的数据迁移
    oldDoc.migrated = true;
    oldDoc.migrationTimestamp = Date.now();
    
    return oldDoc;
  }
};