import { MigrationStrategies } from 'rxdb';

/**
 * 数据库迁移策略配置
 * 每个集合的迁移策略，按版本号升序排列
 */

// 工作区迁移策略
export const workspaceMigrationStrategies: MigrationStrategies = {
  // 示例：版本0 → 版本1 的迁移
  // 1: function(oldDoc: any) {
  //   // 添加新字段的默认值
  //   oldDoc.description = oldDoc.description || '';
  //   return oldDoc;
  // }
};

// 分类迁移策略
export const categoryMigrationStrategies: MigrationStrategies = {
  // 示例：版本0 → 版本1 的迁移
  // 1: function(oldDoc: any) {
  //   // 修改字段类型
  //   oldDoc.ws_num = Number(oldDoc.ws_num) || 0;
  //   return oldDoc;
  // }
};

// 笔记迁移策略
export const noteMigrationStrategies: MigrationStrategies = {
  // 示例：版本0 → 版本1 的迁移
  // 1: function(oldDoc: any) {
  //   // 添加新字段
  //   oldDoc.tags = oldDoc.tags || [];
  //   return oldDoc;
  // }
};

// 节点迁移策略
export const nodeMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 连线迁移策略
export const edgeMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 附件迁移策略
export const attachmentMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 会话迁移策略
export const sessionMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 轮次迁移策略
export const roundMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 标记迁移策略
export const markMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 标签组迁移策略
export const tagGroupMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 标签迁移策略
export const tagMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 节点标签关联迁移策略
export const nodeTagMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 画布迁移策略
export const canvasMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 笔记本迁移策略
export const notebookMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

// 文件夹迁移策略
export const folderMigrationStrategies: MigrationStrategies = {
  // 示例迁移策略
};

/**
 * 迁移策略映射表
 * 方便在数据库初始化时使用
 */
export const allMigrationStrategies = {
  workspaces: workspaceMigrationStrategies,
  categories: categoryMigrationStrategies,
  notes: noteMigrationStrategies,
  nodes: nodeMigrationStrategies,
  edges: edgeMigrationStrategies,
  attachments: attachmentMigrationStrategies,
  sessions: sessionMigrationStrategies,
  rounds: roundMigrationStrategies,
  marks: markMigrationStrategies,
  tagGroups: tagGroupMigrationStrategies,
  tags: tagMigrationStrategies,
  nodeTags: nodeTagMigrationStrategies,
  canvases: canvasMigrationStrategies,
  notebooks: notebookMigrationStrategies,
  folders: folderMigrationStrategies,
};


/**
 * 通用迁移工具函数
 */
export const migrationUtils = {
  // 添加默认时间戳
  addTimestamp: (doc: any, timestamp = Date.now()) => {
    if (!doc.create_at) doc.create_at = Math.floor(timestamp / 1000);
    if (!doc.update_at) doc.update_at = Math.floor(timestamp / 1000);
    return doc;
  },

  // 确保数字类型
  ensureNumber: (value: any, defaultValue = 0) => {
    return typeof value === 'number' ? value : Number(value) || defaultValue;
  },

  // 确保字符串类型
  ensureString: (value: any, defaultValue = '') => {
    return typeof value === 'string' ? value : String(value || defaultValue);
  },

  // 确保数组类型
  ensureArray: (value: any, defaultValue: any[] = []) => {
    return Array.isArray(value) ? value : defaultValue;
  },

  // 重命名字段
  renameField: (doc: any, oldName: string, newName: string) => {
    if (doc.hasOwnProperty(oldName)) {
      doc[newName] = doc[oldName];
      delete doc[oldName];
    }
    return doc;
  },

  // 移除废弃字段
  removeFields: (doc: any, fieldsToRemove: string[]) => {
    fieldsToRemove.forEach(field => {
      if (doc.hasOwnProperty(field)) {
        delete doc[field];
      }
    });
    return doc;
  }
};