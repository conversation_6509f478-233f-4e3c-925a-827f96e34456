import { RxJsonSchema } from 'rxdb';

export interface NoteBookDocType {
    id: string; // 笔记ID
    wid: string; // 工作区ID
    content: string; // 笔记内容
    create_at: number; // 创建时间
    update_at: number; // 更新时间
    diff_content?: string; // 差异内容
    parent_id: string; // 父节点ID
    file_name?: string; // 文件名称
    type: 'notebook'; // 类型
}

export const noteBookSchema: RxJsonSchema<NoteBookDocType> = {
    title: 'note schema',
    version: 0,
    description: '笔记模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        wid: {
            type: 'string',
            maxLength: 100
        },
        content: {
            type: 'string'
        },
        diff_content: {
            type: 'string'
        },
        create_at: {
            type: 'number',
            minimum: 0
        },
        update_at: {
            type: 'number',
            minimum: 0
        },
        parent_id: {
            type: 'string',
            maxLength: 100
        },
        file_name: {
            type: 'string',
            maxLength: 100
        },
        type: {
            type: 'string',
            enum: ['notebook']
        }
    },
    required: ['id', 'wid', 'content'],
    indexes: ['wid']
}; 