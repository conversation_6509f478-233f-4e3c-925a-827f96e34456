import { RxJsonSchema } from "rxdb";

export interface CanvasType {
  id: string; // 画布ID
  wid: string; // 工作区ID
  content: string; // 画布内容 json
  created_at: number; // 创建时间
  updated_at: number; // 更新时间
  deleted_at: number; // 删除时间，0表示未删除
}

export const canvasSchema: RxJsonSchema<CanvasType> = {
  title: "canvas schema",
  version: 0,
  description: "画布模式",
  primaryKey: "id",
  type: "object",
  properties: {
    id: {
      type: "string",
      maxLength: 100,
    },
    wid: {
      type: "string",
      maxLength: 100,
    },
    content: {
      type: "string",
    },
    created_at: {
      type: "number",
      minimum: 0,
    },
    updated_at: {
      type: "number",
      minimum: 0,
    },
    deleted_at: {
      type: "number",
      minimum: 0,
    },
  },
  attachments: {},
  required: ["id", "wid", "content", "created_at", "updated_at", "deleted_at"],
};
