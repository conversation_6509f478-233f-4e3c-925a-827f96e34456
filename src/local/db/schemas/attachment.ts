import { RxJsonSchema } from 'rxdb';

export interface AttachmentDocType {
    id: string; // 附件/文件夹ID
    wid: string; // 工作区ID
    file_name: string; // 文件名/文件夹名
    type: 'file' | 'folder'; // 类型：文件或文件夹
    parent_id: string; // 父文件夹ID，根目录为'root'
    order?: number; // 排序顺序，数字越小越靠前
    size?: number; // 文件大小（字节），仅文件类型有值
    mime_type?: string; // MIME类型，仅文件类型有值
    file_path?: string; // 文件路径，仅文件类型有值
    create_at: number; // 创建时间
    update_at: number; // 更新时间
    tag?: string; // 标签
    content?: string //
    diff_content?: string
}

export const attachmentSchema: RxJsonSchema<AttachmentDocType> = {
    title: 'attachment schema',
    version: 0, // 暂时保持版本0，通过服务层处理迁移
    description: '附件/文件夹模式',
    primaryKey: 'id',
    type: 'object',
    properties: {
        id: {
            type: 'string',
            maxLength: 100
        },
        wid: {
            type: 'string',
            maxLength: 100
        },
        file_name: {
            type: 'string',
            maxLength: 255
        },
        type: {
            type: 'string',
            enum: ['file', 'folder']
        },
        parent_id: {
            type: 'string',
            maxLength: 100,
            default: 'root'
        },
        order: {
            type: 'number',
            minimum: 0,
            default: 0
        },
        size: {
            type: 'number',
            minimum: 0
        },
        mime_type: {
            type: 'string',
            maxLength: 100
        },
        file_path: {
            type: 'string',
            maxLength: 500
        },
        create_at: {
            type: 'integer',
            minimum: 0,
        },
        update_at: {
            type: 'integer',
            minimum: 0,
        },
        tag: {
            type: 'string',
            maxLength: 100
        },
        content: {
            type: 'string',
        },
        diff_content: {
            type: 'string',
        }
    },
    attachments: {},
    required: ['id', 'wid', 'file_name', 'type', 'parent_id', 'order'],
    indexes: [
        'wid',
        'type',
        'parent_id',
        ['wid', 'type'], // 按工作区和类型查询
        ['wid', 'parent_id'], // 按工作区和父文件夹查询
        ['wid', 'parent_id', ], // 按工作区、父文件夹和排序查询
        ['parent_id'], // 按父文件夹和排序查询
        ['wid', 'type', 'parent_id'] // 复合查询优化
    ]
}; 