// 添加RxDB更新插件
import {addRxPlugin, createRxDatabase, RxCollection} from 'rxdb';
// 使用 Dexie 存储替代 PouchDB
import {getRxStorageDexie} from 'rxdb/plugins/storage-dexie';
// 导入更新插件
import {RxDBUpdatePlugin} from 'rxdb/plugins/update';
// 导入验证器
import {getAjv, wrappedValidateAjvStorage} from 'rxdb/plugins/validate-ajv';
import {RxDBAttachmentsPlugin} from 'rxdb/plugins/attachments';
import ajvErrors from 'ajv-errors';

// 在开发模式下添加开发模式插件
if (import.meta.env.DEV) {
    import('rxdb/plugins/dev-mode').then(module => {
        addRxPlugin(module.RxDBDevModePlugin);
    });
}

// 导入所有集合模式
import {categorySchema} from './schemas/category';
import {noteSchema} from './schemas/note';
import {attachmentSchema} from './schemas/attachment';
import {nodeSchema} from './schemas/node';
import {edgeSchema} from './schemas/edge';
import {roundSchema} from './schemas/round';
import {markSchema} from './schemas/mark';
import {workspaceSchema} from './schemas/workspace';
import {sessionSchema} from "@/local/db/schemas/session.ts";
import {tagGroupSchema} from './schemas/tagGroup';
import {tagSchema} from './schemas/tag';
import {nodeTagSchema} from './schemas/nodeTag';
import { canvasSchema } from './schemas/canvas';
import { noteBookSchema } from './schemas/notebook';
import { FolderSchema } from './schemas/folder';
// 导入迁移策略
import { allMigrationStrategies } from './migrations';
import { MigrationService } from '../services/migration.service';
// 添加RxDB插件
addRxPlugin(RxDBUpdatePlugin);
addRxPlugin(RxDBAttachmentsPlugin);

// 数据库实例
let dbPromise: any = null;

/**
 * 添加时间戳钩子到集合
 */
function addTimestampHooks(collection: RxCollection) {
    // 插入前添加时间戳
    collection.preInsert((data: any) => {
        const now = Math.floor(Date.now() / 1000);

        const hasCreateAt = collection.schema.jsonSchema.properties.create_at !== undefined;
        const hasUpdateAt = collection.schema.jsonSchema.properties.update_at !== undefined;

        if (hasCreateAt && data.create_at === undefined) {
            data.create_at = now;
        }

        if (hasUpdateAt && data.update_at === undefined) {
            data.update_at = now;
        }

        // 为 attachments 集合设置默认值
        if (collection.name === 'attachments') {
            if (data.type === undefined) {
                data.type = 'file';
            }
            if (data.parent_id === undefined) {
                data.parent_id = 'root';
            }
            if (data.order === undefined) {
                data.order = 0;
            }
        }

        return data;
    }, false);

    // 更新前添加时间戳
    collection.preSave((data: any) => {
        const hasUpdateAt = collection.schema.jsonSchema.properties.update_at !== undefined;

        if (hasUpdateAt) {
            data.update_at = Math.floor(Date.now() / 1000);
        }

        return data;
    }, false);
}

// 配置 Ajv 实例
const ajv = getAjv();
ajv.opts.allErrors = true;
ajvErrors(ajv);

// 分类名称唯一性检查
const checkCateName = async (data: any) => {
    const res = await dbPromise.categories.findOne({
        selector: {
            cate_name: data.cate_name,
            id: {
                $ne: data.id
            }
        }
    }).exec()
    if (res) {
        throw new Error('分类名称已存在');
    }
    return data;
}

/**
 * 初始化数据库
 */
export async function getDatabase() {
    if (dbPromise) return dbPromise;

    try {
        console.log('🚀 开始初始化数据库...');
        
        // 创建带验证器的存储
        const storage = wrappedValidateAjvStorage({
            storage: getRxStorageDexie(),
        });

        // 创建数据库
        dbPromise = await createRxDatabase({
            name: 'notes_db',
            storage: storage,
            multiInstance: true,
            ignoreDuplicate: false,
        });

        // 添加集合（包含迁移策略）
        const collections = await dbPromise.addCollections({
            workspaces: {
                schema: workspaceSchema,
                migrationStrategies: allMigrationStrategies.workspaces
            },
            categories: {
                schema: categorySchema,
                migrationStrategies: allMigrationStrategies.categories
            },
            notes: {
                schema: noteSchema,
                migrationStrategies: allMigrationStrategies.notes
            },
            nodes: {
                schema: nodeSchema,
                migrationStrategies: allMigrationStrategies.nodes
            },
            edges: {
                schema: edgeSchema,
                migrationStrategies: allMigrationStrategies.edges
            },
            sessions: {
                schema: sessionSchema,
                migrationStrategies: allMigrationStrategies.sessions
            },
            rounds: {
                schema: roundSchema,
                migrationStrategies: allMigrationStrategies.rounds
            },
            attachments: {
                schema: attachmentSchema,
                migrationStrategies: allMigrationStrategies.attachments
            },
            marks: {
                schema: markSchema,
                migrationStrategies: allMigrationStrategies.marks
            },
            tagGroups: {
                schema: tagGroupSchema,
                migrationStrategies: allMigrationStrategies.tagGroups
            },
            tags: {
                schema: tagSchema,
                migrationStrategies: allMigrationStrategies.tags
            },
            nodeTags: {
                schema: nodeTagSchema,
                migrationStrategies: allMigrationStrategies.nodeTags
            },
            canvases: {
                schema: canvasSchema,
                migrationStrategies: allMigrationStrategies.canvases
            },
            notebooks: {
                schema: noteBookSchema,
                migrationStrategies: allMigrationStrategies.notebooks
            },
            folders: {
                schema: FolderSchema,
                migrationStrategies: allMigrationStrategies.folders
            }
        });

        // 为所有集合添加时间戳钩子
        Object.values(collections).forEach(collection => {
            if (collection) {
                addTimestampHooks(collection as RxCollection);
            }
        });

        // 分类名称唯一性检查
        dbPromise.categories.preInsert(checkCateName);
        dbPromise.categories.preSave(checkCateName);

        // 执行健康检查
        const healthCheck = await MigrationService.healthCheck(dbPromise);
        if (!healthCheck.isHealthy) {
            console.warn('⚠️  数据库健康检查发现问题:', healthCheck.issues);
        } else {
            console.log('✅ 数据库初始化完成', {
                collections: healthCheck.collections
            });
        }

        return dbPromise;
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error);
        
        // 重置数据库实例，避免后续调用使用损坏的实例
        dbPromise = null;
        
        // 处理迁移错误
        await MigrationService.handleMigrationError(error);
        
        throw error;
    }
}

export default getDatabase;