import { create } from 'zustand'

// 工作流状态枚举
export type WorkflowStatus = 'idle' | 'running' | 'error'

// 工作流状态接口
interface WorkflowState {
  status: WorkflowStatus
  errorMessage?: string
}

// 工作流操作接口
interface WorkflowActions {
  // 设置为闲置状态
  setIdle: () => void
  // 设置为正在执行状态
  setRunning: () => void
  // 设置为执行错误状态
  setError: (message?: string) => void
  // 重置状态
  reset: () => void
}

// 工作流 Store 类型
type WorkflowStore = WorkflowState & WorkflowActions

// 创建 workflowStore
export const useWorkflowStore = create<WorkflowStore>((set) => ({
  // 初始状态
  status: 'idle',
  errorMessage: undefined,

  // 设置为闲置状态
  setIdle: () => set({ status: 'idle', errorMessage: undefined }),

  // 设置为正在执行状态
  setRunning: () => set({ status: 'running', errorMessage: undefined }),

  // 设置为执行错误状态
  setError: (message?: string) => set({ 
    status: 'error', 
    errorMessage: message || '执行过程中发生错误'
  }),

  // 重置状态
  reset: () => set({ status: 'idle', errorMessage: undefined }),
}))

// 导出状态选择器
export const useWorkflowStatus = () => useWorkflowStore((state) => state.status)
export const useWorkflowError = () => useWorkflowStore((state) => state.errorMessage)

