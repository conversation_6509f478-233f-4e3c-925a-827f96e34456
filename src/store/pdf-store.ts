import {createWithEqualityFn} from "zustand/traditional";
import {shallow} from "zustand/shallow";
import {PdfHighlighterUtils} from "@/components/pdf/components/highlight/src";
import {Highlight} from "@/components/pdf/components/highlight/src/types";
import {TabItem, WindowState} from "@/components/pdf/components/draggable-tabs/types";
import { useTabPanelStore } from "@/store/tab-panel-store";

export enum modeType {
    mouse = 1,
    highlight = 2,
    textInsert = 3,
    screenshot = 4,
}
export interface CustomHighlight extends Highlight {
    aid: string //附件ID
    color: string
    nid: string // 节点ID
}

export type PdfType = {
    aid: string;      // 高亮器唯一标识符
    fname: string; // 高亮内容
}

export type pdfSingleState = {
    aid: string // 附件id
    filename: string //附件名称
    url: string // 附件地址
    scale: number //缩放比例
    highlights: CustomHighlight[] //高亮
    pdfHighlighterUtils: PdfHighlighterUtils | null // 高亮组件
}

export type pdfState = {
    pdfs: Map<string, pdfSingleState> //所有附件
    mode:  typeof modeType[keyof typeof modeType]; // 操作模式 1=>鼠标模式 2=>高亮模式
    activeAid: string // 当前附件id (主Panel)
    showMenu: boolean // 是否显示目录
    tabItems: TabItem[] // 标签页 (主Panel)
    defaultColor: string // 高亮默认颜色
    selectionColor: string // 选择背景色


    // 窗口管理相关状态
    windows: Map<string, WindowState> // 浮动窗口
    windowsContainer: Map<string, DOMRect> // 窗口标签页容器
    nextWindowZIndex: number // 下一个窗口的z-index
    dragTabWindowId: string // 当前拖拽的窗口
    dragTabSourceWindowId: string // 当前拖拽窗口的源窗口
    dragHoverWindowId: string // 悬浮窗口
    dragHandled?: boolean // 拖拽是否已被处理

    setPdfs: (pdf: Map<string, Partial<pdfSingleState>>) => void // 初始化附件
    addPdfs: (pdf: Map<string, Partial<pdfSingleState>>) => void // 追加附件
    updatePdfs: (newPdf: Required<Pick<pdfSingleState, 'aid'>> & Partial<pdfSingleState>) => void // 更新附件信息
    removePdfs: (aid: string) => void // 移除附件
    setHighlights: (aid: string, newHighlights: CustomHighlight[]) => void // 初始化高亮
    addHighlights: (aid: string, newHighlights: CustomHighlight[]) => void // 追加高亮
    updateHighlights: (aid: string, newHighlight: Required<Pick<CustomHighlight, 'id'>> & Partial<CustomHighlight>) => void // 更新高亮
    removeHighlights: (aid: string, highlightId: string) => void // 移除高亮
    batchRemoveHighlights: (highlights: CustomHighlight[]) => void // 批量移除高亮
    setMode: (mode: number) => void // 设置操作模式
    setPdfHighlighterUtils: (aid: string, pdfHighlighterUtils: PdfHighlighterUtils) => void
    setActiveAid: (aid: string) => void
    setShowMenu: (showMenu: boolean) => void
    setTabItems: (tabItems: TabItem[]) => void
    addTabItems: (tabItems: TabItem[]) => void

    // 窗口管理方法
    createWindow: (tabs: TabItem[], position?: { x: number; y: number }) => string
    updateWindow: (windowId: string, updates: Partial<WindowState>) => void
    closeWindow: (windowId: string) => void
    addTabToWindow: (windowId: string, tab: TabItem) => void
    removeTabFromWindow: (windowId: string, tabKey: string) => void
    moveTabBetweenWindows: (sourceWindowId: string, targetWindowId: string, tabKey: string) => void
    mergeWindows: (targetWindowId: string, sourceWindowId: string) => void
    addWindowsContainer: (windowId: string, containerRect: DOMRect) => void
    removeWindowsContainer: (windowId: string) => void

    // 拖拽标签页
    setDragTabWindowId: (dragWindowId: string) => void,
    setDragTabSourceWindowId: (dragTabSourceWindowId: string) => void,
    setDragHoverWindowId: (dragHoverWindowId: string) => void,

    findFileLocation: (fileId: string) => { type: 'main' | 'panel' | 'window' | null, id: string | null }
    // 去重：应用启动后清理重复打开的同一文件
    dedupeOpenFiles?: () => void
}
const defaultPdfState: pdfSingleState = {
    aid: '',
    filename: '',
    url: '',
    scale: 1, // 设置默认缩放比例
    highlights: [], // 设置默认高亮数组
    pdfHighlighterUtils: null,
};

export const usePdfStore = createWithEqualityFn<pdfState>((set, get) => ({
    pdfs: new Map(),
    mode: 2,
    activeAid: '',
    showMenu: true,
    tabItems: [],
    defaultColor: '#FFE28F',
    selectionColor: '#FFE28F',


    // 窗口管理初始状态
    windows: new Map(),
    windowsContainer: new Map(),
    nextWindowZIndex: 2000, // 确保窗口z-index高于面板
    dragTabWindowId: "",
    dragTabSourceWindowId: "",
    dragHoverWindowId: "",
    dragHandled: false,

    setPdfs: (pdfs: Map<string, Partial<pdfSingleState>>) =>
        set(() => {
            const updatedPdfs = new Map<string, pdfSingleState>();
            pdfs.forEach((value, key) => {
                updatedPdfs.set(key, {
                    ...defaultPdfState,
                    ...value,
                });
            });
            return {
                pdfs: updatedPdfs
            };
        }),
    addPdfs: (pdfs: Map<string, Partial<pdfSingleState>>) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            pdfs.forEach((value, key) => {
                updatedPdfs.set(key, {
                    ...defaultPdfState,
                    ...value,
                });
            });
            return {
                pdfs: updatedPdfs
            };
        }),
    updatePdfs: (newPdf: Required<Pick<pdfSingleState, 'aid'>> & Partial<pdfSingleState>) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            const existingPdf = updatedPdfs.get(newPdf.aid);
            if (existingPdf) {
                updatedPdfs.set(newPdf.aid, {
                    ...existingPdf,
                    ...newPdf
                });
            }
            return {
                pdfs: updatedPdfs
            };
        }),
    removePdfs: (aid: string) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            updatedPdfs.delete(aid);
            return {
                pdfs: updatedPdfs
            };
        }),
    batchRemoveHighlights: (highlights: CustomHighlight[]) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            highlights.forEach(highlight => {
                const existingPdf = updatedPdfs.get(highlight.aid);
                if (existingPdf) {
                    updatedPdfs.set(highlight.aid, {
                        ...existingPdf,
                        highlights: existingPdf.highlights.filter(
                            item => item.id !== highlight.id
                        )
                    });
                }
            });
            return {
                pdfs: updatedPdfs
            };
        }),
    setHighlights: (aid: string, newHighlights: CustomHighlight[]) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            const existingPdf = updatedPdfs.get(aid);
            if (existingPdf) {
                updatedPdfs.set(aid, {
                    ...existingPdf,
                    highlights: newHighlights
                });
            }
            return {
                pdfs: updatedPdfs
            };
        }),
    addHighlights: (aid: string, newHighlights: CustomHighlight[]) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            const existingPdf = updatedPdfs.get(aid);
            if (existingPdf) {
                updatedPdfs.set(aid, {
                    ...existingPdf,
                    highlights: [
                        ...(existingPdf.highlights || []),
                        ...newHighlights
                    ]
                });
            }
            return {
                pdfs: updatedPdfs
            };
        }),
    updateHighlights: (aid: string, newHighlight: Required<Pick<CustomHighlight, 'id'>> & Partial<CustomHighlight>) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            const existingPdf = updatedPdfs.get(aid);
            if (existingPdf) {
                updatedPdfs.set(aid, {
                    ...existingPdf,
                    highlights: (existingPdf.highlights || []).map(
                        highlight =>
                            highlight.id === newHighlight.id ? { ...highlight, ...newHighlight } : highlight
                    )
                });
            }
            return {
                pdfs: updatedPdfs
            };
        }),
    removeHighlights: (aid: string, highlightId: string) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            const existingPdf = updatedPdfs.get(aid);
            if (existingPdf) {
                updatedPdfs.set(aid, {
                    ...existingPdf,
                    highlights: (existingPdf.highlights || []).filter(
                        highlight => highlight.id !== highlightId
                    )
                });
            }
            return {
                pdfs: updatedPdfs
            };
        }),
    setMode: (mode: number) => set({ mode, selectionColor: mode === 1 ? '#A8C7FA' : '#FFE28F' }),
    setPdfHighlighterUtils: (aid: string, pdfHighlighterUtils: PdfHighlighterUtils) =>
        set((state) => {
            const updatedPdfs = new Map(state.pdfs);
            const existingPdf = updatedPdfs.get(aid);
            if (existingPdf) {
                updatedPdfs.set(aid, {
                    ...existingPdf,
                    pdfHighlighterUtils
                });
            }
            return {
                pdfs: updatedPdfs
            };
        }),
    setActiveAid: (aid: string) => set({activeAid: aid}),
    setShowMenu: (showMenu: boolean) => set({showMenu}),
    setTabItems: (tabItems: TabItem[]) => set((state) => {
        // eslint-disable-next-line no-console
        // console.log('[Store:setTabItems] BEFORE', (state.tabItems || []).map(t => t.key), 'AFTER', (tabItems || []).map(t => t.key));
        return { tabItems };
    }),
    addTabItems: (tabItems: TabItem[]) => {
        const fileId = tabItems?.[0]?.key;
        if (!fileId) return;
        
        // 检查是否已存在于当前标签页
        const currentTabItems = get().tabItems || [];
        const isExist = currentTabItems.some((item: any) => item.key === fileId);
        if (isExist) {
            get().setActiveAid(fileId);
            return;
        }
        
        // 添加新标签
        set((state) => ({
            tabItems: [...(state.tabItems || []), ...(tabItems || [])]
        }));
        get().setActiveAid(fileId);
    },

    // 窗口管理方法实现
    createWindow: (tabs: TabItem[], position = {x: 100, y: 100}) => {
        const windowId = `${Date.now()}`;
        const newWindow: WindowState = {
            id: windowId,
            title: tabs.length > 0 ? String(tabs[0].label) : '新窗口',
            tabs,
            activeTabKey: tabs.length > 0 ? tabs[0].key : '',
            position,
            size: {width: 800, height: 600 - 35},
            isMinimized: false,
            zIndex: Math.floor(Date.now() / 1000)
        };
        set((state) => {
            const updatedWindows = new Map(state.windows);
            updatedWindows.set(windowId, newWindow);
            return {
                windows: updatedWindows,
                nextWindowZIndex: state.nextWindowZIndex + 1,
            };
        });

        return windowId;
    },

    updateWindow: (windowId: string, updates: Partial<WindowState>) =>
        set((state) => {
            const updatedWindows = new Map(state.windows);
            const existingWindow = updatedWindows.get(windowId);
            if (existingWindow) {
                updatedWindows.set(windowId, {
                    ...existingWindow,
                    ...updates,
                });
            }
            return {
                windows: updatedWindows,
            };
        }),

    closeWindow: (windowId: string) =>
        set((state) => {
            const updatedWindows = new Map(state.windows);
            updatedWindows.delete(windowId);
            state.removeWindowsContainer(windowId)
            return {
                windows: updatedWindows,
            };
        }),

    addTabToWindow: (windowId: string, tab: TabItem) =>
        set((state) => {
            const updatedWindows = new Map(state.windows);
            const existingWindow = updatedWindows.get(windowId);
            if (existingWindow) {
                const updatedTabs = [...existingWindow.tabs, tab];
                updatedWindows.set(windowId, {
                    ...existingWindow,
                    tabs: updatedTabs,
                    activeTabKey: tab.key,
                    title: String(tab.label),
                });
            }
            return {
                windows: updatedWindows,
            };
        }),

    removeTabFromWindow: (windowId: string, tabKey: string) =>
        set((state) => {
            const updatedWindows = new Map(state.windows);
            const existingWindow = updatedWindows.get(windowId);
            if (existingWindow) {
                const updatedTabs = existingWindow.tabs.filter(tab => tab.key !== tabKey);
                if (updatedTabs.length === 0) {
                    // 如果没有标签了，关闭窗口
                    updatedWindows.delete(windowId);
                } else {
                    const newActiveKey = existingWindow.activeTabKey === tabKey
                        ? updatedTabs[0].key
                        : existingWindow.activeTabKey;
                    updatedWindows.set(windowId, {
                        ...existingWindow,
                        tabs: updatedTabs,
                        activeTabKey: newActiveKey,
                        title: String(updatedTabs.find(tab => tab.key === newActiveKey)?.label || '窗口'),
                    });
                }
            }
            return {
                windows: updatedWindows,
            };
        }),

    moveTabBetweenWindows: (sourceWindowId: string, targetWindowId: string, tabKey: string) =>
        set((state) => {
            const updatedWindows = new Map(state.windows);
            const sourceWindow = updatedWindows.get(sourceWindowId);
            const targetWindow = updatedWindows.get(targetWindowId);

            if (sourceWindow && targetWindow) {
                const tabToMove = sourceWindow.tabs.find(tab => tab.key === tabKey);
                if (tabToMove) {
                    // 从源窗口移除标签
                    const updatedSourceTabs = sourceWindow.tabs.filter(tab => tab.key !== tabKey);
                    if (updatedSourceTabs.length === 0) {
                        updatedWindows.delete(sourceWindowId);
                    } else {
                        const newActiveKey = sourceWindow.activeTabKey === tabKey
                            ? updatedSourceTabs[0].key
                            : sourceWindow.activeTabKey;
                        updatedWindows.set(sourceWindowId, {
                            ...sourceWindow,
                            tabs: updatedSourceTabs,
                            activeTabKey: newActiveKey,
                        });
                    }

                    // 添加到目标窗口
                    const updatedTargetTabs = [...targetWindow.tabs, tabToMove];
                    updatedWindows.set(targetWindowId, {
                        ...targetWindow,
                        tabs: updatedTargetTabs,
                        activeTabKey: tabToMove.key,
                    });
                }
            }

            return {
                windows: updatedWindows,
            };
        }),

    mergeWindows: (targetWindowId: string, sourceWindowId: string) =>
        set((state) => {
            const updatedWindows = new Map(state.windows);
            const sourceWindow = updatedWindows.get(sourceWindowId);
            const targetWindow = updatedWindows.get(targetWindowId);

            if (sourceWindow && targetWindow && sourceWindowId !== targetWindowId) {
                // 将源窗口的所有标签添加到目标窗口
                const mergedTabs = [...targetWindow.tabs, ...sourceWindow.tabs];

                // 更新目标窗口
                updatedWindows.set(targetWindowId, {
                    ...targetWindow,
                    tabs: mergedTabs,
                    activeTabKey: sourceWindow.activeTabKey || targetWindow.activeTabKey,
                });

                // 删除源窗口
                updatedWindows.delete(sourceWindowId);

                console.log(`Merged window ${sourceWindowId} into ${targetWindowId}`);
            }

            return {
                windows: updatedWindows,
            };
        }),
    addWindowsContainer: (windowId: string, containerRect: DOMRect) =>
        set((state) => {
            return {
                windowsContainer: new Map(state.windowsContainer).set(windowId, containerRect)
            }
        }),
    removeWindowsContainer: (windowId: string) =>
        set((state) => {
            const updatedWindowsContainer = new Map(state.windowsContainer);
            updatedWindowsContainer.delete(windowId);
            return {
                windowsContainer: updatedWindowsContainer
            }
        }),

    setDragTabWindowId: (dragTabWindowId: string) => set(() => ({dragTabWindowId})),
    setDragTabSourceWindowId: (dragTabSourceWindowId: string) => set(() => ({dragTabSourceWindowId})),
    setDragHoverWindowId: (dragHoverWindowId: string) => set(() => ({dragHoverWindowId})),

    
    findFileLocation: (fileId: string) => {
        const state = get();
        
        // 检查主 Panel
        if (state.tabItems?.some(tab => tab.key === fileId)) {
            return { type: 'main' as const, id: 'main' };
        }
        
        // 检查所有浮动窗口
        for (const [windowId, window] of state.windows) {
            if (window.tabs.some(tab => tab.key === fileId)) {
                return { type: 'window' as const, id: windowId };
            }
        }
        
        // 检查所有 TabPanel（从 tab-panel-store 获取）
        const tabPanelStore = useTabPanelStore.getState();
        const tabPanelLocation = tabPanelStore.findFileLocation(fileId);
        if (tabPanelLocation.type === 'panel') {
            return { type: 'panel' as const, id: tabPanelLocation.id };
        }
        
        return { type: null, id: null };
    },

    // 启动时去重：同一 aid 仅保留一份，优先保留当前激活的那份
    dedupeOpenFiles: () => {
        const s = get();

        type Entry = {
            aid: string;
            container: 'main' | 'window';
            id: string; // main | windowId
            isActive: boolean;
            index: number; // 容器内顺序，兜底用
        };

        const entries: Entry[] = [];

        // main
        (s.tabItems || []).forEach((t, idx) => entries.push({
            aid: t.key,
            container: 'main',
            id: 'main',
            isActive: s.activeAid === t.key,
            index: idx,
        }));


        // windows
        s.windows.forEach((w) => {
            w.tabs.forEach((t, idx) => entries.push({
                aid: t.key,
                container: 'window',
                id: w.id,
                isActive: w.activeTabKey === t.key,
                index: idx,
            }));
        });

        const byAid = new Map<string, Entry[]>();
        for (const e of entries) {
            const arr = byAid.get(e.aid) || [];
            arr.push(e);
            byAid.set(e.aid, arr);
        }

        byAid.forEach((group, aid) => {
            if (group.length <= 1) return;

            // 选择保留项：优先 isActive，其次按原顺序
            const winner = group.sort((a, b) => Number(b.isActive) - Number(a.isActive) || a.index - b.index)[0];

            group.forEach((e) => {
                if (e === winner) return;
                switch (e.container) {
                    case 'main': {
                        const items = (get().tabItems || []).filter((t) => t.key !== e.aid);
                        set({ tabItems: items });
                        if (get().activeAid === e.aid) {
                            set({ activeAid: items[0]?.key || '' });
                        }
                        break;
                    }
                    case 'window': {
                        get().removeTabFromWindow(e.id, e.aid);
                        break;
                    }
                }
            });

            // 诊断日志（一次性）
            // eslint-disable-next-line no-console
            // console.log('[FileUnique:dedupe]', {
            //     aid,
            //     kept: { container: winner.container, id: winner.id },
            //     removed: group.filter((x) => x !== winner).map((x) => ({ container: x.container, id: x.id })),
            // });
        });
    },
}), shallow);

// pdf基础信息比较器
export const comparePdfBasicInfo = (a: Map<string, pdfSingleState>, b: Map<string, pdfSingleState>) => {
    if (a.size !== b.size) return false;

    return Array.from(a.keys()).every(key => {
        const pdfA = a.get(key);
        const pdfB = b.get(key);
        return !(!pdfA || !pdfB);
    });
};
// pdf基础信息选择器
export const selectPdfBasicInfo = (state: pdfState) => state.pdfs;

// 获取节点高亮
export const getNodeHighlight = (nodeIds: string[]): CustomHighlight[] => {
    const highlights: CustomHighlight[] = [];
    for (const pdf of usePdfStore.getState().pdfs.values()) {
        if (pdf.highlights) {
            highlights.push(...pdf.highlights.filter((highlight) => nodeIds.includes(highlight.nid)));
        }
    }
    return highlights;
};

export const getNodeHighlightWithPdf = (nodeIds: string): pdfSingleState | undefined => {
    let targetPdf: pdfSingleState | undefined;
    for (const pdf of usePdfStore.getState().pdfs.values()) {
        if (pdf.highlights) {
            pdf.highlights.find((e) => {
                if (e.nid === nodeIds) {
                    targetPdf = pdf
                }
            })
        }
    }

    return targetPdf;
};

// 打开PDF标签页
export const openPdfTab = (aid: string) => {
    const state = usePdfStore.getState();
    
    // 查找文件所在位置
    const location = state.findFileLocation(aid);
    
    if (location.type === 'main') {
        // 文件在主Panel中，直接激活
        const pdfPanelState = document.querySelector('.PdfPanel');
        if (pdfPanelState) {
            const event = new CustomEvent('openPdfTab', { detail: { aid } });
            pdfPanelState.dispatchEvent(event);
        }
    } else if (location.type === 'panel' && location.id) {
        // 文件在独立Panel中
        // 先显示对应的Panel（通过查找具有特定data-panel-id的元素）
        const panelElement = document.querySelector(`[data-panel-id="${location.id}"]`);
        
        if (panelElement) {
            // 如果Panel是隐藏的，需要先显示它
            const panelContainer = panelElement.closest('.pdf-panel-container');
            if (panelContainer && panelContainer.classList.contains('hidden')) {
                // 触发显示Panel的事件
                const showEvent = new CustomEvent('showPdfPanel', { 
                    detail: { panelId: location.id },
                    bubbles: true 
                });
                document.dispatchEvent(showEvent);
            }
            
            // 然后激活标签页
            setTimeout(() => {
                const event = new CustomEvent('openPdfTab', { detail: { aid, panelId: location.id } });
                panelElement.dispatchEvent(event);
            }, 100);
        }
    } else if (location.type === 'window' && location.id) {
        // 文件在浮动窗口中
        // 激活窗口并切换到对应标签
        const windowElement = document.querySelector(`[data-window-id="${location.id}"]`);
        if (windowElement) {
            // 将窗口置顶
            state.updateWindow(location.id, { zIndex: state.nextWindowZIndex });
            // 激活标签页
            const event = new CustomEvent('openPdfTab', { detail: { aid, windowId: location.id } });
            windowElement.dispatchEvent(event);
        }
    } else {
        // 文件不存在于任何位置，打开到主Panel
        const pdfPanelState = document.querySelector('.PdfPanel');
        if (pdfPanelState) {
            // 先添加标签页
            const pdf = state.pdfs.get(aid);
            if (pdf) {
                state.addTabItems([{
                    label: pdf.filename,
                    key: aid,
                    children: null
                }]);
                // 然后激活
                const event = new CustomEvent('openPdfTab', { detail: { aid } });
                pdfPanelState.dispatchEvent(event);
            }
        }
    }
};
