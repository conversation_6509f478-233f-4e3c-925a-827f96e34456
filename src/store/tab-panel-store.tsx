import { extractLabelText } from "@/components/pdf/components/draggable-tabs/hooks/drag-tab";
import { SerializableTabItem, TabItem } from "@/components/pdf/components/draggable-tabs/types";
import { PanelPosition } from "@/pages/workspace/Panel";
import { useWorkerSpaceStore } from "@/store/workerspace-store/store";
import { devtools } from "zustand/middleware";
import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";

// TabPanel 基础信息（UI层面）
export type TabPanelInfo = {
  id: string;
  label: string;
  aid: string;  // attachment id
  projectId?: string; // 绑定所属项目（workspace id）
  position: PanelPosition;
};

// TabPanel 内容状态（业务层面）
export type TabPanelContentState = {
  panelId: string;
  tabItems: TabItem[];
  activeAid: string;
  mode?: number; // 操作模式（可选，不设置则使用全局模式）
  showMenu?: boolean; // 菜单显示状态
};

// TabPanel 文件列表持久化状态（只保存可序列化的数据）
export interface TabPanelTabsState {
  panelId: string;
  tabItems: SerializableTabItem[];  // 使用明确的可序列化类型
  activeAid: string;
}

type TabPanelStoreType = {
  // === 基础面板管理 ===
  // UI 层面的面板信息
  tabPanels: Map<string, TabPanelInfo>;
  
  // 面板显示状态
  hiddenTabPanels: Set<string>;
  
  // === 内容状态管理 ===
  // 业务层面的面板内容状态
  panelContents: Map<string, TabPanelContentState>;
  
  // 持久化的文件列表数据
  panelTabsData: Map<string, TabPanelTabsState>;
  
  // === 基础面板操作 ===
  addTabPanel: (panel: TabPanelInfo) => void;
  removeTabPanel: (panelId: string) => void;
  updateTabPanelPosition: (panelId: string, position: PanelPosition) => void;
  
  // === 显示状态管理 ===
  toggleTabPanelVisibility: (panelId: string) => void;
  hideTabPanels: (panelIds: string[]) => void;
  showTabPanels: (panelIds: string[]) => void;
  clearHiddenTabPanels: () => void;
  
  // === 内容状态管理 ===
  createPanelContent: (panelId: string, tabItems: TabItem[], activeAid: string, mode?: number) => void;
  updatePanelContent: (panelId: string, updates: Partial<TabPanelContentState>) => void;
  removePanelContent: (panelId: string) => void;
  setPanelActiveAid: (panelId: string, aid: string) => void;
  setPanelTabItems: (panelId: string, tabItems: TabItem[]) => void;
  setPanelMode: (panelId: string, mode: number) => void;
  addPanelTabItem: (panelId: string, tabItem: TabItem) => void;
  removePanelTabItem: (panelId: string, tabKey: string) => void;
  
  // === 持久化管理 ===
  savePanelTabs: (panelId: string, tabItems: TabItem[], activeAid: string) => void;
  loadPanelTabs: (panelId: string) => TabPanelTabsState | null;
  removePanelTabsData: (panelId: string) => void;
  cleanupOrphanedPanelTabs: (validPanelIds: Set<string>) => void;
  
  // === 查询方法 ===
  findFileLocation: (fileId: string) => { type: 'main' | 'panel' | null, id: string | null };
  getPanelsByProject: (projectId?: string) => TabPanelInfo[];
  getPanelContent: (panelId: string) => TabPanelContentState | undefined;
  
  // === 数据清理 ===
  cleanInvalidTabData: () => void;
};

// 项目感知的持久化管理器（专门用于 TabPanel）
class TabPanelPersist {
  private currentWid: string | null = null;
  private storeName = 'tab-panel-storage';
  private saveTimer: NodeJS.Timeout | null = null;
  private pendingState: any = null;
  
  // 兼容旧的 panel-open-store 存储键
  private legacyStoreName = 'panel-persist-storage';
  
  private getStorageKey(): string {
    return this.currentWid ? `${this.storeName}-${this.currentWid}` : `${this.storeName}-default`;
  }
  
  updateProjectId(newWid: string | null): void {
    if (newWid === this.currentWid) return;
    this.currentWid = newWid;
    
    if (newWid) {
      const restoredState = this.loadProjectData();
      if (restoredState) {
        this.setStoreState?.(restoredState);
      }
    }
  }
  
  loadProjectData(): any | null {
    const storageKey = this.getStorageKey();
    let str = localStorage.getItem(storageKey);
    
    // 如果新存储不存在，尝试从旧的 panel-open-store 迁移数据
    if (!str) {
      const legacyKey = this.currentWid ? `${this.legacyStoreName}-${this.currentWid}` : `${this.legacyStoreName}-default`;
      const legacyStr = localStorage.getItem(legacyKey);
      if (legacyStr) {
        try {
          const legacyData = JSON.parse(legacyStr);
          if (legacyData.state) {
            // 迁移数据并保存到新位置
            const migratedState = this.migrateLegacyData(legacyData.state);
            this.saveState(migratedState);
            return migratedState;
          }
        } catch (error) {
          console.error('Failed to migrate legacy TabPanel data:', error);
        }
      }
    }
    
    if (str) {
      try {
        const data = JSON.parse(str);
        if (data.state) {
          return this.deserializeState(data.state);
        }
      } catch (error) {
        console.error('Failed to load TabPanel data:', error);
      }
    }
    return null;
  }
  
  // 迁移旧的 panel-open-store 数据
  private migrateLegacyData(legacyState: any): any {
    const migratedState: any = {
      tabPanels: new Map(),
      hiddenTabPanels: new Set(),
      panelContents: new Map(),
      panelTabsData: new Map()
    };
    
    // 迁移 tabPanels
    if (legacyState.tabPanels) {
      if (legacyState.tabPanels instanceof Map) {
        migratedState.tabPanels = new Map(legacyState.tabPanels);
      } else if (typeof legacyState.tabPanels === 'object') {
        migratedState.tabPanels = new Map(Object.entries(legacyState.tabPanels));
      }
    }
    
    // 迁移 hiddenTabPanels
    if (legacyState.hiddenTabPanels) {
      if (legacyState.hiddenTabPanels instanceof Set) {
        migratedState.hiddenTabPanels = new Set(legacyState.hiddenTabPanels);
      } else if (Array.isArray(legacyState.hiddenTabPanels)) {
        migratedState.hiddenTabPanels = new Set(legacyState.hiddenTabPanels);
      }
    }
    
    // 迁移 panelTabsData
    if (legacyState.panelTabsData) {
      if (legacyState.panelTabsData instanceof Map) {
        migratedState.panelTabsData = new Map(legacyState.panelTabsData);
      } else if (typeof legacyState.panelTabsData === 'object') {
        migratedState.panelTabsData = new Map(Object.entries(legacyState.panelTabsData));
      }
    }
    
    return migratedState;
  }
  
  private deserializeState(state: any): any {
    const result = { ...state };
    
    // 处理 tabPanels Map
    if (state.tabPanels && typeof state.tabPanels === 'object') {
      const panelsEntries: Array<[string, TabPanelInfo]> = Object.entries(state.tabPanels).map(([key, panel]: [string, any]) => {
        const validPanel: TabPanelInfo = {
          id: panel.id || key,
          label: panel.label || 'Tab',
          aid: panel.aid || '',
          projectId: panel.projectId,
          position: panel.position || {
            x: 100, y: 100, width: 800, height: 600, isSnapped: false
          }
        };
        return [key, validPanel];
      });
      result.tabPanels = new Map<string, TabPanelInfo>(panelsEntries);
    } else {
      result.tabPanels = new Map();
    }
    
    // 处理 hiddenTabPanels Set
    if (Array.isArray(state.hiddenTabPanels)) {
      result.hiddenTabPanels = new Set(state.hiddenTabPanels);
    } else {
      result.hiddenTabPanels = new Set();
    }
    
    // 处理 panelContents Map - 确保 tabItems 中的 label 是字符串
    if (state.panelContents && typeof state.panelContents === 'object') {
      const contentsEntries: Array<[string, TabPanelContentState]> = Object.entries(state.panelContents).map(([key, content]: [string, any]) => {
        // 清理 tabItems 中的 label，不恢复 children（会在运行时重新创建）
        const cleanedContent: TabPanelContentState = {
          ...content,
          tabItems: (content.tabItems || []).map((item: any) => ({
            key: item.key,
            label: extractLabelText(item.label),
            closable: item.closable
            // 不恢复 children，因为 React 元素不能被序列化
          }))
        };
        return [key, cleanedContent];
      });
      result.panelContents = new Map<string, TabPanelContentState>(contentsEntries);
    } else {
      result.panelContents = new Map();
    }
    
    // 处理 panelTabsData Map - 确保 label 是字符串
    if (state.panelTabsData && typeof state.panelTabsData === 'object') {
      const tabsDataEntries: Array<[string, TabPanelTabsState]> = Object.entries(state.panelTabsData).map(([key, data]: [string, any]) => {
        // 清理每个 tabItem 的 label，确保是字符串
        const cleanedData: TabPanelTabsState = {
          ...data,
          tabItems: (data.tabItems || []).map((item: any) => ({
            key: item.key,
            label: extractLabelText(item.label),
            closable: item.closable
          }))
        };
        return [key, cleanedData];
      });
      result.panelTabsData = new Map<string, TabPanelTabsState>(tabsDataEntries);
    } else {
      result.panelTabsData = new Map();
    }
    
    return result;
  }
  
  serializeState(state: any): any {
    // 清理 panelContents，移除 children 属性
    let cleanedPanelContents = {};
    if (state.panelContents) {
      const entries: Array<[string, any]> = Array.from(state.panelContents.entries());
      cleanedPanelContents = Object.fromEntries(
        entries.map(([key, content]) => [
          key,
          {
            ...content,
            tabItems: content.tabItems.map((item: any) => ({
              key: item.key,
              label: extractLabelText(item.label),
              closable: item.closable
              // 不序列化 children
            }))
          }
        ])
      );
    }
    
    return {
      ...state,
      tabPanels: state.tabPanels ? Object.fromEntries(state.tabPanels) : {},
      hiddenTabPanels: state.hiddenTabPanels ? Array.from(state.hiddenTabPanels) : [],
      panelContents: cleanedPanelContents,
      panelTabsData: state.panelTabsData ? Object.fromEntries(state.panelTabsData) : {}
    };
  }
  
  saveState(state: any): void {
    if (!this.currentWid) return;
    
    // 保存待处理的状态
    this.pendingState = state;
    
    // 清除之前的定时器
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
    }
    
    // 设置新的定时器，延迟 300ms 执行保存
    this.saveTimer = setTimeout(() => {
      if (this.pendingState) {
        const storageKey = this.getStorageKey();
        const serializedState = this.serializeState(this.pendingState);
        
        try {
          localStorage.setItem(storageKey, JSON.stringify({
            state: serializedState,
            version: 1
          }));
        } catch (error) {
          console.error('Failed to save TabPanel state:', error);
        }
        
        this.pendingState = null;
      }
      this.saveTimer = null;
    }, 300); // 300ms 防抖延迟
  }
  
  // 立即保存方法（用于关键操作）
  saveStateImmediate(state: any): void {
    if (!this.currentWid) return;
    
    // 清除待处理的保存
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
      this.saveTimer = null;
    }
    
    const storageKey = this.getStorageKey();
    const serializedState = this.serializeState(state);
    
    try {
      localStorage.setItem(storageKey, JSON.stringify({
        state: serializedState,
        version: 1
      }));
    } catch (error) {
      console.error('Failed to save TabPanel state:', error);
    }
    
    this.pendingState = null;
  }
  
  setStoreState?: (state: any) => void;
}

// 创建持久化管理器实例
const tabPanelPersist = new TabPanelPersist();

export const useTabPanelStore = createWithEqualityFn<TabPanelStoreType>()(
  devtools(
    (set, get) => ({
    // === 初始状态 ===
    tabPanels: new Map<string, TabPanelInfo>(),
    hiddenTabPanels: new Set<string>(),
    panelContents: new Map<string, TabPanelContentState>(),
    panelTabsData: new Map<string, TabPanelTabsState>(),
    
    // === 基础面板操作 ===
    addTabPanel: (panel: TabPanelInfo) =>
      set((state) => {
        const newTabPanels = new Map(state.tabPanels);
        newTabPanels.set(panel.id, panel);
        return { tabPanels: newTabPanels };
      }),
    
    removeTabPanel: (panelId: string) =>
      set((state) => {
        const newTabPanels = new Map(state.tabPanels);
        const newHiddenTabPanels = new Set(state.hiddenTabPanels);
        const newPanelContents = new Map(state.panelContents);
        const newPanelTabsData = new Map(state.panelTabsData);
        
        newTabPanels.delete(panelId);
        newHiddenTabPanels.delete(panelId);
        newPanelContents.delete(panelId);
        newPanelTabsData.delete(panelId);
        
        return {
          tabPanels: newTabPanels,
          hiddenTabPanels: newHiddenTabPanels,
          panelContents: newPanelContents,
          panelTabsData: newPanelTabsData
        };
      }),
    
    updateTabPanelPosition: (panelId: string, position: PanelPosition) =>
      set((state) => {
        const newTabPanels = new Map(state.tabPanels);
        const panel = newTabPanels.get(panelId);
        if (panel) {
          newTabPanels.set(panelId, { ...panel, position });
        }
        return { tabPanels: newTabPanels };
      }),
    
    // === 显示状态管理 ===
    toggleTabPanelVisibility: (panelId: string) =>
      set((state) => {
        const newHiddenTabPanels = new Set(state.hiddenTabPanels);
        if (newHiddenTabPanels.has(panelId)) {
          newHiddenTabPanels.delete(panelId);
        } else {
          newHiddenTabPanels.add(panelId);
        }
        return { hiddenTabPanels: newHiddenTabPanels };
      }),
    
    hideTabPanels: (panelIds: string[]) =>
      set((state) => {
        const newHiddenTabPanels = new Set(state.hiddenTabPanels);
        panelIds.forEach(id => newHiddenTabPanels.add(id));
        return { hiddenTabPanels: newHiddenTabPanels };
      }),
    
    showTabPanels: (panelIds: string[]) =>
      set((state) => {
        const newHiddenTabPanels = new Set(state.hiddenTabPanels);
        panelIds.forEach(id => newHiddenTabPanels.delete(id));
        return { hiddenTabPanels: newHiddenTabPanels };
      }),
    
    clearHiddenTabPanels: () =>
      set({ hiddenTabPanels: new Set() }),
    
    // === 内容状态管理 ===
    createPanelContent: (panelId: string, tabItems: TabItem[], activeAid: string, mode?: number) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        newPanelContents.set(panelId, {
          panelId,
          tabItems,  // 直接使用传入的 tabItems，不在这里处理
          activeAid,
          mode,
          showMenu: false
        });
        
        // 同时保存到持久化存储（savePanelTabs 会处理序列化）
        get().savePanelTabs(panelId, tabItems, activeAid);
        
        return { panelContents: newPanelContents };
      }),
    
    updatePanelContent: (panelId: string, updates: Partial<TabPanelContentState>) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        const content = newPanelContents.get(panelId);
        if (content) {
          const updatedContent = { ...content, ...updates };
          newPanelContents.set(panelId, updatedContent);
          
          // 如果更新了文件列表或激活状态，持久化
          if (updates.tabItems !== undefined || updates.activeAid !== undefined) {
            get().savePanelTabs(panelId, updatedContent.tabItems, updatedContent.activeAid);
          }
        }
        return { panelContents: newPanelContents };
      }),
    
    removePanelContent: (panelId: string) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        newPanelContents.delete(panelId);
        get().removePanelTabsData(panelId);
        return { panelContents: newPanelContents };
      }),
    
    setPanelActiveAid: (panelId: string, aid: string) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        const content = newPanelContents.get(panelId);
        if (content) {
          const updatedContent = { ...content, activeAid: aid };
          newPanelContents.set(panelId, updatedContent);
          get().savePanelTabs(panelId, content.tabItems, aid);
        }
        return { panelContents: newPanelContents };
      }),
    
    setPanelTabItems: (panelId: string, tabItems: TabItem[]) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        const content = newPanelContents.get(panelId);
        if (content) {
          const updatedContent = { ...content, tabItems };
          newPanelContents.set(panelId, updatedContent);
          get().savePanelTabs(panelId, tabItems, content.activeAid);
        }
        return { panelContents: newPanelContents };
      }),
    
    setPanelMode: (panelId: string, mode: number) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        const content = newPanelContents.get(panelId);
        if (content) {
          newPanelContents.set(panelId, { ...content, mode });
        }
        return { panelContents: newPanelContents };
      }),
    
    addPanelTabItem: (panelId: string, tabItem: TabItem) => {
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        const content = newPanelContents.get(panelId);
        if (content) {
          // 检查是否已存在
          if (content.tabItems.some(tab => tab.key === tabItem.key)) {
            return { panelContents: state.panelContents };
          }
          
          const updatedTabItems = [...content.tabItems, tabItem];
          const updatedContent = { ...content, tabItems: updatedTabItems, activeAid: tabItem.key };
          newPanelContents.set(panelId, updatedContent);
          
          get().savePanelTabs(panelId, updatedTabItems, tabItem.key);
        }
        return { panelContents: newPanelContents };
      });
    },
    
    removePanelTabItem: (panelId: string, tabKey: string) =>
      set((state) => {
        const newPanelContents = new Map(state.panelContents);
        const content = newPanelContents.get(panelId);
        if (content) {
          const updatedTabItems = content.tabItems.filter(tab => tab.key !== tabKey);
          if (updatedTabItems.length > 0) {
            const newActiveAid = content.activeAid === tabKey ? updatedTabItems[0].key : content.activeAid;
            const updatedContent = { ...content, tabItems: updatedTabItems, activeAid: newActiveAid };
            newPanelContents.set(panelId, updatedContent);
            get().savePanelTabs(panelId, updatedTabItems, newActiveAid);
          } else {
            newPanelContents.delete(panelId);
            get().removePanelTabsData(panelId);
          }
        }
        return { panelContents: newPanelContents };
      }),
    
    // === 持久化管理 ===
    savePanelTabs: (panelId: string, tabItems: TabItem[], activeAid: string) =>
      set((state) => {
        const newPanelTabsData = new Map(state.panelTabsData);
        // 这是唯一负责序列化的地方：转换为可序列化格式
        const serializableTabItems: SerializableTabItem[] = tabItems.map(tab => ({
          key: tab.key,
          label: extractLabelText(tab.label),  // 确保 label 是字符串
          closable: tab.closable
          // 不包含 children，因为不能被序列化
        }));
        
        newPanelTabsData.set(panelId, {
          panelId,
          tabItems: serializableTabItems,
          activeAid
        });
        return { panelTabsData: newPanelTabsData };
      }),
    
    loadPanelTabs: (panelId: string): TabPanelTabsState | null => {
      const state = get();
      return state.panelTabsData.get(panelId) || null;
    },
    
    removePanelTabsData: (panelId: string) =>
      set((state) => {
        const newPanelTabsData = new Map(state.panelTabsData);
        newPanelTabsData.delete(panelId);
        return { panelTabsData: newPanelTabsData };
      }),
    
    cleanupOrphanedPanelTabs: (validPanelIds: Set<string>) =>
      set((state) => {
        const newPanelTabsData = new Map<string, TabPanelTabsState>();
        state.panelTabsData.forEach((data, panelId) => {
          if (validPanelIds.has(panelId)) {
            // 清理数据中可能存在的无效children，确保label是字符串
            const cleanedData = {
              ...data,
              tabItems: data.tabItems.map(item => ({
                key: item.key,
                label: extractLabelText(item.label),
                closable: item.closable
                // 确保不包含children
              }))
            };
            newPanelTabsData.set(panelId, cleanedData);
          }
        });
        return { panelTabsData: newPanelTabsData };
      }),
    
    // === 查询方法 ===
    findFileLocation: (fileId: string) => {
      const state = get();
      
      // 检查所有 TabPanel
      for (const [panelId, content] of state.panelContents) {
        if (content.tabItems.some(tab => tab.key === fileId)) {
          return { type: 'panel' as const, id: panelId };
        }
      }
      
      return { type: null, id: null };
    },
    
    getPanelsByProject: (projectId?: string) => {
      const state = get();
      return Array.from(state.tabPanels.values()).filter(panel => 
        !panel.projectId || panel.projectId === projectId
      );
    },
    
    getPanelContent: (panelId: string) => {
      const state = get();
      return state.panelContents.get(panelId);
    },
    
    // === 数据清理 ===
    cleanInvalidTabData: () =>
      set((state) => {
        // 清理 panelContents 中可能包含无效children的数据
        const newPanelContents = new Map<string, TabPanelContentState>();
        state.panelContents.forEach((content, panelId) => {
          const cleanedContent = {
            ...content,
            tabItems: content.tabItems.map((item: any) => ({
              key: item.key,
              label: extractLabelText(item.label),
              closable: item.closable
              // 移除可能的无效children
            }))
          };
          newPanelContents.set(panelId, cleanedContent);
        });
        
        // 清理 panelTabsData 中的数据
        const newPanelTabsData = new Map<string, TabPanelTabsState>();
        state.panelTabsData.forEach((data, panelId) => {
          const cleanedData = {
            ...data,
            tabItems: data.tabItems.map(item => ({
              key: item.key,
              label: extractLabelText(item.label),
              closable: item.closable
            }))
          };
          newPanelTabsData.set(panelId, cleanedData);
        });
        
        return { 
          panelContents: newPanelContents,
          panelTabsData: newPanelTabsData 
        };
      })
    }),
    {
      name: 'tab-panel-store', // 在 Redux DevTools 中显示的名称
      enabled: process.env.NODE_ENV === 'development', // 只在开发环境启用
    }
  ),
    shallow
  );

// 连接持久化管理器
tabPanelPersist.setStoreState = useTabPanelStore.setState;

// 监听 workspace store 变化
useWorkerSpaceStore.subscribe((state) => {
  tabPanelPersist.updateProjectId(state.wid);
});

// 监听状态变化并自动保存
useTabPanelStore.subscribe((state) => {
  const {
    // 排除方法
    addTabPanel, removeTabPanel, updateTabPanelPosition,
    toggleTabPanelVisibility, hideTabPanels, showTabPanels, clearHiddenTabPanels,
    createPanelContent, updatePanelContent, removePanelContent,
    setPanelActiveAid, setPanelTabItems, setPanelMode, addPanelTabItem, removePanelTabItem,
    savePanelTabs, loadPanelTabs, removePanelTabsData, cleanupOrphanedPanelTabs,
    findFileLocation, getPanelsByProject, getPanelContent, cleanInvalidTabData,
    ...stateToSave
  } = state;
  
  tabPanelPersist.saveState(stateToSave);
});

// 开发环境下的状态变化日志记录
if (process.env.NODE_ENV === 'development') {
  let previousState = useTabPanelStore.getState();
  
  useTabPanelStore.subscribe((state) => {
    // 检查哪些字段发生了变化
    const changes: string[] = [];
    
    if (state.tabPanels.size !== previousState.tabPanels.size) {
      changes.push(`面板数量: ${previousState.tabPanels.size} → ${state.tabPanels.size}`);
    }
    
    if (state.hiddenTabPanels.size !== previousState.hiddenTabPanels.size) {
      changes.push(`隐藏面板数量: ${previousState.hiddenTabPanels.size} → ${state.hiddenTabPanels.size}`);
    }
    
    if (state.panelContents.size !== previousState.panelContents.size) {
      changes.push(`内容面板数量: ${previousState.panelContents.size} → ${state.panelContents.size}`);
    }
    
    if (state.panelTabsData.size !== previousState.panelTabsData.size) {
      changes.push(`持久化数据数量: ${previousState.panelTabsData.size} → ${state.panelTabsData.size}`);
    }
    
    if (changes.length > 0) {
      console.group('📋 [TabPanel] 状态变化');
      console.log('🕐 时间:', new Date().toLocaleTimeString());
      changes.forEach(change => console.log('📝', change));
      console.log('📊 当前状态概览:', {
        面板总数: state.tabPanels.size,
        隐藏面板: state.hiddenTabPanels.size,
        内容面板: state.panelContents.size,
        持久化数据: state.panelTabsData.size
      });
      console.groupEnd();
    }
    
    previousState = state;
  });
}

// 导出持久化管理器
export { tabPanelPersist };
