import { createWithEqualityFn } from "zustand/traditional";
import { shallow } from "zustand/shallow";
import { persist } from "zustand/middleware";

// 输入参数项类型
export interface InputParam {
  id: string;
  name: string; // 参数名(必填)
  value: string; // 参数值(可选)
}

// 输出参数项类型
export interface OutputParam {
  id: string;
  outputName: string; // 输出名(必填)
  alias: string; // 别名(可选)
}

// LLM 配置数据类型
export interface LLMConfig {
  inputParams: InputParam[];
  outputParams: OutputParam[];
  userPrompt: string;
  systemPrompt: string;
}

// Store 类型定义
type LLMConfigStore = {
  // 状态
  inputParams: InputParam[];
  outputParams: OutputParam[];
  userPrompt: string;
  systemPrompt: string;
  
  // 输入参数操作
  addInputParam: () => void;
  removeInputParam: (id: string) => void;
  updateInputParam: (id: string, field: keyof InputParam, value: string) => void;
  
  // 输出参数操作
  addOutputParam: () => void;
  removeOutputParam: (id: string) => void;
  updateOutputParam: (id: string, field: keyof OutputParam, value: string) => void;
  
  // Prompt 操作
  setUserPrompt: (prompt: string) => void;
  setSystemPrompt: (prompt: string) => void;
  
  // 重置所有配置
  resetConfig: () => void;
  
  // 获取完整配置
  getConfig: () => LLMConfig;
};

// 生成唯一ID
const generateId = () => Date.now().toString();

// 默认配置
const defaultConfig = {
  inputParams: [{ id: "1", name: "", value: "" }],
  outputParams: [{ id: "1", outputName: "", alias: "" }],
  userPrompt: "",
  systemPrompt: "",
};

export const useLLMConfigStore = createWithEqualityFn<
  LLMConfigStore,
  [["zustand/persist", LLMConfigStore]]
>(
  persist(
    (set, get) => ({
      // 初始状态
      ...defaultConfig,
      
      // 输入参数操作
      addInputParam: () =>
        set((state) => ({
          inputParams: [
            ...state.inputParams,
            { id: generateId(), name: "", value: "" },
          ],
        })),
        
      removeInputParam: (id: string) =>
        set((state) => ({
          inputParams: state.inputParams.filter((param) => param.id !== id),
        })),
        
      updateInputParam: (id: string, field: keyof InputParam, value: string) =>
        set((state) => ({
          inputParams: state.inputParams.map((param) =>
            param.id === id ? { ...param, [field]: value } : param
          ),
        })),
        
      // 输出参数操作
      addOutputParam: () =>
        set((state) => ({
          outputParams: [
            ...state.outputParams,
            { id: generateId(), outputName: "", alias: "" },
          ],
        })),
        
      removeOutputParam: (id: string) =>
        set((state) => ({
          outputParams: state.outputParams.filter((param) => param.id !== id),
        })),
        
      updateOutputParam: (id: string, field: keyof OutputParam, value: string) =>
        set((state) => ({
          outputParams: state.outputParams.map((param) =>
            param.id === id ? { ...param, [field]: value } : param
          ),
        })),
        
      // Prompt 操作
      setUserPrompt: (prompt: string) =>
        set(() => ({
          userPrompt: prompt,
        })),
        
      setSystemPrompt: (prompt: string) =>
        set(() => ({
          systemPrompt: prompt,
        })),
        
      // 重置所有配置
      resetConfig: () =>
        set(() => ({
          ...defaultConfig,
        })),
        
      // 获取完整配置
      getConfig: (): LLMConfig => {
        const state = get();
        return {
          inputParams: state.inputParams,
          outputParams: state.outputParams,
          userPrompt: state.userPrompt,
          systemPrompt: state.systemPrompt,
        };
      },
    }),
    {
      name: "llm-config-storage",
    }
  ),
  shallow
);