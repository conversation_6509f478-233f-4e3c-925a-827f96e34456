import { create } from "zustand";

// 资源类型定义
export interface Resource {
  id: string;
  name: string;
  type: string;
  url?: string;
  size?: number;
  createdAt: Date;
  updatedAt: Date;
}

// 资源管理 store 接口
interface ResourceStore {
  // 资源列表
  resources: any[];

  // 清空所有资源
  clearResources: () => void;

  // 设置资源列表
  setResources: (resources: any[]) => void;
}

// 创建资源管理 store
export const useResourceStore = create<ResourceStore>((set, get) => ({
  // 初始状态
  resources: [],

  // 清空所有资源
  clearResources: () => {
    set({ resources: [] });
  },

  // 设置资源列表
  setResources: (resources) => {
    set({ resources });
  },
}));

// 导出便捷的 hooks
export const useResources = () => useResourceStore((state) => state.resources);
export const useClearResources = () =>
  useResourceStore((state) => state.clearResources);
export const useSetResources = () =>
  useResourceStore((state) => state.setResources);
