import { createWithEqualityFn } from "zustand/traditional";
import { shallow } from "zustand/shallow";
import { PanelPosition } from "@/pages/workspace/Panel";
import { useWorkerSpaceStore } from "@/store/workerspace-store/store";
import { SetStateAction } from "react";
import { ResizeHandle } from "react-resizable";


type PanelOpenType = {
  // 面板开关状态
  pdfPanelOpen: boolean;
  chatPanelOpen: boolean;
  notePanelOpen: boolean;
  tagPanelOpen: boolean;
  cardListPanelOpen: boolean;
  llmPanelOpen: boolean;
  // 面板位置状态
  notePanelPosition: PanelPosition;
  pdfPanelPosition: PanelPosition;
  chatPanelPosition: PanelPosition;
  tagPanelPosition: PanelPosition;
  togglePdfPanel: (open: boolean) => void;
  toggleChatPanel: (open: boolean) => void;
  toggleNotePanel: (open: boolean) => void;
  toggleTagPanel: (open: boolean) => void;
  toggleCardListPanel: (open: boolean) => void;
  toggleLLMPanel: (open: boolean) => void;
  // 面板位置管理方法
  setNotePanelPosition: (position: SetStateAction<PanelPosition>) => void;
  setPdfPanelPosition: (position: SetStateAction<PanelPosition>) => void;
  setChatPanelPosition: (position: SetStateAction<PanelPosition>) => void;
  setTagPanelPosition: (position: SetStateAction<PanelPosition>) => void;
  resetPositionsToDefaults: () => void;
};

// 默认位置配置
const SIDEBAR_WIDTH = 64;

const getDefaultPositions = () => ({
  notePanelPosition: {
    x: 0,
    y: 0,
    width: window.innerWidth / 3,
    height: window.innerHeight,
    isSnapped: false,
    resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'] as ResizeHandle[],
  },
  pdfPanelPosition: {
    x: (window.innerWidth - SIDEBAR_WIDTH) * 2 / 3,
    y: 0,
    width: (window.innerWidth - SIDEBAR_WIDTH) / 3,
    height: window.innerHeight,
    isSnapped: false,
    resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'] as ResizeHandle[],
  },
  chatPanelPosition: {
    x: (window.innerWidth - SIDEBAR_WIDTH) * 0.5,
    y: 0,
    width: (window.innerWidth - SIDEBAR_WIDTH) / 4,
    height: window.innerHeight,
    isSnapped: true,
    resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'] as ResizeHandle[],
  },
  tagPanelPosition: {
    x: window.innerWidth - 300,
    y: 0,
    width: 300,
    height: window.innerHeight,
    isSnapped: false,
    resizeHandles: ['n', 's', 'w', 'e', 'nw', 'sw', 'ne', 'se'] as ResizeHandle[],
  }
});

// 项目感知的持久化管理器
class ProjectAwarePersist {
  private currentWid: string | null = null;
  private storeName = 'panel-persist-storage';
  
  // 获取当前项目的存储键
  private getStorageKey(): string {
    return this.currentWid ? `${this.storeName}-${this.currentWid}` : `${this.storeName}-default`;
  }
  
  // 更新当前项目ID并迁移数据
  updateProjectId(newWid: string | null): void {
    if (newWid === this.currentWid) return;
    
    this.currentWid = newWid;
    
    // 如果存在当前项目的数据，重新加载
    if (newWid) {
      const restoredState = this.loadProjectData();
      if (restoredState) {
        // 使用外部传入的 setState 函数
        this.setStoreState?.(restoredState);
      }
    }
  }
  
  // 设置 store 的 setState 函数
  setStoreState?: (state: any) => void;
  
  // 加载当前项目的持久化数据
  loadProjectData(): any | null {
    const storageKey = this.getStorageKey();
    const str = localStorage.getItem(storageKey);
    
    if (str) {
      try {
        const data = JSON.parse(str);
        if (data.state) {
          // 恢复复杂数据结构
          const restoredState = this.deserializeState(data.state);
          return restoredState;
        }
      } catch (error) {
        console.error('Failed to load project data:', error);
      }
    }
    return null;
  }
  
  // 序列化状态用于存储
  serializeState(state: any): any {
    return { ...state };
  }
  
  // 反序列化存储的状态
  private deserializeState(state: any): any {
    return { ...state };
  }
  
  // 保存当前状态到对应项目的存储中
  saveState(state: any): void {
    if (!this.currentWid) return; // 没有项目ID时不保存
    
    const storageKey = this.getStorageKey();
    const serializedState = this.serializeState(state);
    
    try {
      localStorage.setItem(storageKey, JSON.stringify({
        state: serializedState,
        version: 6
      }));
    } catch (error) {
      console.error('Failed to save project state:', error);
    }
  }
}

// 创建全局持久化管理器实例
const projectPersist = new ProjectAwarePersist();

export const usePanelOpenStore = createWithEqualityFn<PanelOpenType>(
  (set) => ({
      // 面板开关状态
      pdfPanelOpen: true,
      chatPanelOpen: false,
      notePanelOpen: false,
      tagPanelOpen: false,
      cardListPanelOpen: false,
      llmPanelOpen: false,
      // 面板位置状态
      ...getDefaultPositions(),
      togglePdfPanel: (open: boolean) =>
        set(() => ({
          pdfPanelOpen: open,
        })),
      toggleChatPanel: (open: boolean) =>
        set(() => ({
          chatPanelOpen: open,
        })),
      toggleNotePanel: (open: boolean) =>
        set(() => ({
          notePanelOpen: open,
        })),
      toggleTagPanel: (open: boolean) => 
        set(() => ({
          tagPanelOpen: open,
        })),
      toggleCardListPanel: (open: boolean) => set(() => ({cardListPanelOpen: open})),
      toggleLLMPanel: (open: boolean) => set(() => ({llmPanelOpen: open})),
      
      // 面板位置管理方法
      setNotePanelPosition: (position: SetStateAction<PanelPosition>) =>
        set((state) => ({
          notePanelPosition: typeof position === 'function' ? position(state.notePanelPosition) : position
        })),
      
      setPdfPanelPosition: (position: SetStateAction<PanelPosition>) =>
        set((state) => ({
          pdfPanelPosition: typeof position === 'function' ? position(state.pdfPanelPosition) : position
        })),
      
      setChatPanelPosition: (position: SetStateAction<PanelPosition>) =>
        set((state) => ({
          chatPanelPosition: typeof position === 'function' ? position(state.chatPanelPosition) : position
        })),
      
      setTagPanelPosition: (position: SetStateAction<PanelPosition>) =>
        set((state) => ({
          tagPanelPosition: typeof position === 'function' ? position(state.tagPanelPosition) : position
        })),
      
      resetPositionsToDefaults: () => set(getDefaultPositions()),
      
    }),
  shallow
);

// 连接持久化管理器和 store
projectPersist.setStoreState = usePanelOpenStore.setState;

// 监听 workspace store 的变化，同步项目ID
useWorkerSpaceStore.subscribe((state) => {
  projectPersist.updateProjectId(state.wid);
});

// 监听 panel store 的变化，自动保存状态
usePanelOpenStore.subscribe((state) => {
  // 自动保存状态变化（排除方法）
  const { 
    togglePdfPanel, toggleChatPanel, toggleNotePanel, toggleTagPanel, toggleCardListPanel, toggleLLMPanel,
    setNotePanelPosition, setPdfPanelPosition, setChatPanelPosition, setTagPanelPosition, resetPositionsToDefaults,
    ...stateToSave 
  } = state;
  
  projectPersist.saveState(stateToSave);
});

// 导出持久化管理器，供外部使用
export { projectPersist };
