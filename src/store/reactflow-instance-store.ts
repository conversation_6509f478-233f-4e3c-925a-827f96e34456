import { createWithEqualityFn } from "zustand/traditional";
import { ReactFlowInstance } from "@xyflow/react";

// ReactFlow 实例的简单类型定义
export type ReactFlowInstanceStore = {
  // ReactFlow 实例
  instance: ReactFlowInstance | null;
  
  // 设置 ReactFlow 实例
  setInstance: (instance: ReactFlowInstance) => void;
  
  // 清理 ReactFlow 实例
  clearInstance: () => void;
};

export const useReactFlowInstanceStore = createWithEqualityFn<ReactFlowInstanceStore>((set) => ({
  instance: null,
  
  setInstance: (instance) =>
    set(() => ({
      instance,
    })),
  
  clearInstance: () =>
    set(() => ({
      instance: null,
    })),
})); 