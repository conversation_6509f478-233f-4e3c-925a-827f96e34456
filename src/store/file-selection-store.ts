import { createWithEqualityFn } from "zustand/traditional";
import { shallow } from "zustand/shallow";

type FileSelectionType = {
  selectedFileId: string | null;
  selectedFileIds: string[];
  lastSelectedId: string | null;
  refreshTrigger: number;
  selectFile: (fileId: string) => void;
  selectMultiple: (fileIds: string[], isShiftSelect?: boolean, isCtrlSelect?: boolean) => void;
  toggleSelection: (fileId: string) => void;
  rangeSelect: (startId: string, endId: string, allFileIds: string[]) => void;
  clearSelection: () => void;
  isSelected: (fileId: string) => boolean;
  getSelectedIds: () => string[];
  triggerRefresh: () => void;
};

export const useFileSelectionStore = createWithEqualityFn<FileSelectionType>(
  (set, get) => ({
    selectedFileId: null,
    selectedFileIds: [],
    lastSelectedId: null,
    refreshTrigger: 0,
    
    selectFile: (fileId: string) => 
      set({ 
        selectedFileId: fileId, 
        selectedFileIds: [fileId], 
        lastSelectedId: fileId 
      }),
      
    selectMultiple: (fileIds: string[], isShiftSelect = false, isCtrlSelect = false) =>
      set((state) => {
        let newSelectedIds = [...state.selectedFileIds];
        
        if (isCtrlSelect) {
          // Ctrl+Click: 切换选择状态
          fileIds.forEach(id => {
            const index = newSelectedIds.indexOf(id);
            if (index > -1) {
              newSelectedIds.splice(index, 1);
            } else {
              newSelectedIds.push(id);
            }
          });
        } else if (isShiftSelect) {
          // Shift+Click: 范围选择，合并到现有选择
          fileIds.forEach(id => {
            if (!newSelectedIds.includes(id)) {
              newSelectedIds.push(id);
            }
          });
        } else {
          // 普通选择：替换当前选择
          newSelectedIds = [...fileIds];
        }
        
        const lastSelected = fileIds[fileIds.length - 1] || state.lastSelectedId;
        
        return {
          selectedFileIds: newSelectedIds,
          selectedFileId: newSelectedIds.length === 1 ? newSelectedIds[0] : null,
          lastSelectedId: lastSelected
        };
      }),
      
    toggleSelection: (fileId: string) =>
      set((state) => {
        const newSelectedIds = [...state.selectedFileIds];
        const index = newSelectedIds.indexOf(fileId);
        
        if (index > -1) {
          newSelectedIds.splice(index, 1);
        } else {
          newSelectedIds.push(fileId);
        }
        
        return {
          selectedFileIds: newSelectedIds,
          selectedFileId: newSelectedIds.length === 1 ? newSelectedIds[0] : null,
          lastSelectedId: newSelectedIds.length > 0 ? fileId : null
        };
      }),
      
    rangeSelect: (startId: string, endId: string, allFileIds: string[]) =>
      set((state) => {
        const startIndex = allFileIds.indexOf(startId);
        const endIndex = allFileIds.indexOf(endId);
        
        if (startIndex === -1 || endIndex === -1) return state;
        
        const minIndex = Math.min(startIndex, endIndex);
        const maxIndex = Math.max(startIndex, endIndex);
        
        const rangeIds = allFileIds.slice(minIndex, maxIndex + 1);
        const newSelectedIds = [...state.selectedFileIds];
        
        rangeIds.forEach(id => {
          if (!newSelectedIds.includes(id)) {
            newSelectedIds.push(id);
          }
        });
        
        return {
          selectedFileIds: newSelectedIds,
          selectedFileId: newSelectedIds.length === 1 ? newSelectedIds[0] : null,
          lastSelectedId: endId
        };
      }),
      
    clearSelection: () => 
      set({ 
        selectedFileId: null, 
        selectedFileIds: [], 
        lastSelectedId: null 
      }),
      
    isSelected: (fileId: string) => get().selectedFileIds.includes(fileId),
    
    getSelectedIds: () => get().selectedFileIds,
    
    triggerRefresh: () => set(state => ({ refreshTrigger: state.refreshTrigger + 1 }))
  }),
  shallow
);